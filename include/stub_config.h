#ifndef STUB_CONFIG_H
#define STUB_CONFIG_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Application configuration */
#define STUB_VERSION "1.0.0"
#define STUB_ALPN "hq-29"

/* Default server configuration */
#define DEFAULT_SERVER_HOST "127.0.0.1"
#define DEFAULT_SERVER_PORT 4433

/* Connection parameters */
#define MAX_RECONNECT_ATTEMPTS 10
#define RECONNECT_BASE_DELAY_MS 1000
#define RECONNECT_MAX_DELAY_MS 30000
#define CONNECTION_TIMEOUT_MS 60000
#define REGISTRATION_TIMEOUT_MS 60000
#define REGISTRATION_RETRY_DELAY_MS 3000

/* Buffer sizes */
#define MAX_PACKET_SIZE 1500
#define MAX_STREAM_BUFFER_SIZE 65536
#define MAX_UDP_BUFFER_SIZE 65535
#define MAX_DEVICE_ID_LENGTH 12
#define MAX_HOSTNAME_LENGTH 256

/* Protocol constants */
#define CMD_TCP_CONNECT 0x01
#define CMD_UDP_RELAY 0x02

#define ATYP_IPV4 0x01
#define ATYP_DOMAIN 0x03
#define ATYP_IPV6 0x04

/* Device registration protocol */
#define MSG_DEVICE_REGISTER_REQ 0x01
#define MSG_DEVICE_REGISTER_ACK 0x02

/* Registration status codes */
#define REG_STATUS_SUCCESS 0x00
#define REG_STATUS_FORMAT_ERROR 0x01
#define REG_STATUS_DEVICE_EXISTS 0x02
#define REG_STATUS_SERVER_ERROR 0xFF

/* Logging levels */
typedef enum {
    LOG_LEVEL_ERROR = 0,
    LOG_LEVEL_WARN = 1,
    LOG_LEVEL_INFO = 2,
    LOG_LEVEL_DEBUG = 3,
    LOG_LEVEL_TRACE = 4
} log_level_t;

#define DEFAULT_LOG_LEVEL LOG_LEVEL_INFO

/* Network interface detection */
#ifdef __linux__
#define NETWORK_INTERFACE_PATH "/sys/class/net"
#elif defined(__APPLE__)
#define NETWORK_INTERFACE_PATH "/dev"
#endif

/* Memory optimization for embedded systems */
#define MAX_CONCURRENT_STREAMS 100
#define MAX_CONCURRENT_UDP_SESSIONS 50
#define STREAM_POOL_SIZE 128
#define UDP_SESSION_POOL_SIZE 64

/* Error codes */
#define STUB_SUCCESS 0
#define STUB_ERROR_MEMORY -1
#define STUB_ERROR_NETWORK -2
#define STUB_ERROR_PROTOCOL -3
#define STUB_ERROR_TIMEOUT -4
#define STUB_ERROR_REGISTRATION -5
#define STUB_ERROR_CONNECTION -6

#ifdef __cplusplus
}
#endif

#endif /* STUB_CONFIG_H */
