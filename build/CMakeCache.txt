# This is the CMakeCache file.
# For build in directory: /Users/<USER>/Documents/augment-projects/stub/build
# It was generated by CMake: /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build picoquicdemo
BUILD_DEMO:BOOL=ON

//Build picohttp
BUILD_HTTP:BOOL=ON

//Build picoquic-log
BUILD_LOGLIB:BOOL=ON

//Build picolog_t the log reader
BUILD_LOGREADER:BOOL=ON

//Build the testing tree.
BUILD_TESTING:BOOL=OFF

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Release

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=-L/opt/homebrew/lib

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles/pkgRedirects

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=-L/opt/homebrew/lib

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=15.3

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=stub

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=********

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=33

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=2

//Path to a program.
CMAKE_RANLIB:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=-L/opt/homebrew/lib

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Disable Picoquic debug output
DISABLE_DEBUG_PRINTF:BOOL=ON

//Disable QLOG
DISABLE_QLOG:BOOL=ON

//Enable AddressSanitizer (ASAN) for debugging
ENABLE_ASAN:BOOL=OFF

//Enable UndefinedBehaviorSanitizer (UBSan) for debugging
ENABLE_UBSAN:BOOL=OFF

//Directory under which to collect all populated content
FETCHCONTENT_BASE_DIR:PATH=/Users/<USER>/Documents/augment-projects/stub/build/_deps

//Disables all attempts to download or update content and assumes
// source dirs already exist
FETCHCONTENT_FULLY_DISCONNECTED:BOOL=OFF

//Enables QUIET option for all content population
FETCHCONTENT_QUIET:BOOL=ON

//When not empty, overrides where to find pre-populated content
// for picotls
FETCHCONTENT_SOURCE_DIR_PICOTLS:PATH=

//Enables UPDATE_DISCONNECTED behavior for all content population
FETCHCONTENT_UPDATES_DISCONNECTED:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of picotls
FETCHCONTENT_UPDATES_DISCONNECTED_PICOTLS:BOOL=OFF

//Git command line client
GIT_EXECUTABLE:FILEPATH=/opt/homebrew/bin/git

//Location of header files
INCLUDE_INSTALL_DIR:PATH=/usr/local/include

//Path to a library.
LIBC_RESOLV_LIB:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libresolv.tbd

//Path of library files
LIB_PATH:PATH=/usr/local/lib/libpicoquic-core.a;/usr/local/lib/libpicotls-core.a;/usr/local/lib/libpicotls-fusion.a;/usr/local/lib/libpicotls-openssl.a;/usr/local/lib/libpicotls-minicrypto.a

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/opt/homebrew/Cellar/openssl@3/3.3.1/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.3.1/lib/libssl.dylib

//Fetch PicoTLS during configuration
PICOQUIC_FETCH_PTLS:BOOL=ON

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/opt/homebrew/bin/pkg-config

//build 'fusion' AES-GCM engine
PTLS_WITH_FUSION:BOOL=OFF

//enable AEGIS (requires libaegis)
WITH_AEGIS:BOOL=OFF

//use USDT (userspace Dtrace probes)
WITH_DTRACE:BOOL=ON

//build 'fusion' AES-GCM engine
WITH_FUSION:BOOL=OFF

//enable MBEDTLS
WITH_MBEDTLS:BOOL=OFF

//build with OpenSSL
WITH_OPENSSL:BOOL=ON

//Value Computed by CMake
picoquic_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic

//Build Tests for picoquic
picoquic_BUILD_TESTS:BOOL=OFF

//Value Computed by CMake
picoquic_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
picoquic_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic

//Dependencies for the target
picotls-core_LIB_DEPENDS:STATIC=general;brotlidec;general;brotlienc;

//Dependencies for the target
picotls-minicrypto_LIB_DEPENDS:STATIC=general;picotls-core;

//Dependencies for the target
picotls-openssl_LIB_DEPENDS:STATIC=general;/opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib;general;picotls-core;

//Value Computed by CMake
picotls_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-build

//Value Computed by CMake
picotls_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
picotls_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src

//Path to a library.
pkgcfg_lib_BROTLI_DEC_brotlidec:FILEPATH=/opt/homebrew/Cellar/brotli/1.1.0/lib/libbrotlidec.dylib

//Path to a library.
pkgcfg_lib_BROTLI_ENC_brotlienc:FILEPATH=/opt/homebrew/Cellar/brotli/1.1.0/lib/libbrotlienc.dylib

//Path to a library.
pkgcfg_lib_OPENSSL_crypto:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib

//Path to a library.
pkgcfg_lib_OPENSSL_ssl:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.3.1/lib/libssl.dylib

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.3.1/lib/libssl.dylib

//Value Computed by CMake
stub_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/stub/build

//Value Computed by CMake
stub_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
stub_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/stub


########################
# INTERNAL cache entries
########################

BROTLI_DEC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_DEC_CFLAGS_I:INTERNAL=
BROTLI_DEC_CFLAGS_OTHER:INTERNAL=
BROTLI_DEC_FOUND:INTERNAL=1
BROTLI_DEC_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_DEC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_DEC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlidec
BROTLI_DEC_LDFLAGS_OTHER:INTERNAL=
BROTLI_DEC_LIBDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
BROTLI_DEC_LIBRARIES:INTERNAL=brotlidec
BROTLI_DEC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
BROTLI_DEC_LIBS:INTERNAL=
BROTLI_DEC_LIBS_L:INTERNAL=
BROTLI_DEC_LIBS_OTHER:INTERNAL=
BROTLI_DEC_LIBS_PATHS:INTERNAL=
BROTLI_DEC_MODULE_NAME:INTERNAL=libbrotlidec
BROTLI_DEC_PREFIX:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0
BROTLI_DEC_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_DEC_STATIC_CFLAGS_I:INTERNAL=
BROTLI_DEC_STATIC_CFLAGS_OTHER:INTERNAL=
BROTLI_DEC_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_DEC_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlidec;-lbrotlicommon
BROTLI_DEC_STATIC_LDFLAGS_OTHER:INTERNAL=
BROTLI_DEC_STATIC_LIBDIR:INTERNAL=
BROTLI_DEC_STATIC_LIBRARIES:INTERNAL=brotlidec;brotlicommon
BROTLI_DEC_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
BROTLI_DEC_STATIC_LIBS:INTERNAL=
BROTLI_DEC_STATIC_LIBS_L:INTERNAL=
BROTLI_DEC_STATIC_LIBS_OTHER:INTERNAL=
BROTLI_DEC_STATIC_LIBS_PATHS:INTERNAL=
BROTLI_DEC_VERSION:INTERNAL=1.1.0
BROTLI_DEC_libbrotlidec_INCLUDEDIR:INTERNAL=
BROTLI_DEC_libbrotlidec_LIBDIR:INTERNAL=
BROTLI_DEC_libbrotlidec_PREFIX:INTERNAL=
BROTLI_DEC_libbrotlidec_VERSION:INTERNAL=
BROTLI_ENC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_ENC_CFLAGS_I:INTERNAL=
BROTLI_ENC_CFLAGS_OTHER:INTERNAL=
BROTLI_ENC_FOUND:INTERNAL=1
BROTLI_ENC_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_ENC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_ENC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlienc
BROTLI_ENC_LDFLAGS_OTHER:INTERNAL=
BROTLI_ENC_LIBDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
BROTLI_ENC_LIBRARIES:INTERNAL=brotlienc
BROTLI_ENC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
BROTLI_ENC_LIBS:INTERNAL=
BROTLI_ENC_LIBS_L:INTERNAL=
BROTLI_ENC_LIBS_OTHER:INTERNAL=
BROTLI_ENC_LIBS_PATHS:INTERNAL=
BROTLI_ENC_MODULE_NAME:INTERNAL=libbrotlienc
BROTLI_ENC_PREFIX:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0
BROTLI_ENC_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_ENC_STATIC_CFLAGS_I:INTERNAL=
BROTLI_ENC_STATIC_CFLAGS_OTHER:INTERNAL=
BROTLI_ENC_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
BROTLI_ENC_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlienc;-lbrotlicommon
BROTLI_ENC_STATIC_LDFLAGS_OTHER:INTERNAL=
BROTLI_ENC_STATIC_LIBDIR:INTERNAL=
BROTLI_ENC_STATIC_LIBRARIES:INTERNAL=brotlienc;brotlicommon
BROTLI_ENC_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
BROTLI_ENC_STATIC_LIBS:INTERNAL=
BROTLI_ENC_STATIC_LIBS_L:INTERNAL=
BROTLI_ENC_STATIC_LIBS_OTHER:INTERNAL=
BROTLI_ENC_STATIC_LIBS_PATHS:INTERNAL=
BROTLI_ENC_VERSION:INTERNAL=1.1.0
BROTLI_ENC_libbrotlienc_INCLUDEDIR:INTERNAL=
BROTLI_ENC_libbrotlienc_LIBDIR:INTERNAL=
BROTLI_ENC_libbrotlienc_PREFIX:INTERNAL=
BROTLI_ENC_libbrotlienc_VERSION:INTERNAL=
//Test CC_HAS_AESNI256
CC_HAS_AESNI256:INTERNAL=
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/Documents/augment-projects/stub/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=26
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/homebrew/Cellar/cmake/3.26.3/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/homebrew/Cellar/cmake/3.26.3/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/homebrew/Cellar/cmake/3.26.3/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/Documents/augment-projects/stub
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=3
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/homebrew/Cellar/cmake/3.26.3/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib][/opt/homebrew/Cellar/openssl@3/3.3.1/include][c ][v3.3.1()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/opt/homebrew/bin/pkg-config][v0.29.2()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
OPENSSL_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/openssl@3/3.3.1/include
OPENSSL_CFLAGS_I:INTERNAL=
OPENSSL_CFLAGS_OTHER:INTERNAL=
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
OPENSSL_FOUND:INTERNAL=1
OPENSSL_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/include
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
OPENSSL_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/include
OPENSSL_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/openssl@3/3.3.1/lib;-lssl;-lcrypto
OPENSSL_LDFLAGS_OTHER:INTERNAL=
OPENSSL_LIBDIR:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/lib
OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
OPENSSL_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/lib
OPENSSL_LIBS:INTERNAL=
OPENSSL_LIBS_L:INTERNAL=
OPENSSL_LIBS_OTHER:INTERNAL=
OPENSSL_LIBS_PATHS:INTERNAL=
OPENSSL_MODULE_NAME:INTERNAL=openssl
OPENSSL_PREFIX:INTERNAL=
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
OPENSSL_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/openssl@3/3.3.1/include
OPENSSL_STATIC_CFLAGS_I:INTERNAL=
OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/include
OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/openssl@3/3.3.1/lib;-lssl;-lcrypto
OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=
OPENSSL_STATIC_LIBDIR:INTERNAL=
OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto
OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/lib
OPENSSL_STATIC_LIBS:INTERNAL=
OPENSSL_STATIC_LIBS_L:INTERNAL=
OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
OPENSSL_VERSION:INTERNAL=3.3.1
OPENSSL_openssl_INCLUDEDIR:INTERNAL=
OPENSSL_openssl_LIBDIR:INTERNAL=
OPENSSL_openssl_PREFIX:INTERNAL=
OPENSSL_openssl_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
_OPENSSL_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/openssl@3/3.3.1/include
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/include
_OPENSSL_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/openssl@3/3.3.1/lib;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/lib
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/lib
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=
_OPENSSL_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/openssl@3/3.3.1/include
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/include
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/openssl@3/3.3.1/lib;-lssl;-lcrypto
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/lib
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.3.1
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
__pkg_config_arguments_BROTLI_DEC:INTERNAL=libbrotlidec
__pkg_config_arguments_BROTLI_ENC:INTERNAL=libbrotlienc
__pkg_config_arguments_OPENSSL:INTERNAL=openssl
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked_BROTLI_DEC:INTERNAL=1
__pkg_config_checked_BROTLI_ENC:INTERNAL=1
__pkg_config_checked_OPENSSL:INTERNAL=1
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_BROTLI_DEC_brotlidec
pkgcfg_lib_BROTLI_DEC_brotlidec-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_BROTLI_ENC_brotlienc
pkgcfg_lib_BROTLI_ENC_brotlienc-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_OPENSSL_crypto
pkgcfg_lib_OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_OPENSSL_ssl
pkgcfg_lib_OPENSSL_ssl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.3.1/lib

