# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

# Include any dependencies generated for this target.
include CMakeFiles/stub.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/stub.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/stub.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/stub.dir/flags.make

CMakeFiles/stub.dir/src/main.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/main.c.o: /Users/<USER>/Documents/augment-projects/stub/src/main.c
CMakeFiles/stub.dir/src/main.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/stub.dir/src/main.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/main.c.o -MF CMakeFiles/stub.dir/src/main.c.o.d -o CMakeFiles/stub.dir/src/main.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/main.c

CMakeFiles/stub.dir/src/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/main.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/main.c > CMakeFiles/stub.dir/src/main.c.i

CMakeFiles/stub.dir/src/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/main.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/main.c -o CMakeFiles/stub.dir/src/main.c.s

CMakeFiles/stub.dir/src/client/quic_client.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/client/quic_client.c.o: /Users/<USER>/Documents/augment-projects/stub/src/client/quic_client.c
CMakeFiles/stub.dir/src/client/quic_client.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/stub.dir/src/client/quic_client.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/client/quic_client.c.o -MF CMakeFiles/stub.dir/src/client/quic_client.c.o.d -o CMakeFiles/stub.dir/src/client/quic_client.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/client/quic_client.c

CMakeFiles/stub.dir/src/client/quic_client.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/client/quic_client.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/client/quic_client.c > CMakeFiles/stub.dir/src/client/quic_client.c.i

CMakeFiles/stub.dir/src/client/quic_client.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/client/quic_client.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/client/quic_client.c -o CMakeFiles/stub.dir/src/client/quic_client.c.s

CMakeFiles/stub.dir/src/device/device_id.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/device/device_id.c.o: /Users/<USER>/Documents/augment-projects/stub/src/device/device_id.c
CMakeFiles/stub.dir/src/device/device_id.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/stub.dir/src/device/device_id.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/device/device_id.c.o -MF CMakeFiles/stub.dir/src/device/device_id.c.o.d -o CMakeFiles/stub.dir/src/device/device_id.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/device/device_id.c

CMakeFiles/stub.dir/src/device/device_id.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/device/device_id.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/device/device_id.c > CMakeFiles/stub.dir/src/device/device_id.c.i

CMakeFiles/stub.dir/src/device/device_id.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/device/device_id.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/device/device_id.c -o CMakeFiles/stub.dir/src/device/device_id.c.s

CMakeFiles/stub.dir/src/device/device_register.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/device/device_register.c.o: /Users/<USER>/Documents/augment-projects/stub/src/device/device_register.c
CMakeFiles/stub.dir/src/device/device_register.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/stub.dir/src/device/device_register.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/device/device_register.c.o -MF CMakeFiles/stub.dir/src/device/device_register.c.o.d -o CMakeFiles/stub.dir/src/device/device_register.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/device/device_register.c

CMakeFiles/stub.dir/src/device/device_register.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/device/device_register.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/device/device_register.c > CMakeFiles/stub.dir/src/device/device_register.c.i

CMakeFiles/stub.dir/src/device/device_register.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/device/device_register.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/device/device_register.c -o CMakeFiles/stub.dir/src/device/device_register.c.s

CMakeFiles/stub.dir/src/protocol/protocol.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/protocol/protocol.c.o: /Users/<USER>/Documents/augment-projects/stub/src/protocol/protocol.c
CMakeFiles/stub.dir/src/protocol/protocol.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/stub.dir/src/protocol/protocol.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/protocol/protocol.c.o -MF CMakeFiles/stub.dir/src/protocol/protocol.c.o.d -o CMakeFiles/stub.dir/src/protocol/protocol.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/protocol/protocol.c

CMakeFiles/stub.dir/src/protocol/protocol.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/protocol/protocol.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/protocol/protocol.c > CMakeFiles/stub.dir/src/protocol/protocol.c.i

CMakeFiles/stub.dir/src/protocol/protocol.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/protocol/protocol.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/protocol/protocol.c -o CMakeFiles/stub.dir/src/protocol/protocol.c.s

CMakeFiles/stub.dir/src/handler/stream_handler.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/handler/stream_handler.c.o: /Users/<USER>/Documents/augment-projects/stub/src/handler/stream_handler.c
CMakeFiles/stub.dir/src/handler/stream_handler.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/stub.dir/src/handler/stream_handler.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/handler/stream_handler.c.o -MF CMakeFiles/stub.dir/src/handler/stream_handler.c.o.d -o CMakeFiles/stub.dir/src/handler/stream_handler.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/handler/stream_handler.c

CMakeFiles/stub.dir/src/handler/stream_handler.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/handler/stream_handler.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/handler/stream_handler.c > CMakeFiles/stub.dir/src/handler/stream_handler.c.i

CMakeFiles/stub.dir/src/handler/stream_handler.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/handler/stream_handler.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/handler/stream_handler.c -o CMakeFiles/stub.dir/src/handler/stream_handler.c.s

CMakeFiles/stub.dir/src/handler/tcp_handler.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/handler/tcp_handler.c.o: /Users/<USER>/Documents/augment-projects/stub/src/handler/tcp_handler.c
CMakeFiles/stub.dir/src/handler/tcp_handler.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/stub.dir/src/handler/tcp_handler.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/handler/tcp_handler.c.o -MF CMakeFiles/stub.dir/src/handler/tcp_handler.c.o.d -o CMakeFiles/stub.dir/src/handler/tcp_handler.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/handler/tcp_handler.c

CMakeFiles/stub.dir/src/handler/tcp_handler.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/handler/tcp_handler.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/handler/tcp_handler.c > CMakeFiles/stub.dir/src/handler/tcp_handler.c.i

CMakeFiles/stub.dir/src/handler/tcp_handler.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/handler/tcp_handler.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/handler/tcp_handler.c -o CMakeFiles/stub.dir/src/handler/tcp_handler.c.s

CMakeFiles/stub.dir/src/handler/udp_handler.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/handler/udp_handler.c.o: /Users/<USER>/Documents/augment-projects/stub/src/handler/udp_handler.c
CMakeFiles/stub.dir/src/handler/udp_handler.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/stub.dir/src/handler/udp_handler.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/handler/udp_handler.c.o -MF CMakeFiles/stub.dir/src/handler/udp_handler.c.o.d -o CMakeFiles/stub.dir/src/handler/udp_handler.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/handler/udp_handler.c

CMakeFiles/stub.dir/src/handler/udp_handler.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/handler/udp_handler.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/handler/udp_handler.c > CMakeFiles/stub.dir/src/handler/udp_handler.c.i

CMakeFiles/stub.dir/src/handler/udp_handler.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/handler/udp_handler.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/handler/udp_handler.c -o CMakeFiles/stub.dir/src/handler/udp_handler.c.s

CMakeFiles/stub.dir/src/utils/logger.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/utils/logger.c.o: /Users/<USER>/Documents/augment-projects/stub/src/utils/logger.c
CMakeFiles/stub.dir/src/utils/logger.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/stub.dir/src/utils/logger.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/utils/logger.c.o -MF CMakeFiles/stub.dir/src/utils/logger.c.o.d -o CMakeFiles/stub.dir/src/utils/logger.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/utils/logger.c

CMakeFiles/stub.dir/src/utils/logger.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/utils/logger.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/utils/logger.c > CMakeFiles/stub.dir/src/utils/logger.c.i

CMakeFiles/stub.dir/src/utils/logger.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/utils/logger.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/utils/logger.c -o CMakeFiles/stub.dir/src/utils/logger.c.s

CMakeFiles/stub.dir/src/utils/network_utils.c.o: CMakeFiles/stub.dir/flags.make
CMakeFiles/stub.dir/src/utils/network_utils.c.o: /Users/<USER>/Documents/augment-projects/stub/src/utils/network_utils.c
CMakeFiles/stub.dir/src/utils/network_utils.c.o: CMakeFiles/stub.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/stub.dir/src/utils/network_utils.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stub.dir/src/utils/network_utils.c.o -MF CMakeFiles/stub.dir/src/utils/network_utils.c.o.d -o CMakeFiles/stub.dir/src/utils/network_utils.c.o -c /Users/<USER>/Documents/augment-projects/stub/src/utils/network_utils.c

CMakeFiles/stub.dir/src/utils/network_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/stub.dir/src/utils/network_utils.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/src/utils/network_utils.c > CMakeFiles/stub.dir/src/utils/network_utils.c.i

CMakeFiles/stub.dir/src/utils/network_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/stub.dir/src/utils/network_utils.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/src/utils/network_utils.c -o CMakeFiles/stub.dir/src/utils/network_utils.c.s

# Object files for target stub
stub_OBJECTS = \
"CMakeFiles/stub.dir/src/main.c.o" \
"CMakeFiles/stub.dir/src/client/quic_client.c.o" \
"CMakeFiles/stub.dir/src/device/device_id.c.o" \
"CMakeFiles/stub.dir/src/device/device_register.c.o" \
"CMakeFiles/stub.dir/src/protocol/protocol.c.o" \
"CMakeFiles/stub.dir/src/handler/stream_handler.c.o" \
"CMakeFiles/stub.dir/src/handler/tcp_handler.c.o" \
"CMakeFiles/stub.dir/src/handler/udp_handler.c.o" \
"CMakeFiles/stub.dir/src/utils/logger.c.o" \
"CMakeFiles/stub.dir/src/utils/network_utils.c.o"

# External object files for target stub
stub_EXTERNAL_OBJECTS =

stub: CMakeFiles/stub.dir/src/main.c.o
stub: CMakeFiles/stub.dir/src/client/quic_client.c.o
stub: CMakeFiles/stub.dir/src/device/device_id.c.o
stub: CMakeFiles/stub.dir/src/device/device_register.c.o
stub: CMakeFiles/stub.dir/src/protocol/protocol.c.o
stub: CMakeFiles/stub.dir/src/handler/stream_handler.c.o
stub: CMakeFiles/stub.dir/src/handler/tcp_handler.c.o
stub: CMakeFiles/stub.dir/src/handler/udp_handler.c.o
stub: CMakeFiles/stub.dir/src/utils/logger.c.o
stub: CMakeFiles/stub.dir/src/utils/network_utils.c.o
stub: CMakeFiles/stub.dir/build.make
stub: third_party/picoquic_minimal/libpicoquic-minimal.a
stub: CMakeFiles/stub.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking C executable stub"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/stub.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/stub.dir/build: stub
.PHONY : CMakeFiles/stub.dir/build

CMakeFiles/stub.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/stub.dir/cmake_clean.cmake
.PHONY : CMakeFiles/stub.dir/clean

CMakeFiles/stub.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/stub /Users/<USER>/Documents/augment-projects/stub /Users/<USER>/Documents/augment-projects/stub/build /Users/<USER>/Documents/augment-projects/stub/build /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles/stub.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/stub.dir/depend

