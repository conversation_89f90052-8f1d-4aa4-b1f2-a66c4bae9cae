/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc  -Wall -Wextra -Os -ffunction-sections -fdata-sections -flto -O3 -DNDEBUG -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -Wl,-dead_strip CMakeFiles/stub.dir/src/main.c.o CMakeFiles/stub.dir/src/client/quic_client.c.o CMakeFiles/stub.dir/src/device/device_id.c.o CMakeFiles/stub.dir/src/device/device_register.c.o CMakeFiles/stub.dir/src/protocol/protocol.c.o CMakeFiles/stub.dir/src/handler/stream_handler.c.o CMakeFiles/stub.dir/src/handler/tcp_handler.c.o CMakeFiles/stub.dir/src/handler/udp_handler.c.o CMakeFiles/stub.dir/src/utils/logger.c.o CMakeFiles/stub.dir/src/utils/network_utils.c.o -o stub  third_party/picoquic_minimal/libpicoquic-minimal.a 
