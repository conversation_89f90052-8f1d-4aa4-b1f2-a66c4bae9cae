
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/stub/src/client/quic_client.c" "CMakeFiles/stub.dir/src/client/quic_client.c.o" "gcc" "CMakeFiles/stub.dir/src/client/quic_client.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/src/device/device_id.c" "CMakeFiles/stub.dir/src/device/device_id.c.o" "gcc" "CMakeFiles/stub.dir/src/device/device_id.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/src/device/device_register.c" "CMakeFiles/stub.dir/src/device/device_register.c.o" "gcc" "CMakeFiles/stub.dir/src/device/device_register.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/src/handler/stream_handler.c" "CMakeFiles/stub.dir/src/handler/stream_handler.c.o" "gcc" "CMakeFiles/stub.dir/src/handler/stream_handler.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/src/handler/tcp_handler.c" "CMakeFiles/stub.dir/src/handler/tcp_handler.c.o" "gcc" "CMakeFiles/stub.dir/src/handler/tcp_handler.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/src/handler/udp_handler.c" "CMakeFiles/stub.dir/src/handler/udp_handler.c.o" "gcc" "CMakeFiles/stub.dir/src/handler/udp_handler.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/src/main.c" "CMakeFiles/stub.dir/src/main.c.o" "gcc" "CMakeFiles/stub.dir/src/main.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/src/protocol/protocol.c" "CMakeFiles/stub.dir/src/protocol/protocol.c.o" "gcc" "CMakeFiles/stub.dir/src/protocol/protocol.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/src/utils/logger.c" "CMakeFiles/stub.dir/src/utils/logger.c.o" "gcc" "CMakeFiles/stub.dir/src/utils/logger.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/src/utils/network_utils.c" "CMakeFiles/stub.dir/src/utils/network_utils.c.o" "gcc" "CMakeFiles/stub.dir/src/utils/network_utils.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
