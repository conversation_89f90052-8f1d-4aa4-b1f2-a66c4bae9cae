# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/Users/<USER>/Documents/augment-projects/stub/CMakeLists.txt"
  "CMakeFiles/3.26.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.26.3/CMakeSystem.cmake"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/CMakeLists.txt"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeCCompiler.cmake.in"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeCCompilerABI.c"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeCInformation.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeCompilerIdDetection.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeDetermineCCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeDetermineCompileFeatures.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeDetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeDetermineCompilerId.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeDetermineSystem.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeFindBinUtils.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeSystem.cmake.in"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeTestCCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeTestCompilerCommon.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeUnixFindMake.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CheckCSourceCompiles.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CheckIncludeFile.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CheckLibraryExists.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/AppleClang-C.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Clang.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/GNU.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/FindPackageMessage.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/FindPkgConfig.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/FindThreads.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Internal/FeatureTesting.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Apple-Clang-C.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Apple-Clang.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Darwin-Initialize.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Darwin.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.26.3/CMakeSystem.cmake"
  "CMakeFiles/3.26.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.26.3/CMakeCCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "third_party/picoquic_minimal/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/stub.dir/DependInfo.cmake"
  "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/DependInfo.cmake"
  )
