# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/Users/<USER>/Documents/augment-projects/stub/CMakeLists.txt"
  "CMakeFiles/3.26.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.26.3/CMakeSystem.cmake"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/CMakeLists.txt"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeCInformation.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CheckCSourceCompiles.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CheckIncludeFile.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/CheckLibraryExists.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/AppleClang-C.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/Clang.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Compiler/GNU.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/FindPackageMessage.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/FindThreads.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Apple-Clang-C.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Apple-Clang.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Darwin-Initialize.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/Darwin.cmake"
  "/opt/homebrew/Cellar/cmake/3.26.3/share/cmake/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "third_party/picoquic_minimal/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/stub.dir/DependInfo.cmake"
  "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/DependInfo.cmake"
  )
