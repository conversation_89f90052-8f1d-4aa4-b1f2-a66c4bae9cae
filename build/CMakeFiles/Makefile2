# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/stub.dir/all
all: third_party/picoquic_minimal/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: third_party/picoquic_minimal/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/stub.dir/clean
clean: third_party/picoquic_minimal/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory third_party/picoquic_minimal

# Recursive "all" directory target.
third_party/picoquic_minimal/all: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/all
.PHONY : third_party/picoquic_minimal/all

# Recursive "preinstall" directory target.
third_party/picoquic_minimal/preinstall:
.PHONY : third_party/picoquic_minimal/preinstall

# Recursive "clean" directory target.
third_party/picoquic_minimal/clean: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/clean
.PHONY : third_party/picoquic_minimal/clean

#=============================================================================
# Target rules for target CMakeFiles/stub.dir

# All Build rule for target.
CMakeFiles/stub.dir/all: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=26,27,28,29,30,31,32,33,34,35,36 "Built target stub"
.PHONY : CMakeFiles/stub.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/stub.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 36
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/stub.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : CMakeFiles/stub.dir/rule

# Convenience name for target.
stub: CMakeFiles/stub.dir/rule
.PHONY : stub

# clean rule for target.
CMakeFiles/stub.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/clean
.PHONY : CMakeFiles/stub.dir/clean

#=============================================================================
# Target rules for target third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir

# All Build rule for target.
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/all:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/depend
	$(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25 "Built target picoquic-minimal"
.PHONY : third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/all

# Build rule for subdir invocation for target.
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/rule

# Convenience name for target.
picoquic-minimal: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/rule
.PHONY : picoquic-minimal

# clean rule for target.
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/clean:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/clean
.PHONY : third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

