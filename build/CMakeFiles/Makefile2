# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/stub.dir/all
all: third_party/picoquic/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: third_party/picoquic/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/stub.dir/clean
clean: third_party/picoquic/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory _deps/picotls-build

# Recursive "all" directory target.
_deps/picotls-build/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
_deps/picotls-build/all: _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all
_deps/picotls-build/all: _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/all
_deps/picotls-build/all: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all
_deps/picotls-build/all: _deps/picotls-build/CMakeFiles/cli.dir/all
_deps/picotls-build/all: _deps/picotls-build/CMakeFiles/test-openssl.t.dir/all
.PHONY : _deps/picotls-build/all

# Recursive "preinstall" directory target.
_deps/picotls-build/preinstall:
.PHONY : _deps/picotls-build/preinstall

# Recursive "clean" directory target.
_deps/picotls-build/clean: _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/clean
_deps/picotls-build/clean: _deps/picotls-build/CMakeFiles/picotls-core.dir/clean
_deps/picotls-build/clean: _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/clean
_deps/picotls-build/clean: _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/clean
_deps/picotls-build/clean: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/clean
_deps/picotls-build/clean: _deps/picotls-build/CMakeFiles/cli.dir/clean
_deps/picotls-build/clean: _deps/picotls-build/CMakeFiles/test-openssl.t.dir/clean
_deps/picotls-build/clean: _deps/picotls-build/CMakeFiles/ptlsbench.dir/clean
_deps/picotls-build/clean: _deps/picotls-build/CMakeFiles/check.dir/clean
.PHONY : _deps/picotls-build/clean

#=============================================================================
# Directory level rules for directory third_party/picoquic

# Recursive "all" directory target.
third_party/picoquic/all: third_party/picoquic/CMakeFiles/picoquic-core.dir/all
third_party/picoquic/all: third_party/picoquic/CMakeFiles/picoquic-log.dir/all
third_party/picoquic/all: third_party/picoquic/CMakeFiles/picohttp-core.dir/all
third_party/picoquic/all: third_party/picoquic/CMakeFiles/picoquicdemo.dir/all
third_party/picoquic/all: third_party/picoquic/CMakeFiles/picolog_t.dir/all
third_party/picoquic/all: _deps/picotls-build/all
.PHONY : third_party/picoquic/all

# Recursive "preinstall" directory target.
third_party/picoquic/preinstall: _deps/picotls-build/preinstall
.PHONY : third_party/picoquic/preinstall

# Recursive "clean" directory target.
third_party/picoquic/clean: third_party/picoquic/CMakeFiles/picoquic-core.dir/clean
third_party/picoquic/clean: third_party/picoquic/CMakeFiles/picoquic-log.dir/clean
third_party/picoquic/clean: third_party/picoquic/CMakeFiles/picohttp-core.dir/clean
third_party/picoquic/clean: third_party/picoquic/CMakeFiles/picoquicdemo.dir/clean
third_party/picoquic/clean: third_party/picoquic/CMakeFiles/picolog_t.dir/clean
third_party/picoquic/clean: third_party/picoquic/CMakeFiles/clangformat.dir/clean
third_party/picoquic/clean: _deps/picotls-build/clean
.PHONY : third_party/picoquic/clean

#=============================================================================
# Target rules for target CMakeFiles/stub.dir

# All Build rule for target.
CMakeFiles/stub.dir/all: third_party/picoquic/CMakeFiles/picoquic-core.dir/all
CMakeFiles/stub.dir/all: third_party/picoquic/CMakeFiles/picoquic-log.dir/all
CMakeFiles/stub.dir/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
CMakeFiles/stub.dir/all: _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all
CMakeFiles/stub.dir/all: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=62,63,64,65,66,67 "Built target stub"
.PHONY : CMakeFiles/stub.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/stub.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 54
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/stub.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : CMakeFiles/stub.dir/rule

# Convenience name for target.
stub: CMakeFiles/stub.dir/rule
.PHONY : stub

# clean rule for target.
CMakeFiles/stub.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/clean
.PHONY : CMakeFiles/stub.dir/clean

#=============================================================================
# Target rules for target third_party/picoquic/CMakeFiles/picoquic-core.dir

# All Build rule for target.
third_party/picoquic/CMakeFiles/picoquic-core.dir/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
third_party/picoquic/CMakeFiles/picoquic-core.dir/all: _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all
third_party/picoquic/CMakeFiles/picoquic-core.dir/all: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/depend
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34 "Built target picoquic-core"
.PHONY : third_party/picoquic/CMakeFiles/picoquic-core.dir/all

# Build rule for subdir invocation for target.
third_party/picoquic/CMakeFiles/picoquic-core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 43
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picoquic-core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : third_party/picoquic/CMakeFiles/picoquic-core.dir/rule

# Convenience name for target.
picoquic-core: third_party/picoquic/CMakeFiles/picoquic-core.dir/rule
.PHONY : picoquic-core

# clean rule for target.
third_party/picoquic/CMakeFiles/picoquic-core.dir/clean:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/clean
.PHONY : third_party/picoquic/CMakeFiles/picoquic-core.dir/clean

#=============================================================================
# Target rules for target third_party/picoquic/CMakeFiles/picoquic-log.dir

# All Build rule for target.
third_party/picoquic/CMakeFiles/picoquic-log.dir/all:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/depend
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=35,36,37,38,39 "Built target picoquic-log"
.PHONY : third_party/picoquic/CMakeFiles/picoquic-log.dir/all

# Build rule for subdir invocation for target.
third_party/picoquic/CMakeFiles/picoquic-log.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picoquic-log.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : third_party/picoquic/CMakeFiles/picoquic-log.dir/rule

# Convenience name for target.
picoquic-log: third_party/picoquic/CMakeFiles/picoquic-log.dir/rule
.PHONY : picoquic-log

# clean rule for target.
third_party/picoquic/CMakeFiles/picoquic-log.dir/clean:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/clean
.PHONY : third_party/picoquic/CMakeFiles/picoquic-log.dir/clean

#=============================================================================
# Target rules for target third_party/picoquic/CMakeFiles/picohttp-core.dir

# All Build rule for target.
third_party/picoquic/CMakeFiles/picohttp-core.dir/all: third_party/picoquic/CMakeFiles/picoquic-core.dir/all
third_party/picoquic/CMakeFiles/picohttp-core.dir/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
third_party/picoquic/CMakeFiles/picohttp-core.dir/all: _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all
third_party/picoquic/CMakeFiles/picohttp-core.dir/all: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/depend
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=3,4,5,6,7,8 "Built target picohttp-core"
.PHONY : third_party/picoquic/CMakeFiles/picohttp-core.dir/all

# Build rule for subdir invocation for target.
third_party/picoquic/CMakeFiles/picohttp-core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 49
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picohttp-core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : third_party/picoquic/CMakeFiles/picohttp-core.dir/rule

# Convenience name for target.
picohttp-core: third_party/picoquic/CMakeFiles/picohttp-core.dir/rule
.PHONY : picohttp-core

# clean rule for target.
third_party/picoquic/CMakeFiles/picohttp-core.dir/clean:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/clean
.PHONY : third_party/picoquic/CMakeFiles/picohttp-core.dir/clean

#=============================================================================
# Target rules for target third_party/picoquic/CMakeFiles/picoquicdemo.dir

# All Build rule for target.
third_party/picoquic/CMakeFiles/picoquicdemo.dir/all: third_party/picoquic/CMakeFiles/picoquic-core.dir/all
third_party/picoquic/CMakeFiles/picoquicdemo.dir/all: third_party/picoquic/CMakeFiles/picoquic-log.dir/all
third_party/picoquic/CMakeFiles/picoquicdemo.dir/all: third_party/picoquic/CMakeFiles/picohttp-core.dir/all
third_party/picoquic/CMakeFiles/picoquicdemo.dir/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
third_party/picoquic/CMakeFiles/picoquicdemo.dir/all: _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all
third_party/picoquic/CMakeFiles/picoquicdemo.dir/all: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/depend
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=40,41 "Built target picoquicdemo"
.PHONY : third_party/picoquic/CMakeFiles/picoquicdemo.dir/all

# Build rule for subdir invocation for target.
third_party/picoquic/CMakeFiles/picoquicdemo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 56
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picoquicdemo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : third_party/picoquic/CMakeFiles/picoquicdemo.dir/rule

# Convenience name for target.
picoquicdemo: third_party/picoquic/CMakeFiles/picoquicdemo.dir/rule
.PHONY : picoquicdemo

# clean rule for target.
third_party/picoquic/CMakeFiles/picoquicdemo.dir/clean:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/clean
.PHONY : third_party/picoquic/CMakeFiles/picoquicdemo.dir/clean

#=============================================================================
# Target rules for target third_party/picoquic/CMakeFiles/picolog_t.dir

# All Build rule for target.
third_party/picoquic/CMakeFiles/picolog_t.dir/all: third_party/picoquic/CMakeFiles/picoquic-core.dir/all
third_party/picoquic/CMakeFiles/picolog_t.dir/all: third_party/picoquic/CMakeFiles/picoquic-log.dir/all
third_party/picoquic/CMakeFiles/picolog_t.dir/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
third_party/picoquic/CMakeFiles/picolog_t.dir/all: _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all
third_party/picoquic/CMakeFiles/picolog_t.dir/all: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picolog_t.dir/build.make third_party/picoquic/CMakeFiles/picolog_t.dir/depend
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picolog_t.dir/build.make third_party/picoquic/CMakeFiles/picolog_t.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=9 "Built target picolog_t"
.PHONY : third_party/picoquic/CMakeFiles/picolog_t.dir/all

# Build rule for subdir invocation for target.
third_party/picoquic/CMakeFiles/picolog_t.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 49
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picolog_t.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : third_party/picoquic/CMakeFiles/picolog_t.dir/rule

# Convenience name for target.
picolog_t: third_party/picoquic/CMakeFiles/picolog_t.dir/rule
.PHONY : picolog_t

# clean rule for target.
third_party/picoquic/CMakeFiles/picolog_t.dir/clean:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picolog_t.dir/build.make third_party/picoquic/CMakeFiles/picolog_t.dir/clean
.PHONY : third_party/picoquic/CMakeFiles/picolog_t.dir/clean

#=============================================================================
# Target rules for target third_party/picoquic/CMakeFiles/clangformat.dir

# All Build rule for target.
third_party/picoquic/CMakeFiles/clangformat.dir/all:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/clangformat.dir/build.make third_party/picoquic/CMakeFiles/clangformat.dir/depend
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/clangformat.dir/build.make third_party/picoquic/CMakeFiles/clangformat.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num= "Built target clangformat"
.PHONY : third_party/picoquic/CMakeFiles/clangformat.dir/all

# Build rule for subdir invocation for target.
third_party/picoquic/CMakeFiles/clangformat.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/clangformat.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : third_party/picoquic/CMakeFiles/clangformat.dir/rule

# Convenience name for target.
clangformat: third_party/picoquic/CMakeFiles/clangformat.dir/rule
.PHONY : clangformat

# clean rule for target.
third_party/picoquic/CMakeFiles/clangformat.dir/clean:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/clangformat.dir/build.make third_party/picoquic/CMakeFiles/clangformat.dir/clean
.PHONY : third_party/picoquic/CMakeFiles/clangformat.dir/clean

#=============================================================================
# Target rules for target _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir

# All Build rule for target.
_deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/all:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/build.make _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/depend
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/build.make _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=2 "Built target generate-picotls-probes"
.PHONY : _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/all

# Build rule for subdir invocation for target.
_deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/rule

# Convenience name for target.
generate-picotls-probes: _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/rule
.PHONY : generate-picotls-probes

# clean rule for target.
_deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/clean:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/build.make _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/clean
.PHONY : _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/clean

#=============================================================================
# Target rules for target _deps/picotls-build/CMakeFiles/picotls-core.dir

# All Build rule for target.
_deps/picotls-build/CMakeFiles/picotls-core.dir/all:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-core.dir/build.make _deps/picotls-build/CMakeFiles/picotls-core.dir/depend
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-core.dir/build.make _deps/picotls-build/CMakeFiles/picotls-core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=42,43,44 "Built target picotls-core"
.PHONY : _deps/picotls-build/CMakeFiles/picotls-core.dir/all

# Build rule for subdir invocation for target.
_deps/picotls-build/CMakeFiles/picotls-core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _deps/picotls-build/CMakeFiles/picotls-core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : _deps/picotls-build/CMakeFiles/picotls-core.dir/rule

# Convenience name for target.
picotls-core: _deps/picotls-build/CMakeFiles/picotls-core.dir/rule
.PHONY : picotls-core

# clean rule for target.
_deps/picotls-build/CMakeFiles/picotls-core.dir/clean:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-core.dir/build.make _deps/picotls-build/CMakeFiles/picotls-core.dir/clean
.PHONY : _deps/picotls-build/CMakeFiles/picotls-core.dir/clean

#=============================================================================
# Target rules for target _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir

# All Build rule for target.
_deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/build.make _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/depend
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/build.make _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=45,46,47,48,49,50,51,52,53,54,55,56,57,58 "Built target picotls-minicrypto"
.PHONY : _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all

# Build rule for subdir invocation for target.
_deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/rule

# Convenience name for target.
picotls-minicrypto: _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/rule
.PHONY : picotls-minicrypto

# clean rule for target.
_deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/clean:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/build.make _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/clean
.PHONY : _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/clean

#=============================================================================
# Target rules for target _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir

# All Build rule for target.
_deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/all:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/build.make _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/depend
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/build.make _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83 "Built target test-minicrypto.t"
.PHONY : _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/all

# Build rule for subdir invocation for target.
_deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/rule

# Convenience name for target.
test-minicrypto.t: _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/rule
.PHONY : test-minicrypto.t

# clean rule for target.
_deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/clean:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/build.make _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/clean
.PHONY : _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/clean

#=============================================================================
# Target rules for target _deps/picotls-build/CMakeFiles/picotls-openssl.dir

# All Build rule for target.
_deps/picotls-build/CMakeFiles/picotls-openssl.dir/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-openssl.dir/build.make _deps/picotls-build/CMakeFiles/picotls-openssl.dir/depend
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-openssl.dir/build.make _deps/picotls-build/CMakeFiles/picotls-openssl.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=59 "Built target picotls-openssl"
.PHONY : _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all

# Build rule for subdir invocation for target.
_deps/picotls-build/CMakeFiles/picotls-openssl.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : _deps/picotls-build/CMakeFiles/picotls-openssl.dir/rule

# Convenience name for target.
picotls-openssl: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/rule
.PHONY : picotls-openssl

# clean rule for target.
_deps/picotls-build/CMakeFiles/picotls-openssl.dir/clean:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-openssl.dir/build.make _deps/picotls-build/CMakeFiles/picotls-openssl.dir/clean
.PHONY : _deps/picotls-build/CMakeFiles/picotls-openssl.dir/clean

#=============================================================================
# Target rules for target _deps/picotls-build/CMakeFiles/cli.dir

# All Build rule for target.
_deps/picotls-build/CMakeFiles/cli.dir/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
_deps/picotls-build/CMakeFiles/cli.dir/all: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/cli.dir/build.make _deps/picotls-build/CMakeFiles/cli.dir/depend
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/cli.dir/build.make _deps/picotls-build/CMakeFiles/cli.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=1 "Built target cli"
.PHONY : _deps/picotls-build/CMakeFiles/cli.dir/all

# Build rule for subdir invocation for target.
_deps/picotls-build/CMakeFiles/cli.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _deps/picotls-build/CMakeFiles/cli.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : _deps/picotls-build/CMakeFiles/cli.dir/rule

# Convenience name for target.
cli: _deps/picotls-build/CMakeFiles/cli.dir/rule
.PHONY : cli

# clean rule for target.
_deps/picotls-build/CMakeFiles/cli.dir/clean:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/cli.dir/build.make _deps/picotls-build/CMakeFiles/cli.dir/clean
.PHONY : _deps/picotls-build/CMakeFiles/cli.dir/clean

#=============================================================================
# Target rules for target _deps/picotls-build/CMakeFiles/test-openssl.t.dir

# All Build rule for target.
_deps/picotls-build/CMakeFiles/test-openssl.t.dir/all:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/test-openssl.t.dir/build.make _deps/picotls-build/CMakeFiles/test-openssl.t.dir/depend
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/test-openssl.t.dir/build.make _deps/picotls-build/CMakeFiles/test-openssl.t.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100 "Built target test-openssl.t"
.PHONY : _deps/picotls-build/CMakeFiles/test-openssl.t.dir/all

# Build rule for subdir invocation for target.
_deps/picotls-build/CMakeFiles/test-openssl.t.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _deps/picotls-build/CMakeFiles/test-openssl.t.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : _deps/picotls-build/CMakeFiles/test-openssl.t.dir/rule

# Convenience name for target.
test-openssl.t: _deps/picotls-build/CMakeFiles/test-openssl.t.dir/rule
.PHONY : test-openssl.t

# clean rule for target.
_deps/picotls-build/CMakeFiles/test-openssl.t.dir/clean:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/test-openssl.t.dir/build.make _deps/picotls-build/CMakeFiles/test-openssl.t.dir/clean
.PHONY : _deps/picotls-build/CMakeFiles/test-openssl.t.dir/clean

#=============================================================================
# Target rules for target _deps/picotls-build/CMakeFiles/ptlsbench.dir

# All Build rule for target.
_deps/picotls-build/CMakeFiles/ptlsbench.dir/all: _deps/picotls-build/CMakeFiles/picotls-core.dir/all
_deps/picotls-build/CMakeFiles/ptlsbench.dir/all: _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/all
_deps/picotls-build/CMakeFiles/ptlsbench.dir/all: _deps/picotls-build/CMakeFiles/picotls-openssl.dir/all
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/ptlsbench.dir/build.make _deps/picotls-build/CMakeFiles/ptlsbench.dir/depend
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/ptlsbench.dir/build.make _deps/picotls-build/CMakeFiles/ptlsbench.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=60,61 "Built target ptlsbench"
.PHONY : _deps/picotls-build/CMakeFiles/ptlsbench.dir/all

# Build rule for subdir invocation for target.
_deps/picotls-build/CMakeFiles/ptlsbench.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _deps/picotls-build/CMakeFiles/ptlsbench.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : _deps/picotls-build/CMakeFiles/ptlsbench.dir/rule

# Convenience name for target.
ptlsbench: _deps/picotls-build/CMakeFiles/ptlsbench.dir/rule
.PHONY : ptlsbench

# clean rule for target.
_deps/picotls-build/CMakeFiles/ptlsbench.dir/clean:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/ptlsbench.dir/build.make _deps/picotls-build/CMakeFiles/ptlsbench.dir/clean
.PHONY : _deps/picotls-build/CMakeFiles/ptlsbench.dir/clean

#=============================================================================
# Target rules for target _deps/picotls-build/CMakeFiles/check.dir

# All Build rule for target.
_deps/picotls-build/CMakeFiles/check.dir/all: _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/all
_deps/picotls-build/CMakeFiles/check.dir/all: _deps/picotls-build/CMakeFiles/cli.dir/all
_deps/picotls-build/CMakeFiles/check.dir/all: _deps/picotls-build/CMakeFiles/test-openssl.t.dir/all
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/check.dir/build.make _deps/picotls-build/CMakeFiles/check.dir/depend
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/check.dir/build.make _deps/picotls-build/CMakeFiles/check.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num= "Built target check"
.PHONY : _deps/picotls-build/CMakeFiles/check.dir/all

# Build rule for subdir invocation for target.
_deps/picotls-build/CMakeFiles/check.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 38
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _deps/picotls-build/CMakeFiles/check.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : _deps/picotls-build/CMakeFiles/check.dir/rule

# Convenience name for target.
check: _deps/picotls-build/CMakeFiles/check.dir/rule
.PHONY : check

# clean rule for target.
_deps/picotls-build/CMakeFiles/check.dir/clean:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/check.dir/build.make _deps/picotls-build/CMakeFiles/check.dir/clean
.PHONY : _deps/picotls-build/CMakeFiles/check.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

