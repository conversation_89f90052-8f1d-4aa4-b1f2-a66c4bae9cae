/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -std=c99 -Wall -O2 -g   -Wall -Wextra -Os -ffunction-sections -fdata-sections -DPICOTLS_USE_DTRACE=1 -DPICOTLS_USE_BROTLI=1 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -Wl,-search_paths_first -Wl,-headerpad_max_install_names -L/opt/homebrew/lib "CMakeFiles/test-openssl.t.dir/deps/micro-ecc/uECC.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/aes.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/blockwise.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/chacha20.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/chash.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/curve25519.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/drbg.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/hmac.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/gcm.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/gf128.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/modes.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/poly1305.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/sha256.c.o" "CMakeFiles/test-openssl.t.dir/deps/cifra/src/sha512.c.o" "CMakeFiles/test-openssl.t.dir/lib/cifra.c.o" "CMakeFiles/test-openssl.t.dir/lib/cifra/x25519.c.o" "CMakeFiles/test-openssl.t.dir/lib/cifra/chacha20.c.o" "CMakeFiles/test-openssl.t.dir/lib/cifra/aes128.c.o" "CMakeFiles/test-openssl.t.dir/lib/cifra/aes256.c.o" "CMakeFiles/test-openssl.t.dir/lib/cifra/random.c.o" "CMakeFiles/test-openssl.t.dir/lib/uecc.c.o" "CMakeFiles/test-openssl.t.dir/lib/asn1.c.o" "CMakeFiles/test-openssl.t.dir/lib/pembase64.c.o" "CMakeFiles/test-openssl.t.dir/lib/ffx.c.o" "CMakeFiles/test-openssl.t.dir/deps/picotest/picotest.c.o" "CMakeFiles/test-openssl.t.dir/t/hpke.c.o" "CMakeFiles/test-openssl.t.dir/t/picotls.c.o" "CMakeFiles/test-openssl.t.dir/t/openssl.c.o" -o test-openssl.t  /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib 
