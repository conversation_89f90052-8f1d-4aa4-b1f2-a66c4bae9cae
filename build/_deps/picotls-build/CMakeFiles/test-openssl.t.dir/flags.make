# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# compile C with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc
C_DEFINES = 

C_INCLUDES = -I/Users/<USER>/Documents/augment-projects/stub/include -I/Users/<USER>/Documents/augment-projects/stub/src -I/Users/<USER>/Documents/augment-projects/stub/src/utils -I/Users/<USER>/Documents/augment-projects/stub/src/client -I/Users/<USER>/Documents/augment-projects/stub/src/device -I/Users/<USER>/Documents/augment-projects/stub/src/protocol -I/Users/<USER>/Documents/augment-projects/stub/src/handler -I/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic -I/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/deps/cifra/src/ext -I/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/deps/cifra/src -I/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/deps/micro-ecc -I/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/deps/picotest -I/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/include -I/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-build -I/opt/homebrew/Cellar/brotli/1.1.0/include -I/opt/homebrew/Cellar/openssl@3/3.3.1/include

C_FLAGSarm64 = -std=c99 -Wall -O2 -g   -Wall -Wextra -Os -ffunction-sections -fdata-sections -DPICOTLS_USE_DTRACE=1 -DPICOTLS_USE_BROTLI=1 -std=gnu11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -DPTLS_MEMORY_DEBUG=1

C_FLAGS = -std=c99 -Wall -O2 -g   -Wall -Wextra -Os -ffunction-sections -fdata-sections -DPICOTLS_USE_DTRACE=1 -DPICOTLS_USE_BROTLI=1 -std=gnu11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -DPTLS_MEMORY_DEBUG=1

