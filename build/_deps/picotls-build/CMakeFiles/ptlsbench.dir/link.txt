/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -std=c99 -Wall -O2 -g   -Wall -Wextra -Os -ffunction-sections -fdata-sections -DPICOTLS_USE_DTRACE=1 -DPICOTLS_USE_BROTLI=1 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -Wl,-search_paths_first -Wl,-headerpad_max_install_names -L/opt/homebrew/lib CMakeFiles/ptlsbench.dir/t/ptlsbench.c.o -o ptlsbench   -L"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/("  -L/opt/homebrew/Cellar/brotli/1.1.0/lib  -L"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/)"  -Wl,-rpath,"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/(" -Wl,-rpath,/opt/homebrew/Cellar/brotli/1.1.0/lib -Wl,-rpath,"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/)" libpicotls-minicrypto.a libpicotls-core.a libpicotls-openssl.a /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib libpicotls-core.a -lbrotlidec -lbrotlienc 
