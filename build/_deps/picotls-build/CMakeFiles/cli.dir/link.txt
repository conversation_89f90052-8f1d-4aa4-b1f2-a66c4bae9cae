/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -std=c99 -Wall -O2 -g   -Wall -Wextra -Os -ffunction-sections -fdata-sections -DPICOTLS_USE_DTRACE=1 -DPICOTLS_USE_BROTLI=1 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -Wl,-search_paths_first -Wl,-headerpad_max_install_names -L/opt/homebrew/lib CMakeFiles/cli.dir/t/cli.c.o CMakeFiles/cli.dir/lib/pembase64.c.o -o cli   -L"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/("  -L/opt/homebrew/Cellar/brotli/1.1.0/lib  -L"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/)"  -Wl,-rpath,"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/(" -Wl,-rpath,/opt/homebrew/Cellar/brotli/1.1.0/lib -Wl,-rpath,"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/)" libpicotls-openssl.a libpicotls-core.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libresolv.tbd -lbrotlidec -lbrotlienc /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib 
