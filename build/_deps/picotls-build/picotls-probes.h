/*
 * Generated by dtrace(1M).
 */

#ifndef	_PICOTLS_PROBES_H
#define	_PICOTLS_PROBES_H

#if !defined(DTRACE_PROBES_DISABLED) || !DTRACE_PROBES_DISABLED
#include <unistd.h>

#endif /* !defined(DTRACE_PROBES_DISABLED) || !DTRACE_PROBES_DISABLED */

#ifdef	__cplusplus
extern "C" {
#endif

#define PICOTLS_STABILITY "___dtrace_stability$picotls$v1$1_1_0_1_1_0_1_1_0_1_1_0_1_1_0"

#define PICOTLS_TYPEDEFS "___dtrace_typedefs$picotls$v2"

#if !defined(DTRACE_PROBES_DISABLED) || !DTRACE_PROBES_DISABLED

#define	PICOTLS_CLIENT_RANDOM(arg0, arg1) \
do { \
	__asm__ volatile(".reference " PICOTLS_TYPEDEFS); \
	__dtrace_probe$picotls$client_random$v1$7374727563742073745f70746c735f74202a$766f6964202a(arg0, arg1); \
	__asm__ volatile(".reference " PICOTLS_STABILITY); \
} while (0)
#define	PICOTLS_CLIENT_RANDOM_ENABLED() \
	({ int _r = __dtrace_isenabled$picotls$client_random$v1(); \
		__asm__ volatile(""); \
		_r; })
#define	PICOTLS_ECH_SELECTION(arg0, arg1) \
do { \
	__asm__ volatile(".reference " PICOTLS_TYPEDEFS); \
	__dtrace_probe$picotls$ech_selection$v1$7374727563742073745f70746c735f74202a$696e74(arg0, arg1); \
	__asm__ volatile(".reference " PICOTLS_STABILITY); \
} while (0)
#define	PICOTLS_ECH_SELECTION_ENABLED() \
	({ int _r = __dtrace_isenabled$picotls$ech_selection$v1(); \
		__asm__ volatile(""); \
		_r; })
#define	PICOTLS_FREE(arg0) \
do { \
	__asm__ volatile(".reference " PICOTLS_TYPEDEFS); \
	__dtrace_probe$picotls$free$v1$7374727563742073745f70746c735f74202a(arg0); \
	__asm__ volatile(".reference " PICOTLS_STABILITY); \
} while (0)
#define	PICOTLS_FREE_ENABLED() \
	({ int _r = __dtrace_isenabled$picotls$free$v1(); \
		__asm__ volatile(""); \
		_r; })
#define	PICOTLS_NEW(arg0, arg1) \
do { \
	__asm__ volatile(".reference " PICOTLS_TYPEDEFS); \
	__dtrace_probe$picotls$new$v1$7374727563742073745f70746c735f74202a$696e74(arg0, arg1); \
	__asm__ volatile(".reference " PICOTLS_STABILITY); \
} while (0)
#define	PICOTLS_NEW_ENABLED() \
	({ int _r = __dtrace_isenabled$picotls$new$v1(); \
		__asm__ volatile(""); \
		_r; })
#define	PICOTLS_NEW_SECRET(arg0, arg1, arg2) \
do { \
	__asm__ volatile(".reference " PICOTLS_TYPEDEFS); \
	__dtrace_probe$picotls$new_secret$v1$7374727563742073745f70746c735f74202a$63686172202a$63686172202a(arg0, arg1, arg2); \
	__asm__ volatile(".reference " PICOTLS_STABILITY); \
} while (0)
#define	PICOTLS_NEW_SECRET_ENABLED() \
	({ int _r = __dtrace_isenabled$picotls$new_secret$v1(); \
		__asm__ volatile(""); \
		_r; })
#define	PICOTLS_RECEIVE_MESSAGE(arg0, arg1, arg2, arg3, arg4) \
do { \
	__asm__ volatile(".reference " PICOTLS_TYPEDEFS); \
	__dtrace_probe$picotls$receive_message$v1$7374727563742073745f70746c735f74202a$75696e74385f74$766f6964202a$73697a655f74$696e74(arg0, arg1, arg2, arg3, arg4); \
	__asm__ volatile(".reference " PICOTLS_STABILITY); \
} while (0)
#define	PICOTLS_RECEIVE_MESSAGE_ENABLED() \
	({ int _r = __dtrace_isenabled$picotls$receive_message$v1(); \
		__asm__ volatile(""); \
		_r; })


extern void __dtrace_probe$picotls$client_random$v1$7374727563742073745f70746c735f74202a$766f6964202a(const struct st_ptls_t *, const void *);
extern int __dtrace_isenabled$picotls$client_random$v1(void);
extern void __dtrace_probe$picotls$ech_selection$v1$7374727563742073745f70746c735f74202a$696e74(const struct st_ptls_t *, int);
extern int __dtrace_isenabled$picotls$ech_selection$v1(void);
extern void __dtrace_probe$picotls$free$v1$7374727563742073745f70746c735f74202a(const struct st_ptls_t *);
extern int __dtrace_isenabled$picotls$free$v1(void);
extern void __dtrace_probe$picotls$new$v1$7374727563742073745f70746c735f74202a$696e74(const struct st_ptls_t *, int);
extern int __dtrace_isenabled$picotls$new$v1(void);
extern void __dtrace_probe$picotls$new_secret$v1$7374727563742073745f70746c735f74202a$63686172202a$63686172202a(const struct st_ptls_t *, const char *, const char *);
extern int __dtrace_isenabled$picotls$new_secret$v1(void);
extern void __dtrace_probe$picotls$receive_message$v1$7374727563742073745f70746c735f74202a$75696e74385f74$766f6964202a$73697a655f74$696e74(const struct st_ptls_t *, uint8_t, const void *, size_t, int);
extern int __dtrace_isenabled$picotls$receive_message$v1(void);

#else

#define	PICOTLS_CLIENT_RANDOM(arg0, arg1) \
do { \
	} while (0)
#define	PICOTLS_CLIENT_RANDOM_ENABLED() (0)
#define	PICOTLS_ECH_SELECTION(arg0, arg1) \
do { \
	} while (0)
#define	PICOTLS_ECH_SELECTION_ENABLED() (0)
#define	PICOTLS_FREE(arg0) \
do { \
	} while (0)
#define	PICOTLS_FREE_ENABLED() (0)
#define	PICOTLS_NEW(arg0, arg1) \
do { \
	} while (0)
#define	PICOTLS_NEW_ENABLED() (0)
#define	PICOTLS_NEW_SECRET(arg0, arg1, arg2) \
do { \
	} while (0)
#define	PICOTLS_NEW_SECRET_ENABLED() (0)
#define	PICOTLS_RECEIVE_MESSAGE(arg0, arg1, arg2, arg3, arg4) \
do { \
	} while (0)
#define	PICOTLS_RECEIVE_MESSAGE_ENABLED() (0)

#endif /* !defined(DTRACE_PROBES_DISABLED) || !DTRACE_PROBES_DISABLED */


#ifdef	__cplusplus
}
#endif

#endif	/* _PICOTLS_PROBES_H */
