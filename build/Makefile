# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles /Users/<USER>/Documents/augment-projects/stub/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named stub

# Build rule for target.
stub: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 stub
.PHONY : stub

# fast build rule for target.
stub/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/build
.PHONY : stub/fast

#=============================================================================
# Target rules for targets named picoquic-core

# Build rule for target.
picoquic-core: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 picoquic-core
.PHONY : picoquic-core

# fast build rule for target.
picoquic-core/fast:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/build
.PHONY : picoquic-core/fast

#=============================================================================
# Target rules for targets named picoquic-log

# Build rule for target.
picoquic-log: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 picoquic-log
.PHONY : picoquic-log

# fast build rule for target.
picoquic-log/fast:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/build
.PHONY : picoquic-log/fast

#=============================================================================
# Target rules for targets named picohttp-core

# Build rule for target.
picohttp-core: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 picohttp-core
.PHONY : picohttp-core

# fast build rule for target.
picohttp-core/fast:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/build
.PHONY : picohttp-core/fast

#=============================================================================
# Target rules for targets named picoquicdemo

# Build rule for target.
picoquicdemo: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 picoquicdemo
.PHONY : picoquicdemo

# fast build rule for target.
picoquicdemo/fast:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/build
.PHONY : picoquicdemo/fast

#=============================================================================
# Target rules for targets named picolog_t

# Build rule for target.
picolog_t: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 picolog_t
.PHONY : picolog_t

# fast build rule for target.
picolog_t/fast:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picolog_t.dir/build.make third_party/picoquic/CMakeFiles/picolog_t.dir/build
.PHONY : picolog_t/fast

#=============================================================================
# Target rules for targets named clangformat

# Build rule for target.
clangformat: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clangformat
.PHONY : clangformat

# fast build rule for target.
clangformat/fast:
	$(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/clangformat.dir/build.make third_party/picoquic/CMakeFiles/clangformat.dir/build
.PHONY : clangformat/fast

#=============================================================================
# Target rules for targets named generate-picotls-probes

# Build rule for target.
generate-picotls-probes: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 generate-picotls-probes
.PHONY : generate-picotls-probes

# fast build rule for target.
generate-picotls-probes/fast:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/build.make _deps/picotls-build/CMakeFiles/generate-picotls-probes.dir/build
.PHONY : generate-picotls-probes/fast

#=============================================================================
# Target rules for targets named picotls-core

# Build rule for target.
picotls-core: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 picotls-core
.PHONY : picotls-core

# fast build rule for target.
picotls-core/fast:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-core.dir/build.make _deps/picotls-build/CMakeFiles/picotls-core.dir/build
.PHONY : picotls-core/fast

#=============================================================================
# Target rules for targets named picotls-minicrypto

# Build rule for target.
picotls-minicrypto: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 picotls-minicrypto
.PHONY : picotls-minicrypto

# fast build rule for target.
picotls-minicrypto/fast:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/build.make _deps/picotls-build/CMakeFiles/picotls-minicrypto.dir/build
.PHONY : picotls-minicrypto/fast

#=============================================================================
# Target rules for targets named test-minicrypto.t

# Build rule for target.
test-minicrypto.t: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test-minicrypto.t
.PHONY : test-minicrypto.t

# fast build rule for target.
test-minicrypto.t/fast:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/build.make _deps/picotls-build/CMakeFiles/test-minicrypto.t.dir/build
.PHONY : test-minicrypto.t/fast

#=============================================================================
# Target rules for targets named picotls-openssl

# Build rule for target.
picotls-openssl: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 picotls-openssl
.PHONY : picotls-openssl

# fast build rule for target.
picotls-openssl/fast:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/picotls-openssl.dir/build.make _deps/picotls-build/CMakeFiles/picotls-openssl.dir/build
.PHONY : picotls-openssl/fast

#=============================================================================
# Target rules for targets named cli

# Build rule for target.
cli: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cli
.PHONY : cli

# fast build rule for target.
cli/fast:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/cli.dir/build.make _deps/picotls-build/CMakeFiles/cli.dir/build
.PHONY : cli/fast

#=============================================================================
# Target rules for targets named test-openssl.t

# Build rule for target.
test-openssl.t: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test-openssl.t
.PHONY : test-openssl.t

# fast build rule for target.
test-openssl.t/fast:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/test-openssl.t.dir/build.make _deps/picotls-build/CMakeFiles/test-openssl.t.dir/build
.PHONY : test-openssl.t/fast

#=============================================================================
# Target rules for targets named ptlsbench

# Build rule for target.
ptlsbench: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ptlsbench
.PHONY : ptlsbench

# fast build rule for target.
ptlsbench/fast:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/ptlsbench.dir/build.make _deps/picotls-build/CMakeFiles/ptlsbench.dir/build
.PHONY : ptlsbench/fast

#=============================================================================
# Target rules for targets named check

# Build rule for target.
check: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 check
.PHONY : check

# fast build rule for target.
check/fast:
	$(MAKE) $(MAKESILENT) -f _deps/picotls-build/CMakeFiles/check.dir/build.make _deps/picotls-build/CMakeFiles/check.dir/build
.PHONY : check/fast

src/client/quic_client.o: src/client/quic_client.c.o
.PHONY : src/client/quic_client.o

# target to build an object file
src/client/quic_client.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/client/quic_client.c.o
.PHONY : src/client/quic_client.c.o

src/client/quic_client.i: src/client/quic_client.c.i
.PHONY : src/client/quic_client.i

# target to preprocess a source file
src/client/quic_client.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/client/quic_client.c.i
.PHONY : src/client/quic_client.c.i

src/client/quic_client.s: src/client/quic_client.c.s
.PHONY : src/client/quic_client.s

# target to generate assembly for a file
src/client/quic_client.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/client/quic_client.c.s
.PHONY : src/client/quic_client.c.s

src/device/device_id.o: src/device/device_id.c.o
.PHONY : src/device/device_id.o

# target to build an object file
src/device/device_id.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/device/device_id.c.o
.PHONY : src/device/device_id.c.o

src/device/device_id.i: src/device/device_id.c.i
.PHONY : src/device/device_id.i

# target to preprocess a source file
src/device/device_id.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/device/device_id.c.i
.PHONY : src/device/device_id.c.i

src/device/device_id.s: src/device/device_id.c.s
.PHONY : src/device/device_id.s

# target to generate assembly for a file
src/device/device_id.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/device/device_id.c.s
.PHONY : src/device/device_id.c.s

src/device/device_register.o: src/device/device_register.c.o
.PHONY : src/device/device_register.o

# target to build an object file
src/device/device_register.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/device/device_register.c.o
.PHONY : src/device/device_register.c.o

src/device/device_register.i: src/device/device_register.c.i
.PHONY : src/device/device_register.i

# target to preprocess a source file
src/device/device_register.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/device/device_register.c.i
.PHONY : src/device/device_register.c.i

src/device/device_register.s: src/device/device_register.c.s
.PHONY : src/device/device_register.s

# target to generate assembly for a file
src/device/device_register.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/device/device_register.c.s
.PHONY : src/device/device_register.c.s

src/handler/stream_handler.o: src/handler/stream_handler.c.o
.PHONY : src/handler/stream_handler.o

# target to build an object file
src/handler/stream_handler.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/handler/stream_handler.c.o
.PHONY : src/handler/stream_handler.c.o

src/handler/stream_handler.i: src/handler/stream_handler.c.i
.PHONY : src/handler/stream_handler.i

# target to preprocess a source file
src/handler/stream_handler.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/handler/stream_handler.c.i
.PHONY : src/handler/stream_handler.c.i

src/handler/stream_handler.s: src/handler/stream_handler.c.s
.PHONY : src/handler/stream_handler.s

# target to generate assembly for a file
src/handler/stream_handler.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/handler/stream_handler.c.s
.PHONY : src/handler/stream_handler.c.s

src/handler/tcp_handler.o: src/handler/tcp_handler.c.o
.PHONY : src/handler/tcp_handler.o

# target to build an object file
src/handler/tcp_handler.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/handler/tcp_handler.c.o
.PHONY : src/handler/tcp_handler.c.o

src/handler/tcp_handler.i: src/handler/tcp_handler.c.i
.PHONY : src/handler/tcp_handler.i

# target to preprocess a source file
src/handler/tcp_handler.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/handler/tcp_handler.c.i
.PHONY : src/handler/tcp_handler.c.i

src/handler/tcp_handler.s: src/handler/tcp_handler.c.s
.PHONY : src/handler/tcp_handler.s

# target to generate assembly for a file
src/handler/tcp_handler.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/handler/tcp_handler.c.s
.PHONY : src/handler/tcp_handler.c.s

src/handler/udp_handler.o: src/handler/udp_handler.c.o
.PHONY : src/handler/udp_handler.o

# target to build an object file
src/handler/udp_handler.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/handler/udp_handler.c.o
.PHONY : src/handler/udp_handler.c.o

src/handler/udp_handler.i: src/handler/udp_handler.c.i
.PHONY : src/handler/udp_handler.i

# target to preprocess a source file
src/handler/udp_handler.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/handler/udp_handler.c.i
.PHONY : src/handler/udp_handler.c.i

src/handler/udp_handler.s: src/handler/udp_handler.c.s
.PHONY : src/handler/udp_handler.s

# target to generate assembly for a file
src/handler/udp_handler.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/handler/udp_handler.c.s
.PHONY : src/handler/udp_handler.c.s

src/main.o: src/main.c.o
.PHONY : src/main.o

# target to build an object file
src/main.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/main.c.o
.PHONY : src/main.c.o

src/main.i: src/main.c.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/main.c.i
.PHONY : src/main.c.i

src/main.s: src/main.c.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/main.c.s
.PHONY : src/main.c.s

src/protocol/protocol.o: src/protocol/protocol.c.o
.PHONY : src/protocol/protocol.o

# target to build an object file
src/protocol/protocol.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/protocol/protocol.c.o
.PHONY : src/protocol/protocol.c.o

src/protocol/protocol.i: src/protocol/protocol.c.i
.PHONY : src/protocol/protocol.i

# target to preprocess a source file
src/protocol/protocol.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/protocol/protocol.c.i
.PHONY : src/protocol/protocol.c.i

src/protocol/protocol.s: src/protocol/protocol.c.s
.PHONY : src/protocol/protocol.s

# target to generate assembly for a file
src/protocol/protocol.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/protocol/protocol.c.s
.PHONY : src/protocol/protocol.c.s

src/utils/logger.o: src/utils/logger.c.o
.PHONY : src/utils/logger.o

# target to build an object file
src/utils/logger.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/utils/logger.c.o
.PHONY : src/utils/logger.c.o

src/utils/logger.i: src/utils/logger.c.i
.PHONY : src/utils/logger.i

# target to preprocess a source file
src/utils/logger.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/utils/logger.c.i
.PHONY : src/utils/logger.c.i

src/utils/logger.s: src/utils/logger.c.s
.PHONY : src/utils/logger.s

# target to generate assembly for a file
src/utils/logger.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/utils/logger.c.s
.PHONY : src/utils/logger.c.s

src/utils/network_utils.o: src/utils/network_utils.c.o
.PHONY : src/utils/network_utils.o

# target to build an object file
src/utils/network_utils.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/utils/network_utils.c.o
.PHONY : src/utils/network_utils.c.o

src/utils/network_utils.i: src/utils/network_utils.c.i
.PHONY : src/utils/network_utils.i

# target to preprocess a source file
src/utils/network_utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/utils/network_utils.c.i
.PHONY : src/utils/network_utils.c.i

src/utils/network_utils.s: src/utils/network_utils.c.s
.PHONY : src/utils/network_utils.s

# target to generate assembly for a file
src/utils/network_utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stub.dir/build.make CMakeFiles/stub.dir/src/utils/network_utils.c.s
.PHONY : src/utils/network_utils.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... check"
	@echo "... clangformat"
	@echo "... generate-picotls-probes"
	@echo "... cli"
	@echo "... picohttp-core"
	@echo "... picolog_t"
	@echo "... picoquic-core"
	@echo "... picoquic-log"
	@echo "... picoquicdemo"
	@echo "... picotls-core"
	@echo "... picotls-minicrypto"
	@echo "... picotls-openssl"
	@echo "... ptlsbench"
	@echo "... stub"
	@echo "... test-minicrypto.t"
	@echo "... test-openssl.t"
	@echo "... src/client/quic_client.o"
	@echo "... src/client/quic_client.i"
	@echo "... src/client/quic_client.s"
	@echo "... src/device/device_id.o"
	@echo "... src/device/device_id.i"
	@echo "... src/device/device_id.s"
	@echo "... src/device/device_register.o"
	@echo "... src/device/device_register.i"
	@echo "... src/device/device_register.s"
	@echo "... src/handler/stream_handler.o"
	@echo "... src/handler/stream_handler.i"
	@echo "... src/handler/stream_handler.s"
	@echo "... src/handler/tcp_handler.o"
	@echo "... src/handler/tcp_handler.i"
	@echo "... src/handler/tcp_handler.s"
	@echo "... src/handler/udp_handler.o"
	@echo "... src/handler/udp_handler.i"
	@echo "... src/handler/udp_handler.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/protocol/protocol.o"
	@echo "... src/protocol/protocol.i"
	@echo "... src/protocol/protocol.s"
	@echo "... src/utils/logger.o"
	@echo "... src/utils/logger.i"
	@echo "... src/utils/logger.s"
	@echo "... src/utils/network_utils.o"
	@echo "... src/utils/network_utils.i"
	@echo "... src/utils/network_utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

