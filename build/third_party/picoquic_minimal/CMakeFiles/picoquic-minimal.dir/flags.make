# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# compile C with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc
C_DEFINES = -DDISABLE_DEBUG_PRINTF=1 -DPICOQUIC_MINIMAL_BUILD=1 -DPTLS_WITHOUT_FUSION=1 -DPTLS_WITHOUT_OPENSSL=1

C_INCLUDES = -I/Users/<USER>/Documents/augment-projects/stub/include -I/Users/<USER>/Documents/augment-projects/stub/src -I/Users/<USER>/Documents/augment-projects/stub/src/utils -I/Users/<USER>/Documents/augment-projects/stub/src/client -I/Users/<USER>/Documents/augment-projects/stub/src/device -I/Users/<USER>/Documents/augment-projects/stub/src/protocol -I/Users/<USER>/Documents/augment-projects/stub/src/handler -I/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic -I/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/../picoquic/picoquic -I/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/.

C_FLAGSarm64 =  -Wall -Wextra -Os -ffunction-sections -fdata-sections -std=c11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -Os -ffunction-sections -fdata-sections -Wall -Wextra -std=c11

C_FLAGS =  -Wall -Wextra -Os -ffunction-sections -fdata-sections -std=c11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -Os -ffunction-sections -fdata-sections -Wall -Wextra -std=c11

