
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/tls_stub.c" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.o" "gcc" "third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
