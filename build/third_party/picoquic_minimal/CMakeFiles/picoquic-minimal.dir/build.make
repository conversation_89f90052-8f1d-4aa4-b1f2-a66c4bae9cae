# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

# Include any dependencies generated for this target.
include third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.make

# Include the progress variables for this target.
include third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/progress.make

# Include the compile flags for this target's objects.
include third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o -MF CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o.d -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c > CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c -o CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.s

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/flags.make
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/tls_stub.c
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.o: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.o -MF CMakeFiles/picoquic-minimal.dir/tls_stub.c.o.d -o CMakeFiles/picoquic-minimal.dir/tls_stub.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/tls_stub.c

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-minimal.dir/tls_stub.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/tls_stub.c > CMakeFiles/picoquic-minimal.dir/tls_stub.c.i

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-minimal.dir/tls_stub.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/tls_stub.c -o CMakeFiles/picoquic-minimal.dir/tls_stub.c.s

# Object files for target picoquic-minimal
picoquic__minimal_OBJECTS = \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o" \
"CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o" \
"CMakeFiles/picoquic-minimal.dir/tls_stub.c.o"

# External object files for target picoquic-minimal
picoquic__minimal_EXTERNAL_OBJECTS =

third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.o
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make
third_party/picoquic_minimal/libpicoquic-minimal.a: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Linking C static library libpicoquic-minimal.a"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && $(CMAKE_COMMAND) -P CMakeFiles/picoquic-minimal.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/picoquic-minimal.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build: third_party/picoquic_minimal/libpicoquic-minimal.a
.PHONY : third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/clean:
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal && $(CMAKE_COMMAND) -P CMakeFiles/picoquic-minimal.dir/cmake_clean.cmake
.PHONY : third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/clean

third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/stub /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal /Users/<USER>/Documents/augment-projects/stub/build /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/depend

