/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar qc libpicoquic-minimal.a "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o" "CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o" "CMakeFiles/picoquic-minimal.dir/tls_openssl_minimal.c.o"
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib libpicoquic-minimal.a
