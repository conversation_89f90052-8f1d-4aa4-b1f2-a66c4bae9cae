# Install script for directory: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/usr/local")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY FILES "/Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal/libpicoquic-minimal.a")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/libpicoquic-minimal.a" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/libpicoquic-minimal.a")
    execute_process(COMMAND "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/libpicoquic-minimal.a")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/picoquic" TYPE FILE FILES
    "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/../picoquic/picoquic/picoquic.h"
    "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/../picoquic/picoquic/picosocks.h"
    "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/../picoquic/picoquic/picoquic_utils.h"
    "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/../picoquic/picoquic/picoquic_packet_loop.h"
    "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/../picoquic/picoquic/picoquic_config.h"
    "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/../picoquic/picoquic/picoquic_newreno.h"
    "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/../picoquic/picoquic/siphash.h"
    "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic_minimal/tls_stub.h"
    )
endif()

