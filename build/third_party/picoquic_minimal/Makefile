# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic_minimal//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic_minimal/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic_minimal/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic_minimal/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic_minimal/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/rule
.PHONY : third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/rule

# Convenience name for target.
picoquic-minimal: third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/rule
.PHONY : picoquic-minimal

# fast build rule for target.
picoquic-minimal/fast:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build
.PHONY : picoquic-minimal/fast

__/picoquic/picoquic/bytestream.o: __/picoquic/picoquic/bytestream.c.o
.PHONY : __/picoquic/picoquic/bytestream.o

# target to build an object file
__/picoquic/picoquic/bytestream.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.o
.PHONY : __/picoquic/picoquic/bytestream.c.o

__/picoquic/picoquic/bytestream.i: __/picoquic/picoquic/bytestream.c.i
.PHONY : __/picoquic/picoquic/bytestream.i

# target to preprocess a source file
__/picoquic/picoquic/bytestream.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.i
.PHONY : __/picoquic/picoquic/bytestream.c.i

__/picoquic/picoquic/bytestream.s: __/picoquic/picoquic/bytestream.c.s
.PHONY : __/picoquic/picoquic/bytestream.s

# target to generate assembly for a file
__/picoquic/picoquic/bytestream.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/bytestream.c.s
.PHONY : __/picoquic/picoquic/bytestream.c.s

__/picoquic/picoquic/cc_common.o: __/picoquic/picoquic/cc_common.c.o
.PHONY : __/picoquic/picoquic/cc_common.o

# target to build an object file
__/picoquic/picoquic/cc_common.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.o
.PHONY : __/picoquic/picoquic/cc_common.c.o

__/picoquic/picoquic/cc_common.i: __/picoquic/picoquic/cc_common.c.i
.PHONY : __/picoquic/picoquic/cc_common.i

# target to preprocess a source file
__/picoquic/picoquic/cc_common.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.i
.PHONY : __/picoquic/picoquic/cc_common.c.i

__/picoquic/picoquic/cc_common.s: __/picoquic/picoquic/cc_common.c.s
.PHONY : __/picoquic/picoquic/cc_common.s

# target to generate assembly for a file
__/picoquic/picoquic/cc_common.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/cc_common.c.s
.PHONY : __/picoquic/picoquic/cc_common.c.s

__/picoquic/picoquic/config.o: __/picoquic/picoquic/config.c.o
.PHONY : __/picoquic/picoquic/config.o

# target to build an object file
__/picoquic/picoquic/config.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.o
.PHONY : __/picoquic/picoquic/config.c.o

__/picoquic/picoquic/config.i: __/picoquic/picoquic/config.c.i
.PHONY : __/picoquic/picoquic/config.i

# target to preprocess a source file
__/picoquic/picoquic/config.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.i
.PHONY : __/picoquic/picoquic/config.c.i

__/picoquic/picoquic/config.s: __/picoquic/picoquic/config.c.s
.PHONY : __/picoquic/picoquic/config.s

# target to generate assembly for a file
__/picoquic/picoquic/config.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/config.c.s
.PHONY : __/picoquic/picoquic/config.c.s

__/picoquic/picoquic/frames.o: __/picoquic/picoquic/frames.c.o
.PHONY : __/picoquic/picoquic/frames.o

# target to build an object file
__/picoquic/picoquic/frames.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.o
.PHONY : __/picoquic/picoquic/frames.c.o

__/picoquic/picoquic/frames.i: __/picoquic/picoquic/frames.c.i
.PHONY : __/picoquic/picoquic/frames.i

# target to preprocess a source file
__/picoquic/picoquic/frames.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.i
.PHONY : __/picoquic/picoquic/frames.c.i

__/picoquic/picoquic/frames.s: __/picoquic/picoquic/frames.c.s
.PHONY : __/picoquic/picoquic/frames.s

# target to generate assembly for a file
__/picoquic/picoquic/frames.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/frames.c.s
.PHONY : __/picoquic/picoquic/frames.c.s

__/picoquic/picoquic/intformat.o: __/picoquic/picoquic/intformat.c.o
.PHONY : __/picoquic/picoquic/intformat.o

# target to build an object file
__/picoquic/picoquic/intformat.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.o
.PHONY : __/picoquic/picoquic/intformat.c.o

__/picoquic/picoquic/intformat.i: __/picoquic/picoquic/intformat.c.i
.PHONY : __/picoquic/picoquic/intformat.i

# target to preprocess a source file
__/picoquic/picoquic/intformat.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.i
.PHONY : __/picoquic/picoquic/intformat.c.i

__/picoquic/picoquic/intformat.s: __/picoquic/picoquic/intformat.c.s
.PHONY : __/picoquic/picoquic/intformat.s

# target to generate assembly for a file
__/picoquic/picoquic/intformat.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/intformat.c.s
.PHONY : __/picoquic/picoquic/intformat.c.s

__/picoquic/picoquic/logger.o: __/picoquic/picoquic/logger.c.o
.PHONY : __/picoquic/picoquic/logger.o

# target to build an object file
__/picoquic/picoquic/logger.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.o
.PHONY : __/picoquic/picoquic/logger.c.o

__/picoquic/picoquic/logger.i: __/picoquic/picoquic/logger.c.i
.PHONY : __/picoquic/picoquic/logger.i

# target to preprocess a source file
__/picoquic/picoquic/logger.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.i
.PHONY : __/picoquic/picoquic/logger.c.i

__/picoquic/picoquic/logger.s: __/picoquic/picoquic/logger.c.s
.PHONY : __/picoquic/picoquic/logger.s

# target to generate assembly for a file
__/picoquic/picoquic/logger.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/logger.c.s
.PHONY : __/picoquic/picoquic/logger.c.s

__/picoquic/picoquic/loss_recovery.o: __/picoquic/picoquic/loss_recovery.c.o
.PHONY : __/picoquic/picoquic/loss_recovery.o

# target to build an object file
__/picoquic/picoquic/loss_recovery.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.o
.PHONY : __/picoquic/picoquic/loss_recovery.c.o

__/picoquic/picoquic/loss_recovery.i: __/picoquic/picoquic/loss_recovery.c.i
.PHONY : __/picoquic/picoquic/loss_recovery.i

# target to preprocess a source file
__/picoquic/picoquic/loss_recovery.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.i
.PHONY : __/picoquic/picoquic/loss_recovery.c.i

__/picoquic/picoquic/loss_recovery.s: __/picoquic/picoquic/loss_recovery.c.s
.PHONY : __/picoquic/picoquic/loss_recovery.s

# target to generate assembly for a file
__/picoquic/picoquic/loss_recovery.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/loss_recovery.c.s
.PHONY : __/picoquic/picoquic/loss_recovery.c.s

__/picoquic/picoquic/newreno.o: __/picoquic/picoquic/newreno.c.o
.PHONY : __/picoquic/picoquic/newreno.o

# target to build an object file
__/picoquic/picoquic/newreno.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.o
.PHONY : __/picoquic/picoquic/newreno.c.o

__/picoquic/picoquic/newreno.i: __/picoquic/picoquic/newreno.c.i
.PHONY : __/picoquic/picoquic/newreno.i

# target to preprocess a source file
__/picoquic/picoquic/newreno.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.i
.PHONY : __/picoquic/picoquic/newreno.c.i

__/picoquic/picoquic/newreno.s: __/picoquic/picoquic/newreno.c.s
.PHONY : __/picoquic/picoquic/newreno.s

# target to generate assembly for a file
__/picoquic/picoquic/newreno.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/newreno.c.s
.PHONY : __/picoquic/picoquic/newreno.c.s

__/picoquic/picoquic/packet.o: __/picoquic/picoquic/packet.c.o
.PHONY : __/picoquic/picoquic/packet.o

# target to build an object file
__/picoquic/picoquic/packet.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.o
.PHONY : __/picoquic/picoquic/packet.c.o

__/picoquic/picoquic/packet.i: __/picoquic/picoquic/packet.c.i
.PHONY : __/picoquic/picoquic/packet.i

# target to preprocess a source file
__/picoquic/picoquic/packet.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.i
.PHONY : __/picoquic/picoquic/packet.c.i

__/picoquic/picoquic/packet.s: __/picoquic/picoquic/packet.c.s
.PHONY : __/picoquic/picoquic/packet.s

# target to generate assembly for a file
__/picoquic/picoquic/packet.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/packet.c.s
.PHONY : __/picoquic/picoquic/packet.c.s

__/picoquic/picoquic/paths.o: __/picoquic/picoquic/paths.c.o
.PHONY : __/picoquic/picoquic/paths.o

# target to build an object file
__/picoquic/picoquic/paths.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.o
.PHONY : __/picoquic/picoquic/paths.c.o

__/picoquic/picoquic/paths.i: __/picoquic/picoquic/paths.c.i
.PHONY : __/picoquic/picoquic/paths.i

# target to preprocess a source file
__/picoquic/picoquic/paths.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.i
.PHONY : __/picoquic/picoquic/paths.c.i

__/picoquic/picoquic/paths.s: __/picoquic/picoquic/paths.c.s
.PHONY : __/picoquic/picoquic/paths.s

# target to generate assembly for a file
__/picoquic/picoquic/paths.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/paths.c.s
.PHONY : __/picoquic/picoquic/paths.c.s

__/picoquic/picoquic/picohash.o: __/picoquic/picoquic/picohash.c.o
.PHONY : __/picoquic/picoquic/picohash.o

# target to build an object file
__/picoquic/picoquic/picohash.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.o
.PHONY : __/picoquic/picoquic/picohash.c.o

__/picoquic/picoquic/picohash.i: __/picoquic/picoquic/picohash.c.i
.PHONY : __/picoquic/picoquic/picohash.i

# target to preprocess a source file
__/picoquic/picoquic/picohash.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.i
.PHONY : __/picoquic/picoquic/picohash.c.i

__/picoquic/picoquic/picohash.s: __/picoquic/picoquic/picohash.c.s
.PHONY : __/picoquic/picoquic/picohash.s

# target to generate assembly for a file
__/picoquic/picoquic/picohash.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picohash.c.s
.PHONY : __/picoquic/picoquic/picohash.c.s

__/picoquic/picoquic/picosocks.o: __/picoquic/picoquic/picosocks.c.o
.PHONY : __/picoquic/picoquic/picosocks.o

# target to build an object file
__/picoquic/picoquic/picosocks.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.o
.PHONY : __/picoquic/picoquic/picosocks.c.o

__/picoquic/picoquic/picosocks.i: __/picoquic/picoquic/picosocks.c.i
.PHONY : __/picoquic/picoquic/picosocks.i

# target to preprocess a source file
__/picoquic/picoquic/picosocks.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.i
.PHONY : __/picoquic/picoquic/picosocks.c.i

__/picoquic/picoquic/picosocks.s: __/picoquic/picoquic/picosocks.c.s
.PHONY : __/picoquic/picoquic/picosocks.s

# target to generate assembly for a file
__/picoquic/picoquic/picosocks.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosocks.c.s
.PHONY : __/picoquic/picoquic/picosocks.c.s

__/picoquic/picoquic/picosplay.o: __/picoquic/picoquic/picosplay.c.o
.PHONY : __/picoquic/picoquic/picosplay.o

# target to build an object file
__/picoquic/picoquic/picosplay.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.o
.PHONY : __/picoquic/picoquic/picosplay.c.o

__/picoquic/picoquic/picosplay.i: __/picoquic/picoquic/picosplay.c.i
.PHONY : __/picoquic/picoquic/picosplay.i

# target to preprocess a source file
__/picoquic/picoquic/picosplay.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.i
.PHONY : __/picoquic/picoquic/picosplay.c.i

__/picoquic/picoquic/picosplay.s: __/picoquic/picoquic/picosplay.c.s
.PHONY : __/picoquic/picoquic/picosplay.s

# target to generate assembly for a file
__/picoquic/picoquic/picosplay.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/picosplay.c.s
.PHONY : __/picoquic/picoquic/picosplay.c.s

__/picoquic/picoquic/quicctx.o: __/picoquic/picoquic/quicctx.c.o
.PHONY : __/picoquic/picoquic/quicctx.o

# target to build an object file
__/picoquic/picoquic/quicctx.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.o
.PHONY : __/picoquic/picoquic/quicctx.c.o

__/picoquic/picoquic/quicctx.i: __/picoquic/picoquic/quicctx.c.i
.PHONY : __/picoquic/picoquic/quicctx.i

# target to preprocess a source file
__/picoquic/picoquic/quicctx.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.i
.PHONY : __/picoquic/picoquic/quicctx.c.i

__/picoquic/picoquic/quicctx.s: __/picoquic/picoquic/quicctx.c.s
.PHONY : __/picoquic/picoquic/quicctx.s

# target to generate assembly for a file
__/picoquic/picoquic/quicctx.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/quicctx.c.s
.PHONY : __/picoquic/picoquic/quicctx.c.s

__/picoquic/picoquic/sacks.o: __/picoquic/picoquic/sacks.c.o
.PHONY : __/picoquic/picoquic/sacks.o

# target to build an object file
__/picoquic/picoquic/sacks.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.o
.PHONY : __/picoquic/picoquic/sacks.c.o

__/picoquic/picoquic/sacks.i: __/picoquic/picoquic/sacks.c.i
.PHONY : __/picoquic/picoquic/sacks.i

# target to preprocess a source file
__/picoquic/picoquic/sacks.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.i
.PHONY : __/picoquic/picoquic/sacks.c.i

__/picoquic/picoquic/sacks.s: __/picoquic/picoquic/sacks.c.s
.PHONY : __/picoquic/picoquic/sacks.s

# target to generate assembly for a file
__/picoquic/picoquic/sacks.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sacks.c.s
.PHONY : __/picoquic/picoquic/sacks.c.s

__/picoquic/picoquic/sender.o: __/picoquic/picoquic/sender.c.o
.PHONY : __/picoquic/picoquic/sender.o

# target to build an object file
__/picoquic/picoquic/sender.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.o
.PHONY : __/picoquic/picoquic/sender.c.o

__/picoquic/picoquic/sender.i: __/picoquic/picoquic/sender.c.i
.PHONY : __/picoquic/picoquic/sender.i

# target to preprocess a source file
__/picoquic/picoquic/sender.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.i
.PHONY : __/picoquic/picoquic/sender.c.i

__/picoquic/picoquic/sender.s: __/picoquic/picoquic/sender.c.s
.PHONY : __/picoquic/picoquic/sender.s

# target to generate assembly for a file
__/picoquic/picoquic/sender.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sender.c.s
.PHONY : __/picoquic/picoquic/sender.c.s

__/picoquic/picoquic/siphash.o: __/picoquic/picoquic/siphash.c.o
.PHONY : __/picoquic/picoquic/siphash.o

# target to build an object file
__/picoquic/picoquic/siphash.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.o
.PHONY : __/picoquic/picoquic/siphash.c.o

__/picoquic/picoquic/siphash.i: __/picoquic/picoquic/siphash.c.i
.PHONY : __/picoquic/picoquic/siphash.i

# target to preprocess a source file
__/picoquic/picoquic/siphash.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.i
.PHONY : __/picoquic/picoquic/siphash.c.i

__/picoquic/picoquic/siphash.s: __/picoquic/picoquic/siphash.c.s
.PHONY : __/picoquic/picoquic/siphash.s

# target to generate assembly for a file
__/picoquic/picoquic/siphash.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/siphash.c.s
.PHONY : __/picoquic/picoquic/siphash.c.s

__/picoquic/picoquic/sockloop.o: __/picoquic/picoquic/sockloop.c.o
.PHONY : __/picoquic/picoquic/sockloop.o

# target to build an object file
__/picoquic/picoquic/sockloop.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.o
.PHONY : __/picoquic/picoquic/sockloop.c.o

__/picoquic/picoquic/sockloop.i: __/picoquic/picoquic/sockloop.c.i
.PHONY : __/picoquic/picoquic/sockloop.i

# target to preprocess a source file
__/picoquic/picoquic/sockloop.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.i
.PHONY : __/picoquic/picoquic/sockloop.c.i

__/picoquic/picoquic/sockloop.s: __/picoquic/picoquic/sockloop.c.s
.PHONY : __/picoquic/picoquic/sockloop.s

# target to generate assembly for a file
__/picoquic/picoquic/sockloop.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/sockloop.c.s
.PHONY : __/picoquic/picoquic/sockloop.c.s

__/picoquic/picoquic/ticket_store.o: __/picoquic/picoquic/ticket_store.c.o
.PHONY : __/picoquic/picoquic/ticket_store.o

# target to build an object file
__/picoquic/picoquic/ticket_store.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.o
.PHONY : __/picoquic/picoquic/ticket_store.c.o

__/picoquic/picoquic/ticket_store.i: __/picoquic/picoquic/ticket_store.c.i
.PHONY : __/picoquic/picoquic/ticket_store.i

# target to preprocess a source file
__/picoquic/picoquic/ticket_store.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.i
.PHONY : __/picoquic/picoquic/ticket_store.c.i

__/picoquic/picoquic/ticket_store.s: __/picoquic/picoquic/ticket_store.c.s
.PHONY : __/picoquic/picoquic/ticket_store.s

# target to generate assembly for a file
__/picoquic/picoquic/ticket_store.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/ticket_store.c.s
.PHONY : __/picoquic/picoquic/ticket_store.c.s

__/picoquic/picoquic/timing.o: __/picoquic/picoquic/timing.c.o
.PHONY : __/picoquic/picoquic/timing.o

# target to build an object file
__/picoquic/picoquic/timing.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.o
.PHONY : __/picoquic/picoquic/timing.c.o

__/picoquic/picoquic/timing.i: __/picoquic/picoquic/timing.c.i
.PHONY : __/picoquic/picoquic/timing.i

# target to preprocess a source file
__/picoquic/picoquic/timing.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.i
.PHONY : __/picoquic/picoquic/timing.c.i

__/picoquic/picoquic/timing.s: __/picoquic/picoquic/timing.c.s
.PHONY : __/picoquic/picoquic/timing.s

# target to generate assembly for a file
__/picoquic/picoquic/timing.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/timing.c.s
.PHONY : __/picoquic/picoquic/timing.c.s

__/picoquic/picoquic/token_store.o: __/picoquic/picoquic/token_store.c.o
.PHONY : __/picoquic/picoquic/token_store.o

# target to build an object file
__/picoquic/picoquic/token_store.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.o
.PHONY : __/picoquic/picoquic/token_store.c.o

__/picoquic/picoquic/token_store.i: __/picoquic/picoquic/token_store.c.i
.PHONY : __/picoquic/picoquic/token_store.i

# target to preprocess a source file
__/picoquic/picoquic/token_store.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.i
.PHONY : __/picoquic/picoquic/token_store.c.i

__/picoquic/picoquic/token_store.s: __/picoquic/picoquic/token_store.c.s
.PHONY : __/picoquic/picoquic/token_store.s

# target to generate assembly for a file
__/picoquic/picoquic/token_store.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/token_store.c.s
.PHONY : __/picoquic/picoquic/token_store.c.s

__/picoquic/picoquic/transport.o: __/picoquic/picoquic/transport.c.o
.PHONY : __/picoquic/picoquic/transport.o

# target to build an object file
__/picoquic/picoquic/transport.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.o
.PHONY : __/picoquic/picoquic/transport.c.o

__/picoquic/picoquic/transport.i: __/picoquic/picoquic/transport.c.i
.PHONY : __/picoquic/picoquic/transport.i

# target to preprocess a source file
__/picoquic/picoquic/transport.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.i
.PHONY : __/picoquic/picoquic/transport.c.i

__/picoquic/picoquic/transport.s: __/picoquic/picoquic/transport.c.s
.PHONY : __/picoquic/picoquic/transport.s

# target to generate assembly for a file
__/picoquic/picoquic/transport.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/transport.c.s
.PHONY : __/picoquic/picoquic/transport.c.s

__/picoquic/picoquic/util.o: __/picoquic/picoquic/util.c.o
.PHONY : __/picoquic/picoquic/util.o

# target to build an object file
__/picoquic/picoquic/util.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.o
.PHONY : __/picoquic/picoquic/util.c.o

__/picoquic/picoquic/util.i: __/picoquic/picoquic/util.c.i
.PHONY : __/picoquic/picoquic/util.i

# target to preprocess a source file
__/picoquic/picoquic/util.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.i
.PHONY : __/picoquic/picoquic/util.c.i

__/picoquic/picoquic/util.s: __/picoquic/picoquic/util.c.s
.PHONY : __/picoquic/picoquic/util.s

# target to generate assembly for a file
__/picoquic/picoquic/util.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/__/picoquic/picoquic/util.c.s
.PHONY : __/picoquic/picoquic/util.c.s

tls_stub.o: tls_stub.c.o
.PHONY : tls_stub.o

# target to build an object file
tls_stub.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.o
.PHONY : tls_stub.c.o

tls_stub.i: tls_stub.c.i
.PHONY : tls_stub.i

# target to preprocess a source file
tls_stub.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.i
.PHONY : tls_stub.c.i

tls_stub.s: tls_stub.c.s
.PHONY : tls_stub.s

# target to generate assembly for a file
tls_stub.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/build.make third_party/picoquic_minimal/CMakeFiles/picoquic-minimal.dir/tls_stub.c.s
.PHONY : tls_stub.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... picoquic-minimal"
	@echo "... __/picoquic/picoquic/bytestream.o"
	@echo "... __/picoquic/picoquic/bytestream.i"
	@echo "... __/picoquic/picoquic/bytestream.s"
	@echo "... __/picoquic/picoquic/cc_common.o"
	@echo "... __/picoquic/picoquic/cc_common.i"
	@echo "... __/picoquic/picoquic/cc_common.s"
	@echo "... __/picoquic/picoquic/config.o"
	@echo "... __/picoquic/picoquic/config.i"
	@echo "... __/picoquic/picoquic/config.s"
	@echo "... __/picoquic/picoquic/frames.o"
	@echo "... __/picoquic/picoquic/frames.i"
	@echo "... __/picoquic/picoquic/frames.s"
	@echo "... __/picoquic/picoquic/intformat.o"
	@echo "... __/picoquic/picoquic/intformat.i"
	@echo "... __/picoquic/picoquic/intformat.s"
	@echo "... __/picoquic/picoquic/logger.o"
	@echo "... __/picoquic/picoquic/logger.i"
	@echo "... __/picoquic/picoquic/logger.s"
	@echo "... __/picoquic/picoquic/loss_recovery.o"
	@echo "... __/picoquic/picoquic/loss_recovery.i"
	@echo "... __/picoquic/picoquic/loss_recovery.s"
	@echo "... __/picoquic/picoquic/newreno.o"
	@echo "... __/picoquic/picoquic/newreno.i"
	@echo "... __/picoquic/picoquic/newreno.s"
	@echo "... __/picoquic/picoquic/packet.o"
	@echo "... __/picoquic/picoquic/packet.i"
	@echo "... __/picoquic/picoquic/packet.s"
	@echo "... __/picoquic/picoquic/paths.o"
	@echo "... __/picoquic/picoquic/paths.i"
	@echo "... __/picoquic/picoquic/paths.s"
	@echo "... __/picoquic/picoquic/picohash.o"
	@echo "... __/picoquic/picoquic/picohash.i"
	@echo "... __/picoquic/picoquic/picohash.s"
	@echo "... __/picoquic/picoquic/picosocks.o"
	@echo "... __/picoquic/picoquic/picosocks.i"
	@echo "... __/picoquic/picoquic/picosocks.s"
	@echo "... __/picoquic/picoquic/picosplay.o"
	@echo "... __/picoquic/picoquic/picosplay.i"
	@echo "... __/picoquic/picoquic/picosplay.s"
	@echo "... __/picoquic/picoquic/quicctx.o"
	@echo "... __/picoquic/picoquic/quicctx.i"
	@echo "... __/picoquic/picoquic/quicctx.s"
	@echo "... __/picoquic/picoquic/sacks.o"
	@echo "... __/picoquic/picoquic/sacks.i"
	@echo "... __/picoquic/picoquic/sacks.s"
	@echo "... __/picoquic/picoquic/sender.o"
	@echo "... __/picoquic/picoquic/sender.i"
	@echo "... __/picoquic/picoquic/sender.s"
	@echo "... __/picoquic/picoquic/siphash.o"
	@echo "... __/picoquic/picoquic/siphash.i"
	@echo "... __/picoquic/picoquic/siphash.s"
	@echo "... __/picoquic/picoquic/sockloop.o"
	@echo "... __/picoquic/picoquic/sockloop.i"
	@echo "... __/picoquic/picoquic/sockloop.s"
	@echo "... __/picoquic/picoquic/ticket_store.o"
	@echo "... __/picoquic/picoquic/ticket_store.i"
	@echo "... __/picoquic/picoquic/ticket_store.s"
	@echo "... __/picoquic/picoquic/timing.o"
	@echo "... __/picoquic/picoquic/timing.i"
	@echo "... __/picoquic/picoquic/timing.s"
	@echo "... __/picoquic/picoquic/token_store.o"
	@echo "... __/picoquic/picoquic/token_store.i"
	@echo "... __/picoquic/picoquic/token_store.s"
	@echo "... __/picoquic/picoquic/transport.o"
	@echo "... __/picoquic/picoquic/transport.i"
	@echo "... __/picoquic/picoquic/transport.s"
	@echo "... __/picoquic/picoquic/util.o"
	@echo "... __/picoquic/picoquic/util.i"
	@echo "... __/picoquic/picoquic/util.s"
	@echo "... tls_stub.o"
	@echo "... tls_stub.i"
	@echo "... tls_stub.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

