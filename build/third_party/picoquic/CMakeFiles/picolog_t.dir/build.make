# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

# Include any dependencies generated for this target.
include third_party/picoquic/CMakeFiles/picolog_t.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include third_party/picoquic/CMakeFiles/picolog_t.dir/compiler_depend.make

# Include the progress variables for this target.
include third_party/picoquic/CMakeFiles/picolog_t.dir/progress.make

# Include the compile flags for this target's objects.
include third_party/picoquic/CMakeFiles/picolog_t.dir/flags.make

third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.o: third_party/picoquic/CMakeFiles/picolog_t.dir/flags.make
third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picolog/picolog.c
third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.o: third_party/picoquic/CMakeFiles/picolog_t.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.o -MF CMakeFiles/picolog_t.dir/picolog/picolog.c.o.d -o CMakeFiles/picolog_t.dir/picolog/picolog.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picolog/picolog.c

third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picolog_t.dir/picolog/picolog.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picolog/picolog.c > CMakeFiles/picolog_t.dir/picolog/picolog.c.i

third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picolog_t.dir/picolog/picolog.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picolog/picolog.c -o CMakeFiles/picolog_t.dir/picolog/picolog.c.s

# Object files for target picolog_t
picolog_t_OBJECTS = \
"CMakeFiles/picolog_t.dir/picolog/picolog.c.o"

# External object files for target picolog_t
picolog_t_EXTERNAL_OBJECTS =

third_party/picoquic/picolog_t: third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.o
third_party/picoquic/picolog_t: third_party/picoquic/CMakeFiles/picolog_t.dir/build.make
third_party/picoquic/picolog_t: third_party/picoquic/libpicoquic-log.a
third_party/picoquic/picolog_t: third_party/picoquic/libpicoquic-core.a
third_party/picoquic/picolog_t: /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libssl.dylib
third_party/picoquic/picolog_t: _deps/picotls-build/libpicotls-openssl.a
third_party/picoquic/picolog_t: /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib
third_party/picoquic/picolog_t: _deps/picotls-build/libpicotls-minicrypto.a
third_party/picoquic/picolog_t: _deps/picotls-build/libpicotls-core.a
third_party/picoquic/picolog_t: third_party/picoquic/CMakeFiles/picolog_t.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable picolog_t"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/picolog_t.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
third_party/picoquic/CMakeFiles/picolog_t.dir/build: third_party/picoquic/picolog_t
.PHONY : third_party/picoquic/CMakeFiles/picolog_t.dir/build

third_party/picoquic/CMakeFiles/picolog_t.dir/clean:
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -P CMakeFiles/picolog_t.dir/cmake_clean.cmake
.PHONY : third_party/picoquic/CMakeFiles/picolog_t.dir/clean

third_party/picoquic/CMakeFiles/picolog_t.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/stub /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic/CMakeFiles/picolog_t.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : third_party/picoquic/CMakeFiles/picolog_t.dir/depend

