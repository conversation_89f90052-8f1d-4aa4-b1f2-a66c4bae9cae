/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc  -Wall -Wextra -Os -ffunction-sections -fdata-sections -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -Wl,-search_paths_first -Wl,-headerpad_max_install_names -L/opt/homebrew/lib CMakeFiles/picolog_t.dir/picolog/picolog.c.o -o picolog_t   -L"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/("  -L/opt/homebrew/Cellar/brotli/1.1.0/lib  -L"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/)"  -Wl,-rpath,"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/(" -Wl,-rpath,/opt/homebrew/Cellar/brotli/1.1.0/lib -Wl,-rpath,"/Users/<USER>/Documents/augment-projects/stub/build/_deps/picotls-src/)" libpicoquic-log.a libpicoquic-core.a /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libssl.dylib ../../_deps/picotls-build/libpicotls-openssl.a /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib ../../_deps/picotls-build/libpicotls-minicrypto.a ../../_deps/picotls-build/libpicotls-core.a -lbrotlidec -lbrotlienc 
