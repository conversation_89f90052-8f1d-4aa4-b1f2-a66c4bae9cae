# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

# Utility rule file for clangformat.

# Include any custom commands dependencies for this target.
include third_party/picoquic/CMakeFiles/clangformat.dir/compiler_depend.make

# Include the progress variables for this target.
include third_party/picoquic/CMakeFiles/clangformat.dir/progress.make

third_party/picoquic/CMakeFiles/clangformat:
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && clang-format -style=Webkit -i /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/UnitTest1/targetver.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/baton_app/baton_app.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/config.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/auto_memlog.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/autoqlog.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/autoqlog.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/cidset.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/cidset.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/csv.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/csv.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logconvert.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logconvert.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logreader.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logreader.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/memory_log.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/qlog.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/qlog.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/svg.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/svg.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/democlient.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/democlient.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/demoserver.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/demoserver.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_client.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_common.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_common.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_server.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_uri.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_uri.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/pico_webtransport.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/quicperf.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/quicperf.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/webtransport.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/wt_baton.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/wt_baton.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp_t/picohttp_t.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picolog/picolog.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr1.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cidset.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cubic.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ech.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/fastcc.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logwriter.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/memory_log.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/pacing.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/performance_log.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/performance_log.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_bbr.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_bbr1.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_binlog.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_config.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_crypto_provider_api.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_cubic.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_fastcc.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_internal.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_lb.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_lb.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_logger.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_mbedtls.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_newreno.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_packet_loop.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_prague.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_fusion.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_minicrypto.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_openssl.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_set_binlog.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_set_textlog.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_unified_log.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_utils.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/port_blocking.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/prague.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/register_all_cc_algorithms.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sim_link.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/spinbit.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/tls_api.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/tls_api.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/unified_log.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/wincompat.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/winsockloop.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic_c2csv_t/picoquic_c2csv_t.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic_mbedtls/ptls_mbedtls.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic_mbedtls/ptls_mbedtls.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic_mbedtls/ptls_mbedtls_sign.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic_t/picoquic_t.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/getopt.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/getopt.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/picoquicdemo.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/ack_frequency_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/ack_of_ack_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/app_limited.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/bytestream_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/cc_compete_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/cert_verify_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/cleartext_aead_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/cnx_creation_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/cnxstress.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/code_version_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/config_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/congestion_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/cpu_limited.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/datagram_tests.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/delay_tolerant_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/ech_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/edge_cases.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/getter_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/h3zero_stream_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/h3zero_uri_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/h3zerotest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/hashtest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/high_latency_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/intformattest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/l4s_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/mbedtls_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/mediatest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/memlog_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/minicrypto_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/multipath_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/netperf_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/openssl_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/p2p_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/pacing_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/parseheadertest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/picolog_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/picoquic_lb_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/picoquic_ns.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/picoquic_ns.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/picoquictest.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/picoquictest_internal.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/pn2pn64test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/qinqtest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/qlog_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/quic_tester.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/quicperf_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/sacktest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/satellite_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/skip_frame_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/socket_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/sockloop_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/spinbit_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/splay_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/stream0_frame_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/stresstest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/ticket_store_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/tls_api_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/transport_param_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/util_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/warptest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/webtransport_test.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquictest/wifitest.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/quicwind/Resource.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/quicwind/quicwind.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/quicwind/quicwind_proc.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/quicwind/targetver.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/sample/picoquic_sample.h /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/sample/sample.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/sample/sample_background.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/sample/sample_client.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/sample/sample_server.c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/thread_tester/thread_test.c

clangformat: third_party/picoquic/CMakeFiles/clangformat
clangformat: third_party/picoquic/CMakeFiles/clangformat.dir/build.make
.PHONY : clangformat

# Rule to build all files generated by this target.
third_party/picoquic/CMakeFiles/clangformat.dir/build: clangformat
.PHONY : third_party/picoquic/CMakeFiles/clangformat.dir/build

third_party/picoquic/CMakeFiles/clangformat.dir/clean:
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -P CMakeFiles/clangformat.dir/cmake_clean.cmake
.PHONY : third_party/picoquic/CMakeFiles/clangformat.dir/clean

third_party/picoquic/CMakeFiles/clangformat.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/stub /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic/CMakeFiles/clangformat.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : third_party/picoquic/CMakeFiles/clangformat.dir/depend

