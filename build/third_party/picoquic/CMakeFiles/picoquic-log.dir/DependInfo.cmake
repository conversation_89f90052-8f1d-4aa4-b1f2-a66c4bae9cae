
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/autoqlog.c" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/cidset.c" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/csv.c" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logconvert.c" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logreader.c" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/memory_log.c" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/qlog.c" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/svg.c" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
