# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

# Include any dependencies generated for this target.
include third_party/picoquic/CMakeFiles/picoquic-log.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include third_party/picoquic/CMakeFiles/picoquic-log.dir/compiler_depend.make

# Include the progress variables for this target.
include third_party/picoquic/CMakeFiles/picoquic-log.dir/progress.make

# Include the compile flags for this target's objects.
include third_party/picoquic/CMakeFiles/picoquic-log.dir/flags.make

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/autoqlog.c
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o -MF CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o.d -o CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/autoqlog.c

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/autoqlog.c > CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.i

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/autoqlog.c -o CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.s

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/cidset.c
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.o -MF CMakeFiles/picoquic-log.dir/loglib/cidset.c.o.d -o CMakeFiles/picoquic-log.dir/loglib/cidset.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/cidset.c

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-log.dir/loglib/cidset.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/cidset.c > CMakeFiles/picoquic-log.dir/loglib/cidset.c.i

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-log.dir/loglib/cidset.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/cidset.c -o CMakeFiles/picoquic-log.dir/loglib/cidset.c.s

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/csv.c
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.o -MF CMakeFiles/picoquic-log.dir/loglib/csv.c.o.d -o CMakeFiles/picoquic-log.dir/loglib/csv.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/csv.c

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-log.dir/loglib/csv.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/csv.c > CMakeFiles/picoquic-log.dir/loglib/csv.c.i

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-log.dir/loglib/csv.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/csv.c -o CMakeFiles/picoquic-log.dir/loglib/csv.c.s

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logconvert.c
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o -MF CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o.d -o CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logconvert.c

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-log.dir/loglib/logconvert.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logconvert.c > CMakeFiles/picoquic-log.dir/loglib/logconvert.c.i

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-log.dir/loglib/logconvert.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logconvert.c -o CMakeFiles/picoquic-log.dir/loglib/logconvert.c.s

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logreader.c
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.o -MF CMakeFiles/picoquic-log.dir/loglib/logreader.c.o.d -o CMakeFiles/picoquic-log.dir/loglib/logreader.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logreader.c

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-log.dir/loglib/logreader.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logreader.c > CMakeFiles/picoquic-log.dir/loglib/logreader.c.i

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-log.dir/loglib/logreader.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/logreader.c -o CMakeFiles/picoquic-log.dir/loglib/logreader.c.s

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/memory_log.c
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o -MF CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o.d -o CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/memory_log.c

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-log.dir/loglib/memory_log.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/memory_log.c > CMakeFiles/picoquic-log.dir/loglib/memory_log.c.i

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-log.dir/loglib/memory_log.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/memory_log.c -o CMakeFiles/picoquic-log.dir/loglib/memory_log.c.s

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/qlog.c
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.o -MF CMakeFiles/picoquic-log.dir/loglib/qlog.c.o.d -o CMakeFiles/picoquic-log.dir/loglib/qlog.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/qlog.c

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-log.dir/loglib/qlog.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/qlog.c > CMakeFiles/picoquic-log.dir/loglib/qlog.c.i

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-log.dir/loglib/qlog.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/qlog.c -o CMakeFiles/picoquic-log.dir/loglib/qlog.c.s

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/svg.c
third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.o: third_party/picoquic/CMakeFiles/picoquic-log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.o -MF CMakeFiles/picoquic-log.dir/loglib/svg.c.o.d -o CMakeFiles/picoquic-log.dir/loglib/svg.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/svg.c

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-log.dir/loglib/svg.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/svg.c > CMakeFiles/picoquic-log.dir/loglib/svg.c.i

third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-log.dir/loglib/svg.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib/svg.c -o CMakeFiles/picoquic-log.dir/loglib/svg.c.s

# Object files for target picoquic-log
picoquic__log_OBJECTS = \
"CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o" \
"CMakeFiles/picoquic-log.dir/loglib/cidset.c.o" \
"CMakeFiles/picoquic-log.dir/loglib/csv.c.o" \
"CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o" \
"CMakeFiles/picoquic-log.dir/loglib/logreader.c.o" \
"CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o" \
"CMakeFiles/picoquic-log.dir/loglib/qlog.c.o" \
"CMakeFiles/picoquic-log.dir/loglib/svg.c.o"

# External object files for target picoquic-log
picoquic__log_EXTERNAL_OBJECTS =

third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o
third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.o
third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.o
third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o
third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.o
third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o
third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.o
third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.o
third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make
third_party/picoquic/libpicoquic-log.a: third_party/picoquic/CMakeFiles/picoquic-log.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Linking C static library libpicoquic-log.a"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -P CMakeFiles/picoquic-log.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/picoquic-log.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
third_party/picoquic/CMakeFiles/picoquic-log.dir/build: third_party/picoquic/libpicoquic-log.a
.PHONY : third_party/picoquic/CMakeFiles/picoquic-log.dir/build

third_party/picoquic/CMakeFiles/picoquic-log.dir/clean:
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -P CMakeFiles/picoquic-log.dir/cmake_clean.cmake
.PHONY : third_party/picoquic/CMakeFiles/picoquic-log.dir/clean

third_party/picoquic/CMakeFiles/picoquic-log.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/stub /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic/CMakeFiles/picoquic-log.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : third_party/picoquic/CMakeFiles/picoquic-log.dir/depend

