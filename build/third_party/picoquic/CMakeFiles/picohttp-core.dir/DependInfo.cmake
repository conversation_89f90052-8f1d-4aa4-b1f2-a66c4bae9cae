
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/democlient.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/demoserver.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_client.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_common.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_server.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_uri.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/quicperf.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/webtransport.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/wt_baton.c" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o" "gcc" "third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
