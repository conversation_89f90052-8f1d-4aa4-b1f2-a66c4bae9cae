# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

# Include any dependencies generated for this target.
include third_party/picoquic/CMakeFiles/picohttp-core.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.make

# Include the progress variables for this target.
include third_party/picoquic/CMakeFiles/picohttp-core.dir/progress.make

# Include the compile flags for this target's objects.
include third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/democlient.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/democlient.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/democlient.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/democlient.c > CMakeFiles/picohttp-core.dir/picohttp/democlient.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/democlient.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/democlient.c -o CMakeFiles/picohttp-core.dir/picohttp/democlient.c.s

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/demoserver.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/demoserver.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/demoserver.c > CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/demoserver.c -o CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.s

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero.c > CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero.c -o CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.s

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_client.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_client.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_client.c > CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_client.c -o CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.s

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_common.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_common.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_common.c > CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_common.c -o CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.s

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_server.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_server.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_server.c > CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_server.c -o CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.s

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_uri.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_uri.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_uri.c > CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/h3zero_uri.c -o CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.s

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/quicperf.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/quicperf.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/quicperf.c > CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/quicperf.c -o CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.s

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/webtransport.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/webtransport.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/webtransport.c > CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/webtransport.c -o CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.s

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/flags.make
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/wt_baton.c
third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o: third_party/picoquic/CMakeFiles/picohttp-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o -MF CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o.d -o CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/wt_baton.c

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/wt_baton.c > CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.i

third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp/wt_baton.c -o CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.s

# Object files for target picohttp-core
picohttp__core_OBJECTS = \
"CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o" \
"CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o" \
"CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o" \
"CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o" \
"CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o" \
"CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o" \
"CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o" \
"CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o" \
"CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o" \
"CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o"

# External object files for target picohttp-core
picohttp__core_EXTERNAL_OBJECTS =

third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make
third_party/picoquic/libpicohttp-core.a: third_party/picoquic/CMakeFiles/picohttp-core.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking C static library libpicohttp-core.a"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -P CMakeFiles/picohttp-core.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/picohttp-core.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
third_party/picoquic/CMakeFiles/picohttp-core.dir/build: third_party/picoquic/libpicohttp-core.a
.PHONY : third_party/picoquic/CMakeFiles/picohttp-core.dir/build

third_party/picoquic/CMakeFiles/picohttp-core.dir/clean:
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -P CMakeFiles/picohttp-core.dir/cmake_clean.cmake
.PHONY : third_party/picoquic/CMakeFiles/picohttp-core.dir/clean

third_party/picoquic/CMakeFiles/picohttp-core.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/stub /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic/CMakeFiles/picohttp-core.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : third_party/picoquic/CMakeFiles/picohttp-core.dir/depend

