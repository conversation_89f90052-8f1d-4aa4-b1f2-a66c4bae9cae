# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

# Include any dependencies generated for this target.
include third_party/picoquic/CMakeFiles/picoquicdemo.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include third_party/picoquic/CMakeFiles/picoquicdemo.dir/compiler_depend.make

# Include the progress variables for this target.
include third_party/picoquic/CMakeFiles/picoquicdemo.dir/progress.make

# Include the compile flags for this target's objects.
include third_party/picoquic/CMakeFiles/picoquicdemo.dir/flags.make

third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o: third_party/picoquic/CMakeFiles/picoquicdemo.dir/flags.make
third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/picoquicdemo.c
third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o: third_party/picoquic/CMakeFiles/picoquicdemo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o -MF CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o.d -o CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/picoquicdemo.c

third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/picoquicdemo.c > CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.i

third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/picoquicdemo.c -o CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.s

third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o: third_party/picoquic/CMakeFiles/picoquicdemo.dir/flags.make
third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/getopt.c
third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o: third_party/picoquic/CMakeFiles/picoquicdemo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o -MF CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o.d -o CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/getopt.c

third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/getopt.c > CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.i

third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquicfirst/getopt.c -o CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.s

# Object files for target picoquicdemo
picoquicdemo_OBJECTS = \
"CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o" \
"CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o"

# External object files for target picoquicdemo
picoquicdemo_EXTERNAL_OBJECTS =

third_party/picoquic/picoquicdemo: third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o
third_party/picoquic/picoquicdemo: third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o
third_party/picoquic/picoquicdemo: third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make
third_party/picoquic/picoquicdemo: _deps/picotls-build/libpicotls-core.a
third_party/picoquic/picoquicdemo: _deps/picotls-build/libpicotls-openssl.a
third_party/picoquic/picoquicdemo: _deps/picotls-build/libpicotls-minicrypto.a
third_party/picoquic/picoquicdemo: /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libssl.dylib
third_party/picoquic/picoquicdemo: /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib
third_party/picoquic/picoquicdemo: third_party/picoquic/libpicoquic-log.a
third_party/picoquic/picoquicdemo: third_party/picoquic/libpicoquic-core.a
third_party/picoquic/picoquicdemo: third_party/picoquic/libpicohttp-core.a
third_party/picoquic/picoquicdemo: third_party/picoquic/libpicoquic-core.a
third_party/picoquic/picoquicdemo: _deps/picotls-build/libpicotls-openssl.a
third_party/picoquic/picoquicdemo: _deps/picotls-build/libpicotls-minicrypto.a
third_party/picoquic/picoquicdemo: _deps/picotls-build/libpicotls-core.a
third_party/picoquic/picoquicdemo: /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libssl.dylib
third_party/picoquic/picoquicdemo: /opt/homebrew/Cellar/openssl@3/3.3.1/lib/libcrypto.dylib
third_party/picoquic/picoquicdemo: third_party/picoquic/CMakeFiles/picoquicdemo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking C executable picoquicdemo"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/picoquicdemo.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
third_party/picoquic/CMakeFiles/picoquicdemo.dir/build: third_party/picoquic/picoquicdemo
.PHONY : third_party/picoquic/CMakeFiles/picoquicdemo.dir/build

third_party/picoquic/CMakeFiles/picoquicdemo.dir/clean:
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -P CMakeFiles/picoquicdemo.dir/cmake_clean.cmake
.PHONY : third_party/picoquic/CMakeFiles/picoquicdemo.dir/clean

third_party/picoquic/CMakeFiles/picoquicdemo.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/stub /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic/CMakeFiles/picoquicdemo.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : third_party/picoquic/CMakeFiles/picoquicdemo.dir/depend

