# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# compile C with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc
C_DEFINES = -DDISABLE_DEBUG_PRINTF -DPTLS_WITHOUT_FUSION

C_INCLUDES = -I/Users/<USER>/Documents/augment-projects/stub/include -I/Users/<USER>/Documents/augment-projects/stub/src -I/Users/<USER>/Documents/augment-projects/stub/src/utils -I/Users/<USER>/Documents/augment-projects/stub/src/client -I/Users/<USER>/Documents/augment-projects/stub/src/device -I/Users/<USER>/Documents/augment-projects/stub/src/protocol -I/Users/<USER>/Documents/augment-projects/stub/src/handler -I/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic -I/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picohttp -I/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/loglib -I/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic_mbedtls

C_FLAGSarm64 =  -Wall -Wextra -Os -ffunction-sections -fdata-sections -std=gnu11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -O3 -Wall -fno-exceptions -fno-signed-zeros -fno-trapping-math

C_FLAGS =  -Wall -Wextra -Os -ffunction-sections -fdata-sections -std=gnu11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=15.3 -O3 -Wall -fno-exceptions -fno-signed-zeros -fno-trapping-math

