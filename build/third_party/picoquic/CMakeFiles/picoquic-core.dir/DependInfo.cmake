
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr1.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cubic.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ech.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/fastcc.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logwriter.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/pacing.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/performance_log.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_lb.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_mbedtls.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_fusion.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_minicrypto.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_openssl.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/port_blocking.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/prague.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/register_all_cc_algorithms.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sim_link.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/spinbit.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/tls_api.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/unified_log.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o.d"
  "/Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.o" "gcc" "third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
