# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

# Include any dependencies generated for this target.
include third_party/picoquic/CMakeFiles/picoquic-core.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.make

# Include the progress variables for this target.
include third_party/picoquic/CMakeFiles/picoquic-core.dir/progress.make

# Include the compile flags for this target's objects.
include third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/bbr.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr.c > CMakeFiles/picoquic-core.dir/picoquic/bbr.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/bbr.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr.c -o CMakeFiles/picoquic-core.dir/picoquic/bbr.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr1.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr1.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr1.c > CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bbr1.c -o CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c > CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/bytestream.c -o CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c > CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cc_common.c -o CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/config.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/config.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/config.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c > CMakeFiles/picoquic-core.dir/picoquic/config.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/config.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/config.c -o CMakeFiles/picoquic-core.dir/picoquic/config.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cubic.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cubic.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/cubic.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cubic.c > CMakeFiles/picoquic-core.dir/picoquic/cubic.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/cubic.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/cubic.c -o CMakeFiles/picoquic-core.dir/picoquic/cubic.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ech.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/ech.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/ech.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ech.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/ech.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ech.c > CMakeFiles/picoquic-core.dir/picoquic/ech.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/ech.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ech.c -o CMakeFiles/picoquic-core.dir/picoquic/ech.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/fastcc.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/fastcc.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/fastcc.c > CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/fastcc.c -o CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/frames.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/frames.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/frames.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c > CMakeFiles/picoquic-core.dir/picoquic/frames.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/frames.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/frames.c -o CMakeFiles/picoquic-core.dir/picoquic/frames.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/intformat.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c > CMakeFiles/picoquic-core.dir/picoquic/intformat.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/intformat.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/intformat.c -o CMakeFiles/picoquic-core.dir/picoquic/intformat.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/logger.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/logger.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/logger.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c > CMakeFiles/picoquic-core.dir/picoquic/logger.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/logger.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logger.c -o CMakeFiles/picoquic-core.dir/picoquic/logger.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logwriter.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logwriter.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logwriter.c > CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/logwriter.c -o CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c > CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/loss_recovery.c -o CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/newreno.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c > CMakeFiles/picoquic-core.dir/picoquic/newreno.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/newreno.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/newreno.c -o CMakeFiles/picoquic-core.dir/picoquic/newreno.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/pacing.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/pacing.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/pacing.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/pacing.c > CMakeFiles/picoquic-core.dir/picoquic/pacing.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/pacing.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/pacing.c -o CMakeFiles/picoquic-core.dir/picoquic/pacing.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/packet.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/packet.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/packet.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c > CMakeFiles/picoquic-core.dir/picoquic/packet.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/packet.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/packet.c -o CMakeFiles/picoquic-core.dir/picoquic/packet.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/paths.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/paths.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/paths.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c > CMakeFiles/picoquic-core.dir/picoquic/paths.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/paths.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/paths.c -o CMakeFiles/picoquic-core.dir/picoquic/paths.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/performance_log.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/performance_log.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/performance_log.c > CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/performance_log.c -o CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/picohash.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c > CMakeFiles/picoquic-core.dir/picoquic/picohash.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/picohash.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picohash.c -o CMakeFiles/picoquic-core.dir/picoquic/picohash.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_lb.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_lb.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_lb.c > CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_lb.c -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_fusion.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_fusion.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_fusion.c > CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_fusion.c -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_minicrypto.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_minicrypto.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_minicrypto.c > CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_minicrypto.c -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_openssl.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_openssl.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_openssl.c > CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_ptls_openssl.c -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_mbedtls.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_mbedtls.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_mbedtls.c > CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picoquic_mbedtls.c -o CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c > CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosocks.c -o CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c > CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/picosplay.c -o CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/port_blocking.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/port_blocking.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/port_blocking.c > CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/port_blocking.c -o CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/prague.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/prague.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/prague.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/prague.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/prague.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/prague.c > CMakeFiles/picoquic-core.dir/picoquic/prague.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/prague.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/prague.c -o CMakeFiles/picoquic-core.dir/picoquic/prague.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c > CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/quicctx.c -o CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/register_all_cc_algorithms.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/register_all_cc_algorithms.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/register_all_cc_algorithms.c > CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/register_all_cc_algorithms.c -o CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/sacks.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c > CMakeFiles/picoquic-core.dir/picoquic/sacks.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/sacks.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sacks.c -o CMakeFiles/picoquic-core.dir/picoquic/sacks.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/sender.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/sender.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/sender.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c > CMakeFiles/picoquic-core.dir/picoquic/sender.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/sender.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sender.c -o CMakeFiles/picoquic-core.dir/picoquic/sender.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sim_link.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sim_link.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sim_link.c > CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sim_link.c -o CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/siphash.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c > CMakeFiles/picoquic-core.dir/picoquic/siphash.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/siphash.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/siphash.c -o CMakeFiles/picoquic-core.dir/picoquic/siphash.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c > CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/sockloop.c -o CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/spinbit.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/spinbit.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/spinbit.c > CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/spinbit.c -o CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c > CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/ticket_store.c -o CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/timing.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/timing.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/timing.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c > CMakeFiles/picoquic-core.dir/picoquic/timing.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/timing.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/timing.c -o CMakeFiles/picoquic-core.dir/picoquic/timing.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/token_store.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c > CMakeFiles/picoquic-core.dir/picoquic/token_store.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/token_store.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/token_store.c -o CMakeFiles/picoquic-core.dir/picoquic/token_store.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/tls_api.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/tls_api.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/tls_api.c > CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/tls_api.c -o CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/transport.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/transport.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/transport.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c > CMakeFiles/picoquic-core.dir/picoquic/transport.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/transport.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/transport.c -o CMakeFiles/picoquic-core.dir/picoquic/transport.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/unified_log.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/unified_log.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/unified_log.c > CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/unified_log.c -o CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.s

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/flags.make
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.o: /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c
third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.o: third_party/picoquic/CMakeFiles/picoquic-core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building C object third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.o"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.o -MF CMakeFiles/picoquic-core.dir/picoquic/util.c.o.d -o CMakeFiles/picoquic-core.dir/picoquic/util.c.o -c /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/picoquic-core.dir/picoquic/util.c.i"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c > CMakeFiles/picoquic-core.dir/picoquic/util.c.i

third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/picoquic-core.dir/picoquic/util.c.s"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic/picoquic/util.c -o CMakeFiles/picoquic-core.dir/picoquic/util.c.s

# Object files for target picoquic-core
picoquic__core_OBJECTS = \
"CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/config.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/ech.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/frames.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/logger.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/packet.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/paths.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/prague.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/sender.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/timing.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/transport.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o" \
"CMakeFiles/picoquic-core.dir/picoquic/util.c.o"

# External object files for target picoquic-core
picoquic__core_EXTERNAL_OBJECTS =

third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.o
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make
third_party/picoquic/libpicoquic-core.a: third_party/picoquic/CMakeFiles/picoquic-core.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Linking C static library libpicoquic-core.a"
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -P CMakeFiles/picoquic-core.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/picoquic-core.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
third_party/picoquic/CMakeFiles/picoquic-core.dir/build: third_party/picoquic/libpicoquic-core.a
.PHONY : third_party/picoquic/CMakeFiles/picoquic-core.dir/build

third_party/picoquic/CMakeFiles/picoquic-core.dir/clean:
	cd /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic && $(CMAKE_COMMAND) -P CMakeFiles/picoquic-core.dir/cmake_clean.cmake
.PHONY : third_party/picoquic/CMakeFiles/picoquic-core.dir/clean

third_party/picoquic/CMakeFiles/picoquic-core.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/stub /Users/<USER>/Documents/augment-projects/stub/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic/CMakeFiles/picoquic-core.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : third_party/picoquic/CMakeFiles/picoquic-core.dir/depend

