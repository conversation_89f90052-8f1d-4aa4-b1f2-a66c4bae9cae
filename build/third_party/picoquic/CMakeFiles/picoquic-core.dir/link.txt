/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar qc libpicoquic-core.a "CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o" "CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o" "CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o" "CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o" "CMakeFiles/picoquic-core.dir/picoquic/config.c.o" "CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o" "CMakeFiles/picoquic-core.dir/picoquic/ech.c.o" "CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o" "CMakeFiles/picoquic-core.dir/picoquic/frames.c.o" "CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o" "CMakeFiles/picoquic-core.dir/picoquic/logger.c.o" "CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o" "CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o" "CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o" "CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o" "CMakeFiles/picoquic-core.dir/picoquic/packet.c.o" "CMakeFiles/picoquic-core.dir/picoquic/paths.c.o" "CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o" "CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o" "CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o" "CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o" "CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o" "CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o" "CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o" "CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o" "CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o" "CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o" "CMakeFiles/picoquic-core.dir/picoquic/prague.c.o" "CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o" "CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o" "CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o" "CMakeFiles/picoquic-core.dir/picoquic/sender.c.o" "CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o" "CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o" "CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o" "CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o" "CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o" "CMakeFiles/picoquic-core.dir/picoquic/timing.c.o" "CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o" "CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o" "CMakeFiles/picoquic-core.dir/picoquic/transport.c.o" "CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o" "CMakeFiles/picoquic-core.dir/picoquic/util.c.o"
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib libpicoquic-core.a
