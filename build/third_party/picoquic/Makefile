# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/stub

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/stub/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/homebrew/Cellar/cmake/3.26.3/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles /Users/<USER>/Documents/augment-projects/stub/build/third_party/picoquic//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/stub/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
third_party/picoquic/CMakeFiles/picoquic-core.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picoquic-core.dir/rule
.PHONY : third_party/picoquic/CMakeFiles/picoquic-core.dir/rule

# Convenience name for target.
picoquic-core: third_party/picoquic/CMakeFiles/picoquic-core.dir/rule
.PHONY : picoquic-core

# fast build rule for target.
picoquic-core/fast:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/build
.PHONY : picoquic-core/fast

# Convenience name for target.
third_party/picoquic/CMakeFiles/picoquic-log.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picoquic-log.dir/rule
.PHONY : third_party/picoquic/CMakeFiles/picoquic-log.dir/rule

# Convenience name for target.
picoquic-log: third_party/picoquic/CMakeFiles/picoquic-log.dir/rule
.PHONY : picoquic-log

# fast build rule for target.
picoquic-log/fast:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/build
.PHONY : picoquic-log/fast

# Convenience name for target.
third_party/picoquic/CMakeFiles/picohttp-core.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picohttp-core.dir/rule
.PHONY : third_party/picoquic/CMakeFiles/picohttp-core.dir/rule

# Convenience name for target.
picohttp-core: third_party/picoquic/CMakeFiles/picohttp-core.dir/rule
.PHONY : picohttp-core

# fast build rule for target.
picohttp-core/fast:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/build
.PHONY : picohttp-core/fast

# Convenience name for target.
third_party/picoquic/CMakeFiles/picoquicdemo.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picoquicdemo.dir/rule
.PHONY : third_party/picoquic/CMakeFiles/picoquicdemo.dir/rule

# Convenience name for target.
picoquicdemo: third_party/picoquic/CMakeFiles/picoquicdemo.dir/rule
.PHONY : picoquicdemo

# fast build rule for target.
picoquicdemo/fast:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/build
.PHONY : picoquicdemo/fast

# Convenience name for target.
third_party/picoquic/CMakeFiles/picolog_t.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/picolog_t.dir/rule
.PHONY : third_party/picoquic/CMakeFiles/picolog_t.dir/rule

# Convenience name for target.
picolog_t: third_party/picoquic/CMakeFiles/picolog_t.dir/rule
.PHONY : picolog_t

# fast build rule for target.
picolog_t/fast:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picolog_t.dir/build.make third_party/picoquic/CMakeFiles/picolog_t.dir/build
.PHONY : picolog_t/fast

# Convenience name for target.
third_party/picoquic/CMakeFiles/clangformat.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third_party/picoquic/CMakeFiles/clangformat.dir/rule
.PHONY : third_party/picoquic/CMakeFiles/clangformat.dir/rule

# Convenience name for target.
clangformat: third_party/picoquic/CMakeFiles/clangformat.dir/rule
.PHONY : clangformat

# fast build rule for target.
clangformat/fast:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/clangformat.dir/build.make third_party/picoquic/CMakeFiles/clangformat.dir/build
.PHONY : clangformat/fast

loglib/autoqlog.o: loglib/autoqlog.c.o
.PHONY : loglib/autoqlog.o

# target to build an object file
loglib/autoqlog.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.o
.PHONY : loglib/autoqlog.c.o

loglib/autoqlog.i: loglib/autoqlog.c.i
.PHONY : loglib/autoqlog.i

# target to preprocess a source file
loglib/autoqlog.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.i
.PHONY : loglib/autoqlog.c.i

loglib/autoqlog.s: loglib/autoqlog.c.s
.PHONY : loglib/autoqlog.s

# target to generate assembly for a file
loglib/autoqlog.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/autoqlog.c.s
.PHONY : loglib/autoqlog.c.s

loglib/cidset.o: loglib/cidset.c.o
.PHONY : loglib/cidset.o

# target to build an object file
loglib/cidset.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.o
.PHONY : loglib/cidset.c.o

loglib/cidset.i: loglib/cidset.c.i
.PHONY : loglib/cidset.i

# target to preprocess a source file
loglib/cidset.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.i
.PHONY : loglib/cidset.c.i

loglib/cidset.s: loglib/cidset.c.s
.PHONY : loglib/cidset.s

# target to generate assembly for a file
loglib/cidset.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/cidset.c.s
.PHONY : loglib/cidset.c.s

loglib/csv.o: loglib/csv.c.o
.PHONY : loglib/csv.o

# target to build an object file
loglib/csv.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.o
.PHONY : loglib/csv.c.o

loglib/csv.i: loglib/csv.c.i
.PHONY : loglib/csv.i

# target to preprocess a source file
loglib/csv.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.i
.PHONY : loglib/csv.c.i

loglib/csv.s: loglib/csv.c.s
.PHONY : loglib/csv.s

# target to generate assembly for a file
loglib/csv.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/csv.c.s
.PHONY : loglib/csv.c.s

loglib/logconvert.o: loglib/logconvert.c.o
.PHONY : loglib/logconvert.o

# target to build an object file
loglib/logconvert.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.o
.PHONY : loglib/logconvert.c.o

loglib/logconvert.i: loglib/logconvert.c.i
.PHONY : loglib/logconvert.i

# target to preprocess a source file
loglib/logconvert.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.i
.PHONY : loglib/logconvert.c.i

loglib/logconvert.s: loglib/logconvert.c.s
.PHONY : loglib/logconvert.s

# target to generate assembly for a file
loglib/logconvert.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logconvert.c.s
.PHONY : loglib/logconvert.c.s

loglib/logreader.o: loglib/logreader.c.o
.PHONY : loglib/logreader.o

# target to build an object file
loglib/logreader.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.o
.PHONY : loglib/logreader.c.o

loglib/logreader.i: loglib/logreader.c.i
.PHONY : loglib/logreader.i

# target to preprocess a source file
loglib/logreader.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.i
.PHONY : loglib/logreader.c.i

loglib/logreader.s: loglib/logreader.c.s
.PHONY : loglib/logreader.s

# target to generate assembly for a file
loglib/logreader.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/logreader.c.s
.PHONY : loglib/logreader.c.s

loglib/memory_log.o: loglib/memory_log.c.o
.PHONY : loglib/memory_log.o

# target to build an object file
loglib/memory_log.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.o
.PHONY : loglib/memory_log.c.o

loglib/memory_log.i: loglib/memory_log.c.i
.PHONY : loglib/memory_log.i

# target to preprocess a source file
loglib/memory_log.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.i
.PHONY : loglib/memory_log.c.i

loglib/memory_log.s: loglib/memory_log.c.s
.PHONY : loglib/memory_log.s

# target to generate assembly for a file
loglib/memory_log.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/memory_log.c.s
.PHONY : loglib/memory_log.c.s

loglib/qlog.o: loglib/qlog.c.o
.PHONY : loglib/qlog.o

# target to build an object file
loglib/qlog.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.o
.PHONY : loglib/qlog.c.o

loglib/qlog.i: loglib/qlog.c.i
.PHONY : loglib/qlog.i

# target to preprocess a source file
loglib/qlog.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.i
.PHONY : loglib/qlog.c.i

loglib/qlog.s: loglib/qlog.c.s
.PHONY : loglib/qlog.s

# target to generate assembly for a file
loglib/qlog.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/qlog.c.s
.PHONY : loglib/qlog.c.s

loglib/svg.o: loglib/svg.c.o
.PHONY : loglib/svg.o

# target to build an object file
loglib/svg.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.o
.PHONY : loglib/svg.c.o

loglib/svg.i: loglib/svg.c.i
.PHONY : loglib/svg.i

# target to preprocess a source file
loglib/svg.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.i
.PHONY : loglib/svg.c.i

loglib/svg.s: loglib/svg.c.s
.PHONY : loglib/svg.s

# target to generate assembly for a file
loglib/svg.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-log.dir/build.make third_party/picoquic/CMakeFiles/picoquic-log.dir/loglib/svg.c.s
.PHONY : loglib/svg.c.s

picohttp/democlient.o: picohttp/democlient.c.o
.PHONY : picohttp/democlient.o

# target to build an object file
picohttp/democlient.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.o
.PHONY : picohttp/democlient.c.o

picohttp/democlient.i: picohttp/democlient.c.i
.PHONY : picohttp/democlient.i

# target to preprocess a source file
picohttp/democlient.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.i
.PHONY : picohttp/democlient.c.i

picohttp/democlient.s: picohttp/democlient.c.s
.PHONY : picohttp/democlient.s

# target to generate assembly for a file
picohttp/democlient.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/democlient.c.s
.PHONY : picohttp/democlient.c.s

picohttp/demoserver.o: picohttp/demoserver.c.o
.PHONY : picohttp/demoserver.o

# target to build an object file
picohttp/demoserver.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.o
.PHONY : picohttp/demoserver.c.o

picohttp/demoserver.i: picohttp/demoserver.c.i
.PHONY : picohttp/demoserver.i

# target to preprocess a source file
picohttp/demoserver.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.i
.PHONY : picohttp/demoserver.c.i

picohttp/demoserver.s: picohttp/demoserver.c.s
.PHONY : picohttp/demoserver.s

# target to generate assembly for a file
picohttp/demoserver.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/demoserver.c.s
.PHONY : picohttp/demoserver.c.s

picohttp/h3zero.o: picohttp/h3zero.c.o
.PHONY : picohttp/h3zero.o

# target to build an object file
picohttp/h3zero.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.o
.PHONY : picohttp/h3zero.c.o

picohttp/h3zero.i: picohttp/h3zero.c.i
.PHONY : picohttp/h3zero.i

# target to preprocess a source file
picohttp/h3zero.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.i
.PHONY : picohttp/h3zero.c.i

picohttp/h3zero.s: picohttp/h3zero.c.s
.PHONY : picohttp/h3zero.s

# target to generate assembly for a file
picohttp/h3zero.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero.c.s
.PHONY : picohttp/h3zero.c.s

picohttp/h3zero_client.o: picohttp/h3zero_client.c.o
.PHONY : picohttp/h3zero_client.o

# target to build an object file
picohttp/h3zero_client.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.o
.PHONY : picohttp/h3zero_client.c.o

picohttp/h3zero_client.i: picohttp/h3zero_client.c.i
.PHONY : picohttp/h3zero_client.i

# target to preprocess a source file
picohttp/h3zero_client.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.i
.PHONY : picohttp/h3zero_client.c.i

picohttp/h3zero_client.s: picohttp/h3zero_client.c.s
.PHONY : picohttp/h3zero_client.s

# target to generate assembly for a file
picohttp/h3zero_client.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_client.c.s
.PHONY : picohttp/h3zero_client.c.s

picohttp/h3zero_common.o: picohttp/h3zero_common.c.o
.PHONY : picohttp/h3zero_common.o

# target to build an object file
picohttp/h3zero_common.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.o
.PHONY : picohttp/h3zero_common.c.o

picohttp/h3zero_common.i: picohttp/h3zero_common.c.i
.PHONY : picohttp/h3zero_common.i

# target to preprocess a source file
picohttp/h3zero_common.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.i
.PHONY : picohttp/h3zero_common.c.i

picohttp/h3zero_common.s: picohttp/h3zero_common.c.s
.PHONY : picohttp/h3zero_common.s

# target to generate assembly for a file
picohttp/h3zero_common.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_common.c.s
.PHONY : picohttp/h3zero_common.c.s

picohttp/h3zero_server.o: picohttp/h3zero_server.c.o
.PHONY : picohttp/h3zero_server.o

# target to build an object file
picohttp/h3zero_server.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.o
.PHONY : picohttp/h3zero_server.c.o

picohttp/h3zero_server.i: picohttp/h3zero_server.c.i
.PHONY : picohttp/h3zero_server.i

# target to preprocess a source file
picohttp/h3zero_server.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.i
.PHONY : picohttp/h3zero_server.c.i

picohttp/h3zero_server.s: picohttp/h3zero_server.c.s
.PHONY : picohttp/h3zero_server.s

# target to generate assembly for a file
picohttp/h3zero_server.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_server.c.s
.PHONY : picohttp/h3zero_server.c.s

picohttp/h3zero_uri.o: picohttp/h3zero_uri.c.o
.PHONY : picohttp/h3zero_uri.o

# target to build an object file
picohttp/h3zero_uri.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.o
.PHONY : picohttp/h3zero_uri.c.o

picohttp/h3zero_uri.i: picohttp/h3zero_uri.c.i
.PHONY : picohttp/h3zero_uri.i

# target to preprocess a source file
picohttp/h3zero_uri.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.i
.PHONY : picohttp/h3zero_uri.c.i

picohttp/h3zero_uri.s: picohttp/h3zero_uri.c.s
.PHONY : picohttp/h3zero_uri.s

# target to generate assembly for a file
picohttp/h3zero_uri.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/h3zero_uri.c.s
.PHONY : picohttp/h3zero_uri.c.s

picohttp/quicperf.o: picohttp/quicperf.c.o
.PHONY : picohttp/quicperf.o

# target to build an object file
picohttp/quicperf.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.o
.PHONY : picohttp/quicperf.c.o

picohttp/quicperf.i: picohttp/quicperf.c.i
.PHONY : picohttp/quicperf.i

# target to preprocess a source file
picohttp/quicperf.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.i
.PHONY : picohttp/quicperf.c.i

picohttp/quicperf.s: picohttp/quicperf.c.s
.PHONY : picohttp/quicperf.s

# target to generate assembly for a file
picohttp/quicperf.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/quicperf.c.s
.PHONY : picohttp/quicperf.c.s

picohttp/webtransport.o: picohttp/webtransport.c.o
.PHONY : picohttp/webtransport.o

# target to build an object file
picohttp/webtransport.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.o
.PHONY : picohttp/webtransport.c.o

picohttp/webtransport.i: picohttp/webtransport.c.i
.PHONY : picohttp/webtransport.i

# target to preprocess a source file
picohttp/webtransport.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.i
.PHONY : picohttp/webtransport.c.i

picohttp/webtransport.s: picohttp/webtransport.c.s
.PHONY : picohttp/webtransport.s

# target to generate assembly for a file
picohttp/webtransport.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/webtransport.c.s
.PHONY : picohttp/webtransport.c.s

picohttp/wt_baton.o: picohttp/wt_baton.c.o
.PHONY : picohttp/wt_baton.o

# target to build an object file
picohttp/wt_baton.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.o
.PHONY : picohttp/wt_baton.c.o

picohttp/wt_baton.i: picohttp/wt_baton.c.i
.PHONY : picohttp/wt_baton.i

# target to preprocess a source file
picohttp/wt_baton.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.i
.PHONY : picohttp/wt_baton.c.i

picohttp/wt_baton.s: picohttp/wt_baton.c.s
.PHONY : picohttp/wt_baton.s

# target to generate assembly for a file
picohttp/wt_baton.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picohttp-core.dir/build.make third_party/picoquic/CMakeFiles/picohttp-core.dir/picohttp/wt_baton.c.s
.PHONY : picohttp/wt_baton.c.s

picolog/picolog.o: picolog/picolog.c.o
.PHONY : picolog/picolog.o

# target to build an object file
picolog/picolog.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picolog_t.dir/build.make third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.o
.PHONY : picolog/picolog.c.o

picolog/picolog.i: picolog/picolog.c.i
.PHONY : picolog/picolog.i

# target to preprocess a source file
picolog/picolog.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picolog_t.dir/build.make third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.i
.PHONY : picolog/picolog.c.i

picolog/picolog.s: picolog/picolog.c.s
.PHONY : picolog/picolog.s

# target to generate assembly for a file
picolog/picolog.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picolog_t.dir/build.make third_party/picoquic/CMakeFiles/picolog_t.dir/picolog/picolog.c.s
.PHONY : picolog/picolog.c.s

picoquic/bbr.o: picoquic/bbr.c.o
.PHONY : picoquic/bbr.o

# target to build an object file
picoquic/bbr.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.o
.PHONY : picoquic/bbr.c.o

picoquic/bbr.i: picoquic/bbr.c.i
.PHONY : picoquic/bbr.i

# target to preprocess a source file
picoquic/bbr.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.i
.PHONY : picoquic/bbr.c.i

picoquic/bbr.s: picoquic/bbr.c.s
.PHONY : picoquic/bbr.s

# target to generate assembly for a file
picoquic/bbr.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr.c.s
.PHONY : picoquic/bbr.c.s

picoquic/bbr1.o: picoquic/bbr1.c.o
.PHONY : picoquic/bbr1.o

# target to build an object file
picoquic/bbr1.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.o
.PHONY : picoquic/bbr1.c.o

picoquic/bbr1.i: picoquic/bbr1.c.i
.PHONY : picoquic/bbr1.i

# target to preprocess a source file
picoquic/bbr1.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.i
.PHONY : picoquic/bbr1.c.i

picoquic/bbr1.s: picoquic/bbr1.c.s
.PHONY : picoquic/bbr1.s

# target to generate assembly for a file
picoquic/bbr1.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bbr1.c.s
.PHONY : picoquic/bbr1.c.s

picoquic/bytestream.o: picoquic/bytestream.c.o
.PHONY : picoquic/bytestream.o

# target to build an object file
picoquic/bytestream.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.o
.PHONY : picoquic/bytestream.c.o

picoquic/bytestream.i: picoquic/bytestream.c.i
.PHONY : picoquic/bytestream.i

# target to preprocess a source file
picoquic/bytestream.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.i
.PHONY : picoquic/bytestream.c.i

picoquic/bytestream.s: picoquic/bytestream.c.s
.PHONY : picoquic/bytestream.s

# target to generate assembly for a file
picoquic/bytestream.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/bytestream.c.s
.PHONY : picoquic/bytestream.c.s

picoquic/cc_common.o: picoquic/cc_common.c.o
.PHONY : picoquic/cc_common.o

# target to build an object file
picoquic/cc_common.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.o
.PHONY : picoquic/cc_common.c.o

picoquic/cc_common.i: picoquic/cc_common.c.i
.PHONY : picoquic/cc_common.i

# target to preprocess a source file
picoquic/cc_common.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.i
.PHONY : picoquic/cc_common.c.i

picoquic/cc_common.s: picoquic/cc_common.c.s
.PHONY : picoquic/cc_common.s

# target to generate assembly for a file
picoquic/cc_common.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cc_common.c.s
.PHONY : picoquic/cc_common.c.s

picoquic/config.o: picoquic/config.c.o
.PHONY : picoquic/config.o

# target to build an object file
picoquic/config.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.o
.PHONY : picoquic/config.c.o

picoquic/config.i: picoquic/config.c.i
.PHONY : picoquic/config.i

# target to preprocess a source file
picoquic/config.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.i
.PHONY : picoquic/config.c.i

picoquic/config.s: picoquic/config.c.s
.PHONY : picoquic/config.s

# target to generate assembly for a file
picoquic/config.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/config.c.s
.PHONY : picoquic/config.c.s

picoquic/cubic.o: picoquic/cubic.c.o
.PHONY : picoquic/cubic.o

# target to build an object file
picoquic/cubic.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.o
.PHONY : picoquic/cubic.c.o

picoquic/cubic.i: picoquic/cubic.c.i
.PHONY : picoquic/cubic.i

# target to preprocess a source file
picoquic/cubic.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.i
.PHONY : picoquic/cubic.c.i

picoquic/cubic.s: picoquic/cubic.c.s
.PHONY : picoquic/cubic.s

# target to generate assembly for a file
picoquic/cubic.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/cubic.c.s
.PHONY : picoquic/cubic.c.s

picoquic/ech.o: picoquic/ech.c.o
.PHONY : picoquic/ech.o

# target to build an object file
picoquic/ech.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.o
.PHONY : picoquic/ech.c.o

picoquic/ech.i: picoquic/ech.c.i
.PHONY : picoquic/ech.i

# target to preprocess a source file
picoquic/ech.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.i
.PHONY : picoquic/ech.c.i

picoquic/ech.s: picoquic/ech.c.s
.PHONY : picoquic/ech.s

# target to generate assembly for a file
picoquic/ech.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ech.c.s
.PHONY : picoquic/ech.c.s

picoquic/fastcc.o: picoquic/fastcc.c.o
.PHONY : picoquic/fastcc.o

# target to build an object file
picoquic/fastcc.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.o
.PHONY : picoquic/fastcc.c.o

picoquic/fastcc.i: picoquic/fastcc.c.i
.PHONY : picoquic/fastcc.i

# target to preprocess a source file
picoquic/fastcc.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.i
.PHONY : picoquic/fastcc.c.i

picoquic/fastcc.s: picoquic/fastcc.c.s
.PHONY : picoquic/fastcc.s

# target to generate assembly for a file
picoquic/fastcc.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/fastcc.c.s
.PHONY : picoquic/fastcc.c.s

picoquic/frames.o: picoquic/frames.c.o
.PHONY : picoquic/frames.o

# target to build an object file
picoquic/frames.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.o
.PHONY : picoquic/frames.c.o

picoquic/frames.i: picoquic/frames.c.i
.PHONY : picoquic/frames.i

# target to preprocess a source file
picoquic/frames.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.i
.PHONY : picoquic/frames.c.i

picoquic/frames.s: picoquic/frames.c.s
.PHONY : picoquic/frames.s

# target to generate assembly for a file
picoquic/frames.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/frames.c.s
.PHONY : picoquic/frames.c.s

picoquic/intformat.o: picoquic/intformat.c.o
.PHONY : picoquic/intformat.o

# target to build an object file
picoquic/intformat.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.o
.PHONY : picoquic/intformat.c.o

picoquic/intformat.i: picoquic/intformat.c.i
.PHONY : picoquic/intformat.i

# target to preprocess a source file
picoquic/intformat.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.i
.PHONY : picoquic/intformat.c.i

picoquic/intformat.s: picoquic/intformat.c.s
.PHONY : picoquic/intformat.s

# target to generate assembly for a file
picoquic/intformat.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/intformat.c.s
.PHONY : picoquic/intformat.c.s

picoquic/logger.o: picoquic/logger.c.o
.PHONY : picoquic/logger.o

# target to build an object file
picoquic/logger.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.o
.PHONY : picoquic/logger.c.o

picoquic/logger.i: picoquic/logger.c.i
.PHONY : picoquic/logger.i

# target to preprocess a source file
picoquic/logger.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.i
.PHONY : picoquic/logger.c.i

picoquic/logger.s: picoquic/logger.c.s
.PHONY : picoquic/logger.s

# target to generate assembly for a file
picoquic/logger.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logger.c.s
.PHONY : picoquic/logger.c.s

picoquic/logwriter.o: picoquic/logwriter.c.o
.PHONY : picoquic/logwriter.o

# target to build an object file
picoquic/logwriter.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.o
.PHONY : picoquic/logwriter.c.o

picoquic/logwriter.i: picoquic/logwriter.c.i
.PHONY : picoquic/logwriter.i

# target to preprocess a source file
picoquic/logwriter.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.i
.PHONY : picoquic/logwriter.c.i

picoquic/logwriter.s: picoquic/logwriter.c.s
.PHONY : picoquic/logwriter.s

# target to generate assembly for a file
picoquic/logwriter.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/logwriter.c.s
.PHONY : picoquic/logwriter.c.s

picoquic/loss_recovery.o: picoquic/loss_recovery.c.o
.PHONY : picoquic/loss_recovery.o

# target to build an object file
picoquic/loss_recovery.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.o
.PHONY : picoquic/loss_recovery.c.o

picoquic/loss_recovery.i: picoquic/loss_recovery.c.i
.PHONY : picoquic/loss_recovery.i

# target to preprocess a source file
picoquic/loss_recovery.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.i
.PHONY : picoquic/loss_recovery.c.i

picoquic/loss_recovery.s: picoquic/loss_recovery.c.s
.PHONY : picoquic/loss_recovery.s

# target to generate assembly for a file
picoquic/loss_recovery.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/loss_recovery.c.s
.PHONY : picoquic/loss_recovery.c.s

picoquic/newreno.o: picoquic/newreno.c.o
.PHONY : picoquic/newreno.o

# target to build an object file
picoquic/newreno.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.o
.PHONY : picoquic/newreno.c.o

picoquic/newreno.i: picoquic/newreno.c.i
.PHONY : picoquic/newreno.i

# target to preprocess a source file
picoquic/newreno.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.i
.PHONY : picoquic/newreno.c.i

picoquic/newreno.s: picoquic/newreno.c.s
.PHONY : picoquic/newreno.s

# target to generate assembly for a file
picoquic/newreno.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/newreno.c.s
.PHONY : picoquic/newreno.c.s

picoquic/pacing.o: picoquic/pacing.c.o
.PHONY : picoquic/pacing.o

# target to build an object file
picoquic/pacing.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.o
.PHONY : picoquic/pacing.c.o

picoquic/pacing.i: picoquic/pacing.c.i
.PHONY : picoquic/pacing.i

# target to preprocess a source file
picoquic/pacing.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.i
.PHONY : picoquic/pacing.c.i

picoquic/pacing.s: picoquic/pacing.c.s
.PHONY : picoquic/pacing.s

# target to generate assembly for a file
picoquic/pacing.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/pacing.c.s
.PHONY : picoquic/pacing.c.s

picoquic/packet.o: picoquic/packet.c.o
.PHONY : picoquic/packet.o

# target to build an object file
picoquic/packet.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.o
.PHONY : picoquic/packet.c.o

picoquic/packet.i: picoquic/packet.c.i
.PHONY : picoquic/packet.i

# target to preprocess a source file
picoquic/packet.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.i
.PHONY : picoquic/packet.c.i

picoquic/packet.s: picoquic/packet.c.s
.PHONY : picoquic/packet.s

# target to generate assembly for a file
picoquic/packet.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/packet.c.s
.PHONY : picoquic/packet.c.s

picoquic/paths.o: picoquic/paths.c.o
.PHONY : picoquic/paths.o

# target to build an object file
picoquic/paths.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.o
.PHONY : picoquic/paths.c.o

picoquic/paths.i: picoquic/paths.c.i
.PHONY : picoquic/paths.i

# target to preprocess a source file
picoquic/paths.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.i
.PHONY : picoquic/paths.c.i

picoquic/paths.s: picoquic/paths.c.s
.PHONY : picoquic/paths.s

# target to generate assembly for a file
picoquic/paths.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/paths.c.s
.PHONY : picoquic/paths.c.s

picoquic/performance_log.o: picoquic/performance_log.c.o
.PHONY : picoquic/performance_log.o

# target to build an object file
picoquic/performance_log.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.o
.PHONY : picoquic/performance_log.c.o

picoquic/performance_log.i: picoquic/performance_log.c.i
.PHONY : picoquic/performance_log.i

# target to preprocess a source file
picoquic/performance_log.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.i
.PHONY : picoquic/performance_log.c.i

picoquic/performance_log.s: picoquic/performance_log.c.s
.PHONY : picoquic/performance_log.s

# target to generate assembly for a file
picoquic/performance_log.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/performance_log.c.s
.PHONY : picoquic/performance_log.c.s

picoquic/picohash.o: picoquic/picohash.c.o
.PHONY : picoquic/picohash.o

# target to build an object file
picoquic/picohash.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.o
.PHONY : picoquic/picohash.c.o

picoquic/picohash.i: picoquic/picohash.c.i
.PHONY : picoquic/picohash.i

# target to preprocess a source file
picoquic/picohash.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.i
.PHONY : picoquic/picohash.c.i

picoquic/picohash.s: picoquic/picohash.c.s
.PHONY : picoquic/picohash.s

# target to generate assembly for a file
picoquic/picohash.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picohash.c.s
.PHONY : picoquic/picohash.c.s

picoquic/picoquic_lb.o: picoquic/picoquic_lb.c.o
.PHONY : picoquic/picoquic_lb.o

# target to build an object file
picoquic/picoquic_lb.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.o
.PHONY : picoquic/picoquic_lb.c.o

picoquic/picoquic_lb.i: picoquic/picoquic_lb.c.i
.PHONY : picoquic/picoquic_lb.i

# target to preprocess a source file
picoquic/picoquic_lb.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.i
.PHONY : picoquic/picoquic_lb.c.i

picoquic/picoquic_lb.s: picoquic/picoquic_lb.c.s
.PHONY : picoquic/picoquic_lb.s

# target to generate assembly for a file
picoquic/picoquic_lb.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_lb.c.s
.PHONY : picoquic/picoquic_lb.c.s

picoquic/picoquic_mbedtls.o: picoquic/picoquic_mbedtls.c.o
.PHONY : picoquic/picoquic_mbedtls.o

# target to build an object file
picoquic/picoquic_mbedtls.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.o
.PHONY : picoquic/picoquic_mbedtls.c.o

picoquic/picoquic_mbedtls.i: picoquic/picoquic_mbedtls.c.i
.PHONY : picoquic/picoquic_mbedtls.i

# target to preprocess a source file
picoquic/picoquic_mbedtls.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.i
.PHONY : picoquic/picoquic_mbedtls.c.i

picoquic/picoquic_mbedtls.s: picoquic/picoquic_mbedtls.c.s
.PHONY : picoquic/picoquic_mbedtls.s

# target to generate assembly for a file
picoquic/picoquic_mbedtls.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_mbedtls.c.s
.PHONY : picoquic/picoquic_mbedtls.c.s

picoquic/picoquic_ptls_fusion.o: picoquic/picoquic_ptls_fusion.c.o
.PHONY : picoquic/picoquic_ptls_fusion.o

# target to build an object file
picoquic/picoquic_ptls_fusion.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.o
.PHONY : picoquic/picoquic_ptls_fusion.c.o

picoquic/picoquic_ptls_fusion.i: picoquic/picoquic_ptls_fusion.c.i
.PHONY : picoquic/picoquic_ptls_fusion.i

# target to preprocess a source file
picoquic/picoquic_ptls_fusion.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.i
.PHONY : picoquic/picoquic_ptls_fusion.c.i

picoquic/picoquic_ptls_fusion.s: picoquic/picoquic_ptls_fusion.c.s
.PHONY : picoquic/picoquic_ptls_fusion.s

# target to generate assembly for a file
picoquic/picoquic_ptls_fusion.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_fusion.c.s
.PHONY : picoquic/picoquic_ptls_fusion.c.s

picoquic/picoquic_ptls_minicrypto.o: picoquic/picoquic_ptls_minicrypto.c.o
.PHONY : picoquic/picoquic_ptls_minicrypto.o

# target to build an object file
picoquic/picoquic_ptls_minicrypto.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.o
.PHONY : picoquic/picoquic_ptls_minicrypto.c.o

picoquic/picoquic_ptls_minicrypto.i: picoquic/picoquic_ptls_minicrypto.c.i
.PHONY : picoquic/picoquic_ptls_minicrypto.i

# target to preprocess a source file
picoquic/picoquic_ptls_minicrypto.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.i
.PHONY : picoquic/picoquic_ptls_minicrypto.c.i

picoquic/picoquic_ptls_minicrypto.s: picoquic/picoquic_ptls_minicrypto.c.s
.PHONY : picoquic/picoquic_ptls_minicrypto.s

# target to generate assembly for a file
picoquic/picoquic_ptls_minicrypto.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_minicrypto.c.s
.PHONY : picoquic/picoquic_ptls_minicrypto.c.s

picoquic/picoquic_ptls_openssl.o: picoquic/picoquic_ptls_openssl.c.o
.PHONY : picoquic/picoquic_ptls_openssl.o

# target to build an object file
picoquic/picoquic_ptls_openssl.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.o
.PHONY : picoquic/picoquic_ptls_openssl.c.o

picoquic/picoquic_ptls_openssl.i: picoquic/picoquic_ptls_openssl.c.i
.PHONY : picoquic/picoquic_ptls_openssl.i

# target to preprocess a source file
picoquic/picoquic_ptls_openssl.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.i
.PHONY : picoquic/picoquic_ptls_openssl.c.i

picoquic/picoquic_ptls_openssl.s: picoquic/picoquic_ptls_openssl.c.s
.PHONY : picoquic/picoquic_ptls_openssl.s

# target to generate assembly for a file
picoquic/picoquic_ptls_openssl.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picoquic_ptls_openssl.c.s
.PHONY : picoquic/picoquic_ptls_openssl.c.s

picoquic/picosocks.o: picoquic/picosocks.c.o
.PHONY : picoquic/picosocks.o

# target to build an object file
picoquic/picosocks.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.o
.PHONY : picoquic/picosocks.c.o

picoquic/picosocks.i: picoquic/picosocks.c.i
.PHONY : picoquic/picosocks.i

# target to preprocess a source file
picoquic/picosocks.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.i
.PHONY : picoquic/picosocks.c.i

picoquic/picosocks.s: picoquic/picosocks.c.s
.PHONY : picoquic/picosocks.s

# target to generate assembly for a file
picoquic/picosocks.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosocks.c.s
.PHONY : picoquic/picosocks.c.s

picoquic/picosplay.o: picoquic/picosplay.c.o
.PHONY : picoquic/picosplay.o

# target to build an object file
picoquic/picosplay.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.o
.PHONY : picoquic/picosplay.c.o

picoquic/picosplay.i: picoquic/picosplay.c.i
.PHONY : picoquic/picosplay.i

# target to preprocess a source file
picoquic/picosplay.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.i
.PHONY : picoquic/picosplay.c.i

picoquic/picosplay.s: picoquic/picosplay.c.s
.PHONY : picoquic/picosplay.s

# target to generate assembly for a file
picoquic/picosplay.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/picosplay.c.s
.PHONY : picoquic/picosplay.c.s

picoquic/port_blocking.o: picoquic/port_blocking.c.o
.PHONY : picoquic/port_blocking.o

# target to build an object file
picoquic/port_blocking.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.o
.PHONY : picoquic/port_blocking.c.o

picoquic/port_blocking.i: picoquic/port_blocking.c.i
.PHONY : picoquic/port_blocking.i

# target to preprocess a source file
picoquic/port_blocking.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.i
.PHONY : picoquic/port_blocking.c.i

picoquic/port_blocking.s: picoquic/port_blocking.c.s
.PHONY : picoquic/port_blocking.s

# target to generate assembly for a file
picoquic/port_blocking.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/port_blocking.c.s
.PHONY : picoquic/port_blocking.c.s

picoquic/prague.o: picoquic/prague.c.o
.PHONY : picoquic/prague.o

# target to build an object file
picoquic/prague.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.o
.PHONY : picoquic/prague.c.o

picoquic/prague.i: picoquic/prague.c.i
.PHONY : picoquic/prague.i

# target to preprocess a source file
picoquic/prague.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.i
.PHONY : picoquic/prague.c.i

picoquic/prague.s: picoquic/prague.c.s
.PHONY : picoquic/prague.s

# target to generate assembly for a file
picoquic/prague.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/prague.c.s
.PHONY : picoquic/prague.c.s

picoquic/quicctx.o: picoquic/quicctx.c.o
.PHONY : picoquic/quicctx.o

# target to build an object file
picoquic/quicctx.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.o
.PHONY : picoquic/quicctx.c.o

picoquic/quicctx.i: picoquic/quicctx.c.i
.PHONY : picoquic/quicctx.i

# target to preprocess a source file
picoquic/quicctx.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.i
.PHONY : picoquic/quicctx.c.i

picoquic/quicctx.s: picoquic/quicctx.c.s
.PHONY : picoquic/quicctx.s

# target to generate assembly for a file
picoquic/quicctx.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/quicctx.c.s
.PHONY : picoquic/quicctx.c.s

picoquic/register_all_cc_algorithms.o: picoquic/register_all_cc_algorithms.c.o
.PHONY : picoquic/register_all_cc_algorithms.o

# target to build an object file
picoquic/register_all_cc_algorithms.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.o
.PHONY : picoquic/register_all_cc_algorithms.c.o

picoquic/register_all_cc_algorithms.i: picoquic/register_all_cc_algorithms.c.i
.PHONY : picoquic/register_all_cc_algorithms.i

# target to preprocess a source file
picoquic/register_all_cc_algorithms.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.i
.PHONY : picoquic/register_all_cc_algorithms.c.i

picoquic/register_all_cc_algorithms.s: picoquic/register_all_cc_algorithms.c.s
.PHONY : picoquic/register_all_cc_algorithms.s

# target to generate assembly for a file
picoquic/register_all_cc_algorithms.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/register_all_cc_algorithms.c.s
.PHONY : picoquic/register_all_cc_algorithms.c.s

picoquic/sacks.o: picoquic/sacks.c.o
.PHONY : picoquic/sacks.o

# target to build an object file
picoquic/sacks.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.o
.PHONY : picoquic/sacks.c.o

picoquic/sacks.i: picoquic/sacks.c.i
.PHONY : picoquic/sacks.i

# target to preprocess a source file
picoquic/sacks.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.i
.PHONY : picoquic/sacks.c.i

picoquic/sacks.s: picoquic/sacks.c.s
.PHONY : picoquic/sacks.s

# target to generate assembly for a file
picoquic/sacks.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sacks.c.s
.PHONY : picoquic/sacks.c.s

picoquic/sender.o: picoquic/sender.c.o
.PHONY : picoquic/sender.o

# target to build an object file
picoquic/sender.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.o
.PHONY : picoquic/sender.c.o

picoquic/sender.i: picoquic/sender.c.i
.PHONY : picoquic/sender.i

# target to preprocess a source file
picoquic/sender.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.i
.PHONY : picoquic/sender.c.i

picoquic/sender.s: picoquic/sender.c.s
.PHONY : picoquic/sender.s

# target to generate assembly for a file
picoquic/sender.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sender.c.s
.PHONY : picoquic/sender.c.s

picoquic/sim_link.o: picoquic/sim_link.c.o
.PHONY : picoquic/sim_link.o

# target to build an object file
picoquic/sim_link.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.o
.PHONY : picoquic/sim_link.c.o

picoquic/sim_link.i: picoquic/sim_link.c.i
.PHONY : picoquic/sim_link.i

# target to preprocess a source file
picoquic/sim_link.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.i
.PHONY : picoquic/sim_link.c.i

picoquic/sim_link.s: picoquic/sim_link.c.s
.PHONY : picoquic/sim_link.s

# target to generate assembly for a file
picoquic/sim_link.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sim_link.c.s
.PHONY : picoquic/sim_link.c.s

picoquic/siphash.o: picoquic/siphash.c.o
.PHONY : picoquic/siphash.o

# target to build an object file
picoquic/siphash.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.o
.PHONY : picoquic/siphash.c.o

picoquic/siphash.i: picoquic/siphash.c.i
.PHONY : picoquic/siphash.i

# target to preprocess a source file
picoquic/siphash.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.i
.PHONY : picoquic/siphash.c.i

picoquic/siphash.s: picoquic/siphash.c.s
.PHONY : picoquic/siphash.s

# target to generate assembly for a file
picoquic/siphash.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/siphash.c.s
.PHONY : picoquic/siphash.c.s

picoquic/sockloop.o: picoquic/sockloop.c.o
.PHONY : picoquic/sockloop.o

# target to build an object file
picoquic/sockloop.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.o
.PHONY : picoquic/sockloop.c.o

picoquic/sockloop.i: picoquic/sockloop.c.i
.PHONY : picoquic/sockloop.i

# target to preprocess a source file
picoquic/sockloop.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.i
.PHONY : picoquic/sockloop.c.i

picoquic/sockloop.s: picoquic/sockloop.c.s
.PHONY : picoquic/sockloop.s

# target to generate assembly for a file
picoquic/sockloop.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/sockloop.c.s
.PHONY : picoquic/sockloop.c.s

picoquic/spinbit.o: picoquic/spinbit.c.o
.PHONY : picoquic/spinbit.o

# target to build an object file
picoquic/spinbit.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.o
.PHONY : picoquic/spinbit.c.o

picoquic/spinbit.i: picoquic/spinbit.c.i
.PHONY : picoquic/spinbit.i

# target to preprocess a source file
picoquic/spinbit.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.i
.PHONY : picoquic/spinbit.c.i

picoquic/spinbit.s: picoquic/spinbit.c.s
.PHONY : picoquic/spinbit.s

# target to generate assembly for a file
picoquic/spinbit.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/spinbit.c.s
.PHONY : picoquic/spinbit.c.s

picoquic/ticket_store.o: picoquic/ticket_store.c.o
.PHONY : picoquic/ticket_store.o

# target to build an object file
picoquic/ticket_store.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.o
.PHONY : picoquic/ticket_store.c.o

picoquic/ticket_store.i: picoquic/ticket_store.c.i
.PHONY : picoquic/ticket_store.i

# target to preprocess a source file
picoquic/ticket_store.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.i
.PHONY : picoquic/ticket_store.c.i

picoquic/ticket_store.s: picoquic/ticket_store.c.s
.PHONY : picoquic/ticket_store.s

# target to generate assembly for a file
picoquic/ticket_store.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/ticket_store.c.s
.PHONY : picoquic/ticket_store.c.s

picoquic/timing.o: picoquic/timing.c.o
.PHONY : picoquic/timing.o

# target to build an object file
picoquic/timing.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.o
.PHONY : picoquic/timing.c.o

picoquic/timing.i: picoquic/timing.c.i
.PHONY : picoquic/timing.i

# target to preprocess a source file
picoquic/timing.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.i
.PHONY : picoquic/timing.c.i

picoquic/timing.s: picoquic/timing.c.s
.PHONY : picoquic/timing.s

# target to generate assembly for a file
picoquic/timing.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/timing.c.s
.PHONY : picoquic/timing.c.s

picoquic/tls_api.o: picoquic/tls_api.c.o
.PHONY : picoquic/tls_api.o

# target to build an object file
picoquic/tls_api.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.o
.PHONY : picoquic/tls_api.c.o

picoquic/tls_api.i: picoquic/tls_api.c.i
.PHONY : picoquic/tls_api.i

# target to preprocess a source file
picoquic/tls_api.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.i
.PHONY : picoquic/tls_api.c.i

picoquic/tls_api.s: picoquic/tls_api.c.s
.PHONY : picoquic/tls_api.s

# target to generate assembly for a file
picoquic/tls_api.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/tls_api.c.s
.PHONY : picoquic/tls_api.c.s

picoquic/token_store.o: picoquic/token_store.c.o
.PHONY : picoquic/token_store.o

# target to build an object file
picoquic/token_store.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.o
.PHONY : picoquic/token_store.c.o

picoquic/token_store.i: picoquic/token_store.c.i
.PHONY : picoquic/token_store.i

# target to preprocess a source file
picoquic/token_store.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.i
.PHONY : picoquic/token_store.c.i

picoquic/token_store.s: picoquic/token_store.c.s
.PHONY : picoquic/token_store.s

# target to generate assembly for a file
picoquic/token_store.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/token_store.c.s
.PHONY : picoquic/token_store.c.s

picoquic/transport.o: picoquic/transport.c.o
.PHONY : picoquic/transport.o

# target to build an object file
picoquic/transport.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.o
.PHONY : picoquic/transport.c.o

picoquic/transport.i: picoquic/transport.c.i
.PHONY : picoquic/transport.i

# target to preprocess a source file
picoquic/transport.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.i
.PHONY : picoquic/transport.c.i

picoquic/transport.s: picoquic/transport.c.s
.PHONY : picoquic/transport.s

# target to generate assembly for a file
picoquic/transport.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/transport.c.s
.PHONY : picoquic/transport.c.s

picoquic/unified_log.o: picoquic/unified_log.c.o
.PHONY : picoquic/unified_log.o

# target to build an object file
picoquic/unified_log.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.o
.PHONY : picoquic/unified_log.c.o

picoquic/unified_log.i: picoquic/unified_log.c.i
.PHONY : picoquic/unified_log.i

# target to preprocess a source file
picoquic/unified_log.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.i
.PHONY : picoquic/unified_log.c.i

picoquic/unified_log.s: picoquic/unified_log.c.s
.PHONY : picoquic/unified_log.s

# target to generate assembly for a file
picoquic/unified_log.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/unified_log.c.s
.PHONY : picoquic/unified_log.c.s

picoquic/util.o: picoquic/util.c.o
.PHONY : picoquic/util.o

# target to build an object file
picoquic/util.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.o
.PHONY : picoquic/util.c.o

picoquic/util.i: picoquic/util.c.i
.PHONY : picoquic/util.i

# target to preprocess a source file
picoquic/util.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.i
.PHONY : picoquic/util.c.i

picoquic/util.s: picoquic/util.c.s
.PHONY : picoquic/util.s

# target to generate assembly for a file
picoquic/util.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquic-core.dir/build.make third_party/picoquic/CMakeFiles/picoquic-core.dir/picoquic/util.c.s
.PHONY : picoquic/util.c.s

picoquicfirst/getopt.o: picoquicfirst/getopt.c.o
.PHONY : picoquicfirst/getopt.o

# target to build an object file
picoquicfirst/getopt.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.o
.PHONY : picoquicfirst/getopt.c.o

picoquicfirst/getopt.i: picoquicfirst/getopt.c.i
.PHONY : picoquicfirst/getopt.i

# target to preprocess a source file
picoquicfirst/getopt.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.i
.PHONY : picoquicfirst/getopt.c.i

picoquicfirst/getopt.s: picoquicfirst/getopt.c.s
.PHONY : picoquicfirst/getopt.s

# target to generate assembly for a file
picoquicfirst/getopt.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/getopt.c.s
.PHONY : picoquicfirst/getopt.c.s

picoquicfirst/picoquicdemo.o: picoquicfirst/picoquicdemo.c.o
.PHONY : picoquicfirst/picoquicdemo.o

# target to build an object file
picoquicfirst/picoquicdemo.c.o:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.o
.PHONY : picoquicfirst/picoquicdemo.c.o

picoquicfirst/picoquicdemo.i: picoquicfirst/picoquicdemo.c.i
.PHONY : picoquicfirst/picoquicdemo.i

# target to preprocess a source file
picoquicfirst/picoquicdemo.c.i:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.i
.PHONY : picoquicfirst/picoquicdemo.c.i

picoquicfirst/picoquicdemo.s: picoquicfirst/picoquicdemo.c.s
.PHONY : picoquicfirst/picoquicdemo.s

# target to generate assembly for a file
picoquicfirst/picoquicdemo.c.s:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(MAKE) $(MAKESILENT) -f third_party/picoquic/CMakeFiles/picoquicdemo.dir/build.make third_party/picoquic/CMakeFiles/picoquicdemo.dir/picoquicfirst/picoquicdemo.c.s
.PHONY : picoquicfirst/picoquicdemo.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... clangformat"
	@echo "... picohttp-core"
	@echo "... picolog_t"
	@echo "... picoquic-core"
	@echo "... picoquic-log"
	@echo "... picoquicdemo"
	@echo "... loglib/autoqlog.o"
	@echo "... loglib/autoqlog.i"
	@echo "... loglib/autoqlog.s"
	@echo "... loglib/cidset.o"
	@echo "... loglib/cidset.i"
	@echo "... loglib/cidset.s"
	@echo "... loglib/csv.o"
	@echo "... loglib/csv.i"
	@echo "... loglib/csv.s"
	@echo "... loglib/logconvert.o"
	@echo "... loglib/logconvert.i"
	@echo "... loglib/logconvert.s"
	@echo "... loglib/logreader.o"
	@echo "... loglib/logreader.i"
	@echo "... loglib/logreader.s"
	@echo "... loglib/memory_log.o"
	@echo "... loglib/memory_log.i"
	@echo "... loglib/memory_log.s"
	@echo "... loglib/qlog.o"
	@echo "... loglib/qlog.i"
	@echo "... loglib/qlog.s"
	@echo "... loglib/svg.o"
	@echo "... loglib/svg.i"
	@echo "... loglib/svg.s"
	@echo "... picohttp/democlient.o"
	@echo "... picohttp/democlient.i"
	@echo "... picohttp/democlient.s"
	@echo "... picohttp/demoserver.o"
	@echo "... picohttp/demoserver.i"
	@echo "... picohttp/demoserver.s"
	@echo "... picohttp/h3zero.o"
	@echo "... picohttp/h3zero.i"
	@echo "... picohttp/h3zero.s"
	@echo "... picohttp/h3zero_client.o"
	@echo "... picohttp/h3zero_client.i"
	@echo "... picohttp/h3zero_client.s"
	@echo "... picohttp/h3zero_common.o"
	@echo "... picohttp/h3zero_common.i"
	@echo "... picohttp/h3zero_common.s"
	@echo "... picohttp/h3zero_server.o"
	@echo "... picohttp/h3zero_server.i"
	@echo "... picohttp/h3zero_server.s"
	@echo "... picohttp/h3zero_uri.o"
	@echo "... picohttp/h3zero_uri.i"
	@echo "... picohttp/h3zero_uri.s"
	@echo "... picohttp/quicperf.o"
	@echo "... picohttp/quicperf.i"
	@echo "... picohttp/quicperf.s"
	@echo "... picohttp/webtransport.o"
	@echo "... picohttp/webtransport.i"
	@echo "... picohttp/webtransport.s"
	@echo "... picohttp/wt_baton.o"
	@echo "... picohttp/wt_baton.i"
	@echo "... picohttp/wt_baton.s"
	@echo "... picolog/picolog.o"
	@echo "... picolog/picolog.i"
	@echo "... picolog/picolog.s"
	@echo "... picoquic/bbr.o"
	@echo "... picoquic/bbr.i"
	@echo "... picoquic/bbr.s"
	@echo "... picoquic/bbr1.o"
	@echo "... picoquic/bbr1.i"
	@echo "... picoquic/bbr1.s"
	@echo "... picoquic/bytestream.o"
	@echo "... picoquic/bytestream.i"
	@echo "... picoquic/bytestream.s"
	@echo "... picoquic/cc_common.o"
	@echo "... picoquic/cc_common.i"
	@echo "... picoquic/cc_common.s"
	@echo "... picoquic/config.o"
	@echo "... picoquic/config.i"
	@echo "... picoquic/config.s"
	@echo "... picoquic/cubic.o"
	@echo "... picoquic/cubic.i"
	@echo "... picoquic/cubic.s"
	@echo "... picoquic/ech.o"
	@echo "... picoquic/ech.i"
	@echo "... picoquic/ech.s"
	@echo "... picoquic/fastcc.o"
	@echo "... picoquic/fastcc.i"
	@echo "... picoquic/fastcc.s"
	@echo "... picoquic/frames.o"
	@echo "... picoquic/frames.i"
	@echo "... picoquic/frames.s"
	@echo "... picoquic/intformat.o"
	@echo "... picoquic/intformat.i"
	@echo "... picoquic/intformat.s"
	@echo "... picoquic/logger.o"
	@echo "... picoquic/logger.i"
	@echo "... picoquic/logger.s"
	@echo "... picoquic/logwriter.o"
	@echo "... picoquic/logwriter.i"
	@echo "... picoquic/logwriter.s"
	@echo "... picoquic/loss_recovery.o"
	@echo "... picoquic/loss_recovery.i"
	@echo "... picoquic/loss_recovery.s"
	@echo "... picoquic/newreno.o"
	@echo "... picoquic/newreno.i"
	@echo "... picoquic/newreno.s"
	@echo "... picoquic/pacing.o"
	@echo "... picoquic/pacing.i"
	@echo "... picoquic/pacing.s"
	@echo "... picoquic/packet.o"
	@echo "... picoquic/packet.i"
	@echo "... picoquic/packet.s"
	@echo "... picoquic/paths.o"
	@echo "... picoquic/paths.i"
	@echo "... picoquic/paths.s"
	@echo "... picoquic/performance_log.o"
	@echo "... picoquic/performance_log.i"
	@echo "... picoquic/performance_log.s"
	@echo "... picoquic/picohash.o"
	@echo "... picoquic/picohash.i"
	@echo "... picoquic/picohash.s"
	@echo "... picoquic/picoquic_lb.o"
	@echo "... picoquic/picoquic_lb.i"
	@echo "... picoquic/picoquic_lb.s"
	@echo "... picoquic/picoquic_mbedtls.o"
	@echo "... picoquic/picoquic_mbedtls.i"
	@echo "... picoquic/picoquic_mbedtls.s"
	@echo "... picoquic/picoquic_ptls_fusion.o"
	@echo "... picoquic/picoquic_ptls_fusion.i"
	@echo "... picoquic/picoquic_ptls_fusion.s"
	@echo "... picoquic/picoquic_ptls_minicrypto.o"
	@echo "... picoquic/picoquic_ptls_minicrypto.i"
	@echo "... picoquic/picoquic_ptls_minicrypto.s"
	@echo "... picoquic/picoquic_ptls_openssl.o"
	@echo "... picoquic/picoquic_ptls_openssl.i"
	@echo "... picoquic/picoquic_ptls_openssl.s"
	@echo "... picoquic/picosocks.o"
	@echo "... picoquic/picosocks.i"
	@echo "... picoquic/picosocks.s"
	@echo "... picoquic/picosplay.o"
	@echo "... picoquic/picosplay.i"
	@echo "... picoquic/picosplay.s"
	@echo "... picoquic/port_blocking.o"
	@echo "... picoquic/port_blocking.i"
	@echo "... picoquic/port_blocking.s"
	@echo "... picoquic/prague.o"
	@echo "... picoquic/prague.i"
	@echo "... picoquic/prague.s"
	@echo "... picoquic/quicctx.o"
	@echo "... picoquic/quicctx.i"
	@echo "... picoquic/quicctx.s"
	@echo "... picoquic/register_all_cc_algorithms.o"
	@echo "... picoquic/register_all_cc_algorithms.i"
	@echo "... picoquic/register_all_cc_algorithms.s"
	@echo "... picoquic/sacks.o"
	@echo "... picoquic/sacks.i"
	@echo "... picoquic/sacks.s"
	@echo "... picoquic/sender.o"
	@echo "... picoquic/sender.i"
	@echo "... picoquic/sender.s"
	@echo "... picoquic/sim_link.o"
	@echo "... picoquic/sim_link.i"
	@echo "... picoquic/sim_link.s"
	@echo "... picoquic/siphash.o"
	@echo "... picoquic/siphash.i"
	@echo "... picoquic/siphash.s"
	@echo "... picoquic/sockloop.o"
	@echo "... picoquic/sockloop.i"
	@echo "... picoquic/sockloop.s"
	@echo "... picoquic/spinbit.o"
	@echo "... picoquic/spinbit.i"
	@echo "... picoquic/spinbit.s"
	@echo "... picoquic/ticket_store.o"
	@echo "... picoquic/ticket_store.i"
	@echo "... picoquic/ticket_store.s"
	@echo "... picoquic/timing.o"
	@echo "... picoquic/timing.i"
	@echo "... picoquic/timing.s"
	@echo "... picoquic/tls_api.o"
	@echo "... picoquic/tls_api.i"
	@echo "... picoquic/tls_api.s"
	@echo "... picoquic/token_store.o"
	@echo "... picoquic/token_store.i"
	@echo "... picoquic/token_store.s"
	@echo "... picoquic/transport.o"
	@echo "... picoquic/transport.i"
	@echo "... picoquic/transport.s"
	@echo "... picoquic/unified_log.o"
	@echo "... picoquic/unified_log.i"
	@echo "... picoquic/unified_log.s"
	@echo "... picoquic/util.o"
	@echo "... picoquic/util.i"
	@echo "... picoquic/util.s"
	@echo "... picoquicfirst/getopt.o"
	@echo "... picoquicfirst/getopt.i"
	@echo "... picoquicfirst/getopt.s"
	@echo "... picoquicfirst/picoquicdemo.o"
	@echo "... picoquicfirst/picoquicdemo.i"
	@echo "... picoquicfirst/picoquicdemo.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/augment-projects/stub/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

