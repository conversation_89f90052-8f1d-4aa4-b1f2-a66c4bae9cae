* **协议概述**

  1. **适用场景**

     * Relay ←→ Stub 间通过 QUIC 传输客户 TCP/UDP 请求与响应
     * Relay 暴露 SOCKS5 接口给外部客户端
     * Stub 位于私网，无公网 IP，由 Relay 发起 QUIC 流（stream）连接，用于真正出网转发
     * **设备注册**：Stub 首次连接时需要向 Relay 注册设备身份，获取唯一识别
  2. **核心思想**

     * 所有逻辑都在单个 QUIC Session 中进行，无需多次握手
     * **设备注册优先**：QUIC连接建立后，首先进行设备注册，注册成功后才开始处理转发请求
     * 每个 TCP 或 UDP 请求在 QUIC 中对应一个单独的 stream（流）
     * 自定义头部＋SOCKS5 地址字段＋Payload，递送转发信息
     * TCP 直接双向透传；UDP 以 "请求→响应" 模式处理
  3. **实际架构**
     * **设备管理**：基于MAC地址生成唯一设备ID，支持设备识别和状态管理
     * **自动重连机制**：Stub 支持配置化的自动重连，包含指数退避策略
     * **连接监控**：内置连接状态监控和keep-alive机制
     * **流处理**：每个QUIC流由独立的goroutine处理，确保并发性能

---

## 零、设备注册协议

### 0.1 协议概述

设备注册协议用于Stub向Relay注册设备身份。每个Stub基于MAC地址生成唯一的设备ID，确保在相同公网IP下能够区分不同设备。

### 0.2 消息格式 (TLV结构)

所有设备注册消息采用**TLV（Type-Length-Value）**格式：

| 字段 | 长度 (Byte) | 说明 |
|------|-------------|------|
| Type | 1 | 消息类型 |
| Length | 2 | Value字段长度（网络字节序） |
| Value | Length | 消息内容 |

### 0.3 消息类型定义

| 消息类型 | 值 | 方向 | 说明 |
|----------|----|----|------|
| MsgDeviceRegisterReq | 0x01 | Stub→Relay | 设备注册请求 |
| MsgDeviceRegisterAck | 0x02 | Relay→Stub | 设备注册响应 |

### 0.4 设备注册请求 (MsgDeviceRegisterReq)

**消息结构：**
```
┌──────────┬──────────────┬─────────────────────────────────┐
│ 1 Byte   │ 2 Bytes      │ Length Bytes                    │
│ ┌──────┐ │ ┌──────────┐ │ ┌─────────────────────────────┐ │
│ │ 0x01 │ │ │ Length   │ │ │ Device ID (Hex String)      │ │
│ │      │ │ │(Big-End) │ │ │ 例: "00ff883ec65e"          │ │
│ └──────┘ │ └──────────┘ │ └─────────────────────────────┘ │
└──────────┴──────────────┴─────────────────────────────────┘
```

**字段说明：**
- **Type**: `0x01` 表示设备注册请求
- **Length**: Device ID字符串的字节长度（通常为12）
- **Value**: 设备ID的十六进制字符串，不包含前缀

**示例：**
```
0x01                    ← Type = MsgDeviceRegisterReq
0x00 0x0C               ← Length = 12 字节
"00ff883ec65e"          ← Device ID (12字节ASCII字符串)
```

### 0.5 设备注册响应 (MsgDeviceRegisterAck)

**消息结构：**
```
┌──────────┬──────────────┬─────────────────────────────────┐
│ 1 Byte   │ 2 Bytes      │ 1 Byte                          │
│ ┌──────┐ │ ┌──────────┐ │ ┌─────────────────────────────┐ │
│ │ 0x02 │ │ │ 0x00 0x01│ │ │ Status Code                 │ │
│ │      │ │ │(固定长度)│ │ │ 0x00=成功, 其他=错误        │ │
│ └──────┘ │ └──────────┘ │ └─────────────────────────────┘ │
└──────────┴──────────────┴─────────────────────────────────┘
```

**字段说明：**
- **Type**: `0x02` 表示设备注册响应
- **Length**: `0x0001` 固定值，表示状态码长度为1字节
- **Value**: 状态码
  - `0x00`: 注册成功
  - `0x01`: 设备ID格式错误
  - `0x02`: 设备已存在
  - `0xFF`: 服务器内部错误

**成功响应示例：**
```
0x02                    ← Type = MsgDeviceRegisterAck
0x00 0x01               ← Length = 1 字节
0x00                    ← Status = 成功
```

### 0.6 设备ID生成规则

设备ID基于网络接口MAC地址生成，确保设备唯一性：

1. **MAC地址获取**：
   - 遍历所有网络接口（`net.Interfaces()`）
   - 跳过回环接口和虚拟接口
   - 选择第一个有效的硬件地址

2. **ID格式**：
   - 直接使用6字节MAC地址
   - 转换为12字符的十六进制字符串
   - 全小写格式，无分隔符
   - 例：MAC `00:FF:88:3E:C6:5E` → Device ID `"00ff883ec65e"`

3. **代码实现**：
   ```go
   func GenerateDeviceID() (string, error) {
       interfaces, err := net.Interfaces()
       if err != nil {
           return "", fmt.Errorf("获取网络接口失败: %w", err)
       }
       
       for _, iface := range interfaces {
           if iface.Flags&net.FlagLoopback == 0 && 
              iface.HardwareAddr != nil && 
              len(iface.HardwareAddr) == 6 {
               return hex.EncodeToString(iface.HardwareAddr), nil
           }
       }
       
       return "", fmt.Errorf("未找到有效的网络接口")
   }
   ```

### 0.7 注册流程

#### 0.7.1 Stub侧流程

1. **连接建立**：QUIC连接成功后，立即开始设备注册
2. **创建注册流**：使用`session.OpenStream()`创建新的QUIC流
3. **发送注册请求**：
   - 生成设备ID
   - 构造TLV格式的注册请求
   - 通过QUIC流发送给Relay
4. **等待响应**：设置60秒超时，等待Relay的ACK响应
5. **重试机制**：
   - 最大重试10次
   - 重试间隔3秒
   - 总超时时间60秒
6. **处理响应**：
   - 解析TLV格式的ACK消息
   - 检查状态码，`0x00`表示成功
   - 成功后关闭注册流，开始处理转发请求
   - 失败则继续重试或报告错误

#### 0.7.2 Relay侧流程

1. **接收注册请求**：监听新的QUIC流，识别设备注册消息
2. **解析请求**：
   - 验证TLV格式
   - 提取设备ID
   - 验证设备ID格式（12字符十六进制）
3. **设备管理**：
   - 检查设备是否已注册
   - 记录设备信息（IP、连接时间等）
   - 更新设备状态
4. **发送响应**：
   - 构造TLV格式的ACK消息
   - 设置相应的状态码
   - 通过注册流发送响应
5. **后续处理**：
   - 成功注册后，该连接可以处理转发请求
   - 失败的连接可能被断开或要求重新注册

### 0.8 错误处理

1. **超时处理**：
   - 注册流读写超时：60秒
   - 重试间隔：3秒
   - 总重试时间：60秒

2. **网络错误**：
   - 连接断开：记录错误，启动重连流程
   - 读写错误：记录详细错误信息，进行重试

3. **协议错误**：
   - 格式错误：记录错误消息，关闭连接
   - 状态码错误：根据错误类型进行相应处理

4. **资源清理**：
   - 使用`defer`确保流正确关闭
   - Context取消时清理相关资源
   - 错误发生时的优雅降级

### 0.9 日志和监控

设备注册过程包含详细的日志记录：

```
2024/01/15 10:30:15 [INFO] 开始设备注册，设备ID: 00ff883ec65e
2024/01/15 10:30:15 [INFO] 发送设备注册请求到relay
2024/01/15 10:30:15 [INFO] 等待设备注册响应...
2024/01/15 10:30:16 [INFO] 收到设备注册响应，状态: 成功
2024/01/15 10:30:16 [INFO] 设备注册完成，开始处理转发请求
```

---

## 一、数据转发协议

### 1.1 公共字段

| 字段   | 长度 (Byte) | 说明                                                       |
| ---- | --------- | -------------------------------------------------------- |
| CMD  | 1         | 类型字节，表示后续为 TCP CONNECT(0x01) 还是 UDP 转发(0x02)             |
| ATYP | 1         | 地址类型：<br>• 0x01 = IPv4 <br>• 0x03 = 域名 <br>• 0x04 = IPv6 |

> **注意**
>
> * 设备注册完成后，每个 QUIC stream 第一次读到的即是这两个公共字节。
> * 如果 CMD 不在 {0x01, 0x02} 范围，则视为非法并立即关闭该流。

### 1.2 TCP CONNECT (CMD = 0x01)

1. **结构**

   ```
   ┌───────────┬──────────┬──────────────────────────────┐
   │ 1 Byte    │ 1 Byte   │ 可变长度                       │
   │ ┌──────┐  │ ┌──────┐ │ ┌────────────────────────────┐ │
   │ │ CMD  │  │ ATYP │ │ ADDRESS   │ PORT           │ │
   │ │0x01  │  │0x01/0x03/0x04│ (见下文)│ (2 bytes)   │ │
   │ └──────┘  │ └──────┘ │ └────────────────────────────┘ │
   └───────────┴──────────┴──────────────────────────────┘
   ```

2. **字段详解**

   1. **CMD (1 Byte)**

      * `0x01`：表示后续为 TCP CONNECT 请求。
   2. **ATYP (1 Byte)**

      * `0x01`：IPv4
      * `0x03`：域名
      * `0x04`：IPv6
   3. **ADDRESS (可变)**

      * 如果 ATYP = `0x01` (IPv4)：4 字节，格式为标准 IPv4 地址 (e.g. `0A0B0C0D` → `***********`)
      * 如果 ATYP = `0x03` (域名)：1 字节域名长度 `LEN` + LEN 字节域名字符串 (无终止符)，例如：`06` `65 78 61 6D 70 6C 65` → "example"
      * 如果 ATYP = `0x04` (IPv6)：16 字节，格式为标准 IPv6 地址
   4. **PORT (2 Bytes)**

      * Network-byte order (big-endian) 的 16 位整数，表示 TCP 目标端口

3. **TCP CONNECT 完整示例**

   * 假设要连接到 `*******:8080`，则报文：

     ```
     0x01             ← CMD
     0x01             ← ATYP=IPv4
     0x01 0x02 0x03 0x04   ← IPv4 地址 *******
     0x1F 0x90            ← 8080 (0x1F90)
     ```

4. **处理流程 (Stub 侧)**

   1. **解析协议头**：从QUIC流读取`CMD=0x01`和`ATYP`
   2. **解析目标地址**：根据ATYP解析ADDRESS和PORT，组成`host:port`
   3. **建立TCP连接**：使用`net.Dial("tcp", targetHost)`建立到目标的连接
      * 连接失败时记录错误并关闭QUIC流
      * 连接成功时记录连接信息并开始数据转发
   4. **双向透传**：
      * **QUIC → TCP**：单独goroutine循环读取QUIC流数据并写入TCP连接
      * **TCP → QUIC**：主goroutine循环读取TCP连接数据并写入QUIC流
   5. **连接关闭**：
      * 任一方向的读取出现EOF或错误时，关闭对应连接并退出处理协程
      * 实现优雅关闭，确保数据完整传输

---

### 1.3 UDP 转发 (CMD = 0x02)

**重要更新**：根据实际代码实现，UDP转发采用**持久会话模式**，每个QUIC流创建单个UDP socket，支持多个目标地址的复用。

#### 1.3.1 总体结构

UDP转发支持两种数据格式：
1. **完整协议格式**：包含CMD+ATYP+ADDRESS+PORT+PAYLOAD
2. **纯负载格式**：仅包含UDP负载数据（复用已建立的目标地址）

**完整协议格式：**
```
┌───────────┬──────────┬─────────────────────────┬───────────────────┐
│ 1 Byte    │ 1 Byte   │ 可变长度                │ 剩余数据          │
│ ┌──────┐  │ ┌──────┐ │ ┌──────────────────────┐ │ ┌──────────────┐ │
│ │ CMD  │  │ ATYP │ │ ADDRESS   │ PORT     │ │ │ UDP PAYLOAD    │ │
│ │0x02  │  │0x01/0x03/0x04│ (见下文)│ (2 bytes)│ │ │ (可变长度)      │ │
│ └──────┘  │ └──────┘ │ └──────────────────────┘ │ └──────────────┘ │
└───────────┴──────────┴─────────────────────────┴───────────────────┘
```



#### 1.3.2 Stub 侧处理流程

1. **UDP会话建立**
   1. 创建本地UDP socket（`net.ListenUDP`），由系统分配端口
   2. 启动UDP响应监听goroutine（`udpResponseListener`）
   3. 使用context控制goroutine生命周期

2. **数据包处理循环**
   1. **读取原始数据**：从QUIC流读取数据包
   2. **格式识别**：
      * 如果数据以`0x02`开头，按完整协议格式解析
      * 否则作为纯UDP负载处理（使用初始目标地址）
   3. **目标地址解析**：
      * 完整格式：解析CMD+ATYP+ADDRESS+PORT
      * 纯负载格式：使用建立连接时的初始目标地址
   4. **数据转发**：使用`udpConn.WriteToUDP`发送到目标服务器

3. **UDP响应监听** (`udpResponseListener`)
   1. **循环监听**：持续调用`udpConn.ReadFromUDP`接收响应
   2. **超时处理**：设置100ms读取超时，超时后继续循环
   3. **响应封装**：将响应数据按协议格式封装
   4. **回传relay**：通过QUIC流发送响应给relay

4. **响应数据格式**
   ```
   ┌───────────┬──────────┬─────────────────────────┬───────────────────┐
   │ 1 Byte    │ 1 Byte   │ 6 Bytes (IPv4)          │ 剩余数据          │
   │ ┌──────┐  │ ┌──────┐ │ ┌──────────────────────┐ │ ┌──────────────┐ │
   │ │ CMD  │  │ ATYP │ │ IPv4 ADDR │ PORT     │ │ │ UDP RESPONSE   │ │
   │ │0x02  │  │0x01   │ │ (4 bytes) │ (2 bytes)│ │ │ (原始数据)      │ │
   │ └──────┘  │ └──────┘ │ └──────────────────────┘ │ └──────────────┘ │
   └───────────┴──────────┴─────────────────────────┴───────────────────┘
   ```

5. **资源管理**
   * Stream关闭时自动清理UDP socket和相关goroutine
   * 使用defer确保资源正确释放
   * context取消时优雅退出所有协程

#### 1.3.3 实际实现特性

1. **详细日志记录**
   * 数据包传输的完整日志
   * 十六进制和ASCII格式的数据内容打印
   * 连接状态和错误的详细记录

2. **错误处理**
   * 网络错误的分类处理（超时 vs 实际错误）
   * 协议解析错误的详细报告
   * 连接失败时的优雅降级

3. **性能优化**
   * 使用较大的缓冲区（65535字节）
   * 异步处理模式避免阻塞
   * 资源复用减少连接开销

---

## 二、报文示例

### 2.1 TCP CONNECT 示例

* 客户端请求 Relay → Relay 解析后打开 QUIC 流并转发给 Stub，Stub 中处理如下：

  * 假设客户端想连接 `*******:8080`，则发送给 Stub 的字节流：

    ```
    0x01             ← CMD=0x01 (TCP CONNECT)
    0x01             ← ATYP=0x01 (IPv4)
    0x01 0x02 0x03 0x04   ← IPv4=*******
    0x1F 0x90            ← PORT=8080
    ```
  * Stub 成功连接 `*******:8080` 后，将后续数据在 QUIC 上做双向透传直到任一端关闭。

### 2.2 UDP 转发示例

1. **第一次 UDP 请求 (向 "*******:53" 发送 DNS 查询)**

   * QUIC Payload（完整协议格式）:

     ```
     0x02             ← CMD=0x02 (UDP 转发)
     0x01             ← ATYP=0x01 (IPv4)
     0x01 0x02 0x03 0x04   ← 目标 IPv4=*******  
     0x00 0x35            ← 目标 PORT=53
     <DNS QUERY PAYLOAD>   ← UDP 负载数据
     ```
   * Stub处理：
     1. 解析出`targetHost="*******:53"`
     2. 创建UDP socket并启动响应监听goroutine
     3. 发送DNS查询到`*******:53`

2. **后续相同目标的UDP请求（纯负载格式）**

   * QUIC Payload（纯负载格式）:
     ```
     <DNS QUERY PAYLOAD 2>  ← 直接的UDP负载数据
     ```
   * Stub处理：
     1. 识别为纯负载格式
     2. 使用已建立的目标地址`*******:53`
     3. 复用现有UDP socket发送数据

3. **UDP响应处理**

   * Stub的`udpResponseListener`接收到响应后封装：
     ```
     0x02             ← CMD=0x02 (UDP 响应)
     0x01             ← ATYP=0x01 (IPv4)
     0x01 0x02 0x03 0x04   ← 响应来源IPv4=*******
     0x00 0x35            ← 响应来源PORT=53
     <DNS RESPONSE PAYLOAD> ← UDP响应数据
     ```
   * 通过QUIC流发送回Relay

---

## 三、架构与实现细节

### 3.1 程序结构

```
stub_demo/
├── cmd/stub/              # 应用入口
│   └── main.go           # 主程序，信号处理，状态监控
├── internal/
│   ├── client/           # QUIC客户端实现
│   │   └── client.go     # 连接管理、自动重连、状态监控
│   ├── device/           # 设备管理模块
│   │   ├── device.go     # 设备ID生成和管理
│   │   └── register.go   # 设备注册逻辑
│   ├── handler/          # 协议处理器
│   │   ├── stream_handler.go  # 流分发器
│   │   ├── tcp_handler.go     # TCP连接处理
│   │   └── udp_handler.go     # UDP转发处理
│   └── protocol/         # 协议定义
│       └── types.go      # 协议常量、结构体、解析函数
└── configs/              # 配置管理
```

### 3.2 核心组件

1. **QUIC客户端 (`client.go`)**
   * 连接管理和自动重连
   * 指数退避重连策略
   * 连接状态监控和keep-alive
   * 优雅关闭和资源清理
   * 集成设备注册流程

2. **设备管理模块**
   * **设备标识 (`device.go`)**：基于MAC地址生成唯一设备ID
   * **注册服务 (`register.go`)**：TLV协议实现、重试机制、超时控制

3. **流处理器 (`stream_handler.go`)**
   * 协议头解析和流分发
   * TCP/UDP处理器的统一调度
   * 设备注册完成后的流量处理
   * 错误处理和日志记录

4. **TCP处理器 (`tcp_handler.go`)**
   * TCP连接建立和双向透传
   * 异步数据转发（双goroutine模式）
   * 连接状态监控和优雅关闭

5. **UDP处理器 (`udp_handler.go`)**
   * UDP会话管理和数据包处理
   * 多格式数据包解析
   * 响应监听和封装回传
   * 详细的数据包日志记录
   * 多目标连接管理（修复路由bug）

### 3.3 关键特性

1. **高可靠性**
   * 自动重连机制
   * 详细的错误处理和恢复
   * 资源泄漏防护

2. **高性能**
   * 异步处理模式
   * 大缓冲区优化
   * goroutine池化管理

3. **可监控性**
   * 全面的日志记录
   * 连接状态实时监控
   * 数据包内容调试输出

4. **嵌入式友好**
   * 硬编码配置支持
   * 最小化依赖
   * 优化的资源使用

---

## 四、工作流程图

```
Relay                                       Stub
│                                          │
│ < 启动并等待连接 >                        │
│                                          │ main.go 启动
│                                          │ ├─ 创建QUIC客户端
│                                          │ ├─ 创建处理器(TCP/UDP)
│                                          │ ├─ 启动状态监控
│                                          │ └─ 开始连接循环
│                                          │
│ <QUIC连接建立>                           │
│ ←─────────────────────────────────────── │ client.Connect()
│                                          │ ├─ TLS握手
│                                          │ ├─ QUIC握手  
│                                          │ └─ 连接状态更新
│                                          │
│ <设备注册阶段>                            │
│ <─ [注册流: 0x01|长度|设备ID] ──────────── │ device.Register()
│                                          │ ├─ 生成MAC设备ID
│                                          │ ├─ 构造TLV注册消息
│                                          │ ├─ 创建注册QUIC流
│                                          │ └─ 发送注册请求
│                                          │
│ device_manager.RegisterDevice()          │
│ ├─ 验证设备ID格式                         │
│ ├─ 检查设备状态                           │
│ ├─ 更新设备记录                           │
│ └─ 构造ACK响应                           │
│                                          │
│ ──> [注册响应: 0x02|0x0001|状态码] ────── │ 
│                                          │ ├─ 解析TLV响应
│                                          │ ├─ 检查状态码
│                                          │ ├─ 关闭注册流
│                                          │ └─ 开始转发服务
│                                          │
│ <TCP连接请求>                            │
│ ──> [QUIC Stream1: 0x01|ATYP|ADDR|PORT] │
│                                          │ stream_handler.HandleStream()
│                                          │ ├─ 解析协议头(CMD=0x01)
│                                          │ ├─ 解析目标地址
│                                          │ └─ 调用tcp_handler
│                                          │
│                                          │ tcp_handler.HandleTCPConnect()
│                                          │ ├─ net.Dial(目标地址)
│                                          │ ├─ 启动QUIC→TCP协程
│                                          │ └─ 启动TCP→QUIC协程
│                                          │
│ <── 双向数据透传 ──────────────────────> │ 
│                                          │
│ <UDP转发请求>                            │
│ ──> [QUIC Stream2: 0x02|ATYP|ADDR|DATA] │
│                                          │ stream_handler.HandleStream()
│                                          │ ├─ 解析协议头(CMD=0x02)
│                                          │ ├─ 解析目标地址
│                                          │ └─ 调用udp_handler
│                                          │
│                                          │ udp_handler.HandleUDPRelay()
│                                          │ ├─ 创建UDP socket
│                                          │ ├─ 启动响应监听协程
│                                          │ └─ 进入数据处理循环
│                                          │
│                                          │ processUDPStream()
│                                          │ ├─ 读取QUIC数据
│                                          │ ├─ 识别数据格式
│                                          │ ├─ 解析目标地址
│                                          │ └─ WriteToUDP(目标)
│                                          │
│                                          │ udpResponseListener()
│                                          │ ├─ ReadFromUDP(响应)
│                                          │ ├─ 封装响应头部
│ <─────────────────────────────────────── │ └─ stream.Write(响应)
│                                          │
│ <连接断开或错误>                          │
│                                          │ 自动重连逻辑
│                                          │ ├─ 检测连接状态
│                                          │ ├─ 计算退避间隔
│                                          │ ├─ 重新连接
│                                          │ └─ 恢复流处理
```

---

## 五、协议版本和兼容性

### 5.1 当前协议版本

- **协议版本**: `v1.0`
- **支持的消息类型**:
  - 设备注册请求: `0x01`
  - 设备注册响应: `0x02`
  - TCP连接请求: `CMD=0x01`
  - UDP转发请求: `CMD=0x02`

### 5.2 版本兼容性

1. **向后兼容**：
   - 新版本Stub可以连接旧版本Relay（不使用设备注册功能）
   - 协议字段扩展时保持现有字段含义不变

2. **版本协商**：
   - 暂时采用硬编码版本，未来可扩展版本协商机制
   - 建议在QUIC连接参数中携带协议版本信息

### 5.3 已知问题修复记录

1. **UDP数据包边界问题** (v1.0修复)：
   - **问题**：多个UDP数据包在QUIC流中被错误合并
   - **修复**：实现数据包边界检测和缓冲机制

2. **UDP响应路由错误** (v1.0修复)：
   - **问题**：多目标场景下响应数据发送到错误的目标
   - **修复**：为每个目标创建独立的UDP连接和监听器

3. **设备识别问题** (v1.0新增)：
   - **需求**：同一公网IP下区分不同设备
   - **解决**：基于MAC地址的设备注册机制

### 5.4 性能指标

- **设备注册延迟**: < 5秒（包含重试）
- **TCP连接建立**: < 500ms
- **UDP首包延迟**: < 100ms
- **并发流处理**: > 1000个并发QUIC流
- **内存使用**: 基础运行 < 50MB

### 5.5 安全考虑

1. **设备身份验证**：
   - 基于MAC地址的设备ID生成
   - 防止设备ID伪造的机制（依赖网络接口真实性）

2. **传输安全**：
   - QUIC内置TLS 1.3加密
   - 端到端数据保护

3. **访问控制**：
   - Relay侧设备白名单支持
   - 异常连接检测和阻断

---
