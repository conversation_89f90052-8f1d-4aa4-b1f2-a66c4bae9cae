
Skip to content
Pull requests
Issues
Marketplace
Explore
@huitema
private-octopus /
picoquic
Public

Code
Issues 34
Pull requests 2
Discussions
Actions
Projects
Wiki
Security
Insights

    Settings

Add valgrind tests CITestsNoFusion #41

Summary
Jobs

    CI-Tests-No-Fusion

CI-Tests-No-Fusion
failed 2 minutes ago in 1m 52s
1s
1s
47s
26s
35s
Run sudo apt-get update
Hit:1 http://azure.archive.ubuntu.com/ubuntu focal InRelease
Get:2 http://azure.archive.ubuntu.com/ubuntu focal-updates InRelease [114 kB]
Get:3 http://azure.archive.ubuntu.com/ubuntu focal-backports InRelease [108 kB]
Get:4 https://packages.microsoft.com/ubuntu/20.04/prod focal InRelease [10.5 kB]
Get:5 http://security.ubuntu.com/ubuntu focal-security InRelease [114 kB]
Hit:6 http://ppa.launchpad.net/ubuntu-toolchain-r/test/ubuntu focal InRelease
Get:7 http://azure.archive.ubuntu.com/ubuntu focal-updates/main amd64 Packages [1642 kB]
Get:8 http://azure.archive.ubuntu.com/ubuntu focal-updates/main Translation-en [312 kB]
Get:9 http://azure.archive.ubuntu.com/ubuntu focal-updates/main amd64 c-n-f Metadata [14.8 kB]
Get:10 http://azure.archive.ubuntu.com/ubuntu focal-updates/restricted amd64 Packages [853 kB]
Get:11 http://azure.archive.ubuntu.com/ubuntu focal-updates/restricted Translation-en [122 kB]
Get:12 http://azure.archive.ubuntu.com/ubuntu focal-updates/universe amd64 Packages [910 kB]
Get:13 http://azure.archive.ubuntu.com/ubuntu focal-updates/universe Translation-en [202 kB]
Get:14 http://azure.archive.ubuntu.com/ubuntu focal-updates/universe amd64 c-n-f Metadata [20.3 kB]
Get:15 http://azure.archive.ubuntu.com/ubuntu focal-updates/multiverse amd64 Packages [23.8 kB]
Get:16 http://azure.archive.ubuntu.com/ubuntu focal-updates/multiverse amd64 c-n-f Metadata [580 B]
Get:17 http://azure.archive.ubuntu.com/ubuntu focal-backports/main amd64 Packages [42.2 kB]
Get:18 http://azure.archive.ubuntu.com/ubuntu focal-backports/main Translation-en [10.1 kB]
Get:19 http://azure.archive.ubuntu.com/ubuntu focal-backports/universe amd64 Packages [22.7 kB]
Get:20 http://azure.archive.ubuntu.com/ubuntu focal-backports/universe Translation-en [15.4 kB]
Get:21 http://azure.archive.ubuntu.com/ubuntu focal-backports/universe amd64 c-n-f Metadata [804 B]
Get:22 https://packages.microsoft.com/ubuntu/20.04/prod focal/main amd64 Packages [141 kB]
Get:23 http://security.ubuntu.com/ubuntu focal-security/main amd64 Packages [1317 kB]
Get:24 http://security.ubuntu.com/ubuntu focal-security/main Translation-en [231 kB]
Get:25 http://security.ubuntu.com/ubuntu focal-security/main amd64 c-n-f Metadata [9808 B]
Get:26 http://security.ubuntu.com/ubuntu focal-security/restricted amd64 Packages [799 kB]
Get:27 http://security.ubuntu.com/ubuntu focal-security/restricted Translation-en [114 kB]
Get:28 http://security.ubuntu.com/ubuntu focal-security/universe amd64 Packages [692 kB]
Get:29 http://security.ubuntu.com/ubuntu focal-security/universe Translation-en [121 kB]
Get:30 http://security.ubuntu.com/ubuntu focal-security/universe amd64 c-n-f Metadata [14.0 kB]
Fetched 7976 kB in 1s (6116 kB/s)
Reading package lists...
Reading package lists...
Building dependency tree...
Reading state information...
The following additional packages will be installed:
  gdb libbabeltrace1 libboost-regex1.71.0 libc-dev-bin libc6 libc6-dbg
  libc6-dev libc6-i386 libipt2 libsource-highlight-common
  libsource-highlight4v5 libxxhash0
Suggested packages:
  gdb-doc gdbserver glibc-doc manpages-dev valgrind-dbg valgrind-mpi
  kcachegrind alleyoop valkyrie
Recommended packages:
  manpages-dev
The following NEW packages will be installed:
  gdb libbabeltrace1 libboost-regex1.71.0 libc6-dbg libipt2
  libsource-highlight-common libsource-highlight4v5 libxxhash0 valgrind
The following packages will be upgraded:
  libc-dev-bin libc6 libc6-dev libc6-i386
4 upgraded, 9 newly installed, 0 to remove and 115 not upgraded.
Need to get 43.1 MB of archives.
After this operation, 182 MB of additional disk space will be used.
Get:1 http://azure.archive.ubuntu.com/ubuntu focal-updates/main amd64 libc6-dev amd64 2.31-0ubuntu9.7 [2518 kB]
Get:2 http://azure.archive.ubuntu.com/ubuntu focal-updates/main amd64 libc-dev-bin amd64 2.31-0ubuntu9.7 [71.6 kB]
Get:3 http://azure.archive.ubuntu.com/ubuntu focal-updates/main amd64 libc6-i386 amd64 2.31-0ubuntu9.7 [2725 kB]
Get:4 http://ppa.launchpad.net/ubuntu-toolchain-r/test/ubuntu focal/main amd64 gdb amd64 10.2-0ubuntu1~20.04~1 [3282 kB]
Get:5 http://azure.archive.ubuntu.com/ubuntu focal-updates/main amd64 libc6 amd64 2.31-0ubuntu9.7 [2714 kB]
Get:6 http://azure.archive.ubuntu.com/ubuntu focal/main amd64 libbabeltrace1 amd64 1.5.8-1build1 [156 kB]
Get:7 http://azure.archive.ubuntu.com/ubuntu focal/universe amd64 libipt2 amd64 2.0.1-1 [42.8 kB]
Get:8 http://azure.archive.ubuntu.com/ubuntu focal/universe amd64 libsource-highlight-common all 3.1.8-1.2build5 [50.7 kB]
Get:9 http://azure.archive.ubuntu.com/ubuntu focal/universe amd64 libboost-regex1.71.0 amd64 1.71.0-6ubuntu6 [471 kB]
Get:10 http://azure.archive.ubuntu.com/ubuntu focal/universe amd64 libsource-highlight4v5 amd64 3.1.8-1.2build5 [232 kB]
Get:11 http://azure.archive.ubuntu.com/ubuntu focal/universe amd64 libxxhash0 amd64 0.7.3-1 [15.3 kB]
Get:12 http://azure.archive.ubuntu.com/ubuntu focal-updates/main amd64 libc6-dbg amd64 2.31-0ubuntu9.7 [10.5 MB]
Get:13 http://azure.archive.ubuntu.com/ubuntu focal-updates/main amd64 valgrind amd64 1:3.15.0-1ubuntu9.1 [20.3 MB]
Preconfiguring packages ...
Fetched 43.1 MB in 1s (52.7 MB/s)
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 241056 files and directories currently installed.)
Preparing to unpack .../libc6-dev_2.31-0ubuntu9.7_amd64.deb ...
Unpacking libc6-dev:amd64 (2.31-0ubuntu9.7) over (2.31-0ubuntu9.2) ...
Preparing to unpack .../libc-dev-bin_2.31-0ubuntu9.7_amd64.deb ...
Unpacking libc-dev-bin (2.31-0ubuntu9.7) over (2.31-0ubuntu9.2) ...
Preparing to unpack .../libc6-i386_2.31-0ubuntu9.7_amd64.deb ...
Unpacking libc6-i386 (2.31-0ubuntu9.7) over (2.31-0ubuntu9.2) ...
Preparing to unpack .../libc6_2.31-0ubuntu9.7_amd64.deb ...
Unpacking libc6:amd64 (2.31-0ubuntu9.7) over (2.31-0ubuntu9.2) ...
Setting up libc6:amd64 (2.31-0ubuntu9.7) ...
Selecting previously unselected package libbabeltrace1:amd64.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 241056 files and directories currently installed.)
Preparing to unpack .../0-libbabeltrace1_1.5.8-1build1_amd64.deb ...
Unpacking libbabeltrace1:amd64 (1.5.8-1build1) ...
Selecting previously unselected package libipt2.
Preparing to unpack .../1-libipt2_2.0.1-1_amd64.deb ...
Unpacking libipt2 (2.0.1-1) ...
Selecting previously unselected package libsource-highlight-common.
Preparing to unpack .../2-libsource-highlight-common_3.1.8-1.2build5_all.deb ...
Unpacking libsource-highlight-common (3.1.8-1.2build5) ...
Selecting previously unselected package libboost-regex1.71.0:amd64.
Preparing to unpack .../3-libboost-regex1.71.0_1.71.0-6ubuntu6_amd64.deb ...
Unpacking libboost-regex1.71.0:amd64 (1.71.0-6ubuntu6) ...
Selecting previously unselected package libsource-highlight4v5.
Preparing to unpack .../4-libsource-highlight4v5_3.1.8-1.2build5_amd64.deb ...
Unpacking libsource-highlight4v5 (3.1.8-1.2build5) ...
Selecting previously unselected package libxxhash0:amd64.
Preparing to unpack .../5-libxxhash0_0.7.3-1_amd64.deb ...
Unpacking libxxhash0:amd64 (0.7.3-1) ...
Selecting previously unselected package gdb.
Preparing to unpack .../6-gdb_10.2-0ubuntu1~20.04~1_amd64.deb ...
Unpacking gdb (10.2-0ubuntu1~20.04~1) ...
Selecting previously unselected package libc6-dbg:amd64.
Preparing to unpack .../7-libc6-dbg_2.31-0ubuntu9.7_amd64.deb ...
Unpacking libc6-dbg:amd64 (2.31-0ubuntu9.7) ...
Selecting previously unselected package valgrind.
Preparing to unpack .../8-valgrind_1%3a3.15.0-1ubuntu9.1_amd64.deb ...
Unpacking valgrind (1:3.15.0-1ubuntu9.1) ...
Setting up libsource-highlight-common (3.1.8-1.2build5) ...
Setting up libc6-dbg:amd64 (2.31-0ubuntu9.7) ...
Setting up libipt2 (2.0.1-1) ...
Setting up libboost-regex1.71.0:amd64 (1.71.0-6ubuntu6) ...
Setting up libbabeltrace1:amd64 (1.5.8-1build1) ...
Setting up libxxhash0:amd64 (0.7.3-1) ...
Setting up libc6-i386 (2.31-0ubuntu9.7) ...
Setting up libc-dev-bin (2.31-0ubuntu9.7) ...
Setting up valgrind (1:3.15.0-1ubuntu9.1) ...
Setting up libsource-highlight4v5 (3.1.8-1.2build5) ...
Setting up gdb (10.2-0ubuntu1~20.04~1) ...
Setting up libc6-dev:amd64 (2.31-0ubuntu9.7) ...
Processing triggers for man-db (2.9.1-1) ...
Processing triggers for libc-bin (2.31-0ubuntu9.2) ...
==4646== Memcheck, a memory error detector
==4646== Copyright (C) 2002-2017, and GNU GPL'd, by Julian Seward et al.
==4646== Using Valgrind-3.15.0-608cb11914-20190413 and LibVEX; rerun with -h for copyright info
==4646== Command: ./picoquic_ct zero_rtt_many_losses
==4646== 
--4646-- Valgrind options:
--4646--    -v
--4646--    --error-exitcode=1
--4646--    --track-origins=yes
--4646-- Contents of /proc/version:
--4646--   Linux version 5.11.0-1028-azure (buildd@lcy02-amd64-043) (gcc (Ubuntu 9.3.0-17ubuntu1~20.04) 9.3.0, GNU ld (GNU Binutils for Ubuntu) 2.34) #31~20.04.2-Ubuntu SMP Tue Jan 18 08:46:15 UTC 2022
--4646-- 
--4646-- Arch and hwcaps: AMD64, LittleEndian, amd64-cx16-lzcnt-rdtscp-sse3-ssse3-avx-avx2-bmi-f16c-rdrand
--4646-- Page sizes: currently 4096, max supported 4096
--4646-- Valgrind library directory: /usr/lib/x86_64-linux-gnu/valgrind
--4646-- Reading syms from /home/<USER>/work/picoquic/picoquic/picoquic_ct
--4646-- Reading syms from /usr/lib/x86_64-linux-gnu/ld-2.31.so
--4646--   Considering /usr/lib/x86_64-linux-gnu/ld-2.31.so ..
--4646--   .. CRC mismatch (computed 0306b78c wanted 8d362b37)
--4646--   Considering /lib/x86_64-linux-gnu/ld-2.31.so ..
--4646--   .. CRC mismatch (computed 0306b78c wanted 8d362b37)
--4646--   Considering /usr/lib/debug/lib/x86_64-linux-gnu/ld-2.31.so ..
--4646--   .. CRC is valid
--4646-- Reading syms from /usr/lib/x86_64-linux-gnu/valgrind/memcheck-amd64-linux
--4646--    object doesn't have a symbol table
--4646--    object doesn't have a dynamic symbol table
--4646-- Scheduler: using generic scheduler lock implementation.
--4646-- Reading suppressions file: /usr/lib/x86_64-linux-gnu/valgrind/default.supp
==4646== embedded gdbserver: reading from /tmp/vgdb-pipe-from-vgdb-to-4646-by-runner-on-???
==4646== embedded gdbserver: writing to   /tmp/vgdb-pipe-to-vgdb-from-4646-by-runner-on-???
==4646== embedded gdbserver: shared mem   /tmp/vgdb-pipe-shared-mem-vgdb-4646-by-runner-on-???
==4646== 
==4646== TO CONTROL THIS PROCESS USING vgdb (which you probably
==4646== don't want to do, unless you know exactly what you're doing,
==4646== or are doing some strange experiment):
==4646==   /usr/lib/x86_64-linux-gnu/valgrind/../../bin/vgdb --pid=4646 ...command...
==4646== 
==4646== TO DEBUG THIS PROCESS USING GDB: start GDB like this
==4646==   /path/to/gdb ./picoquic_ct
==4646== and then give GDB the following command
==4646==   target remote | /usr/lib/x86_64-linux-gnu/valgrind/../../bin/vgdb --pid=4646
==4646== --pid is optional if only one valgrind process is running
==4646== 
--4646-- REDIR: 0x4022e10 (ld-linux-x86-64.so.2:strlen) redirected to 0x580c9ce2 (???)
--4646-- REDIR: 0x4022be0 (ld-linux-x86-64.so.2:index) redirected to 0x580c9cfc (???)
--4646-- Reading syms from /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_core-amd64-linux.so
--4646--    object doesn't have a symbol table
--4646-- Reading syms from /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so
--4646--    object doesn't have a symbol table
==4646== WARNING: new redirection conflicts with existing -- ignoring it
--4646--     old: 0x04022e10 (strlen              ) R-> (0000.0) 0x580c9ce2 ???
--4646--     new: 0x04022e10 (strlen              ) R-> (2007.0) 0x0483f060 strlen
--4646-- REDIR: 0x401f5f0 (ld-linux-x86-64.so.2:strcmp) redirected to 0x483ffd0 (strcmp)
--4646-- REDIR: 0x4023370 (ld-linux-x86-64.so.2:mempcpy) redirected to 0x4843a20 (mempcpy)
--4646-- Reading syms from /usr/lib/x86_64-linux-gnu/libcrypto.so.1.1
--4646--    object doesn't have a symbol table
--4646-- Reading syms from /usr/lib/x86_64-linux-gnu/libpthread-2.31.so
--4646--   Considering /usr/lib/debug/.build-id/f0/983025f0e0f327a6da752ff4ffa675e0be393f.debug ..
--4646--   .. build-id is valid
--4646-- Reading syms from /usr/lib/x86_64-linux-gnu/libc-2.31.so
--4646--   Considering /usr/lib/x86_64-linux-gnu/libc-2.31.so ..
--4646--   .. CRC mismatch (computed ef41b1a0 wanted f854b801)
--4646--   Considering /lib/x86_64-linux-gnu/libc-2.31.so ..
--4646--   .. CRC mismatch (computed ef41b1a0 wanted f854b801)
--4646--   Considering /usr/lib/debug/lib/x86_64-linux-gnu/libc-2.31.so ..
--4646--   .. CRC is valid
--4646-- Reading syms from /usr/lib/x86_64-linux-gnu/libdl-2.31.so
--4646--   Considering /usr/lib/x86_64-linux-gnu/libdl-2.31.so ..
--4646--   .. CRC mismatch (computed 2bb25318 wanted f334bb32)
--4646--   Considering /lib/x86_64-linux-gnu/libdl-2.31.so ..
--4646--   .. CRC mismatch (computed 2bb25318 wanted f334bb32)
--4646--   Considering /usr/lib/debug/lib/x86_64-linux-gnu/libdl-2.31.so ..
--4646--   .. CRC is valid
--4646-- REDIR: 0x4bf34b0 (libc.so.6:memmove) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf27b0 (libc.so.6:strncpy) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf37e0 (libc.so.6:strcasecmp) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf20d0 (libc.so.6:strcat) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf2810 (libc.so.6:rindex) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf4c80 (libc.so.6:rawmemchr) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4c0fd10 (libc.so.6:wmemchr) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4c0f850 (libc.so.6:wcscmp) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3610 (libc.so.6:mempcpy) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3440 (libc.so.6:bcmp) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf2740 (libc.so.6:strncmp) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf2180 (libc.so.6:strcmp) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3570 (libc.so.6:memset) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4c0f810 (libc.so.6:wcschr) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf26a0 (libc.so.6:strnlen) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf2260 (libc.so.6:strcspn) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3830 (libc.so.6:strncasecmp) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf2200 (libc.so.6:strcpy) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3980 (libc.so.6:memcpy@@GLIBC_2.14) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4c10f80 (libc.so.6:wcsnlen) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4c0f890 (libc.so.6:wcscpy) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf2850 (libc.so.6:strpbrk) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf2130 (libc.so.6:index) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf2660 (libc.so.6:strlen) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bfbbd0 (libc.so.6:memrchr) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3880 (libc.so.6:strcasecmp_l) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3400 (libc.so.6:memchr) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4c0f960 (libc.so.6:wcslen) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf2b10 (libc.so.6:strspn) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3780 (libc.so.6:stpncpy) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3720 (libc.so.6:stpcpy) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf4cc0 (libc.so.6:strchrnul) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf38d0 (libc.so.6:strncasecmp_l) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4c80880 (libc.so.6:__memcpy_chk) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4bf3320 (libc.so.6:strstr) redirected to 0x48311d0 (_vgnU_ifunc_wrapper)
--4646-- REDIR: 0x4cdb410 (libc.so.6:__strrchr_avx2) redirected to 0x483ea10 (rindex)
--4646-- REDIR: 0x4cdb5e0 (libc.so.6:__strlen_avx2) redirected to 0x483ef40 (strlen)
--4646-- REDIR: 0x4beeb40 (libc.so.6:calloc) redirected to 0x483dce0 (calloc)
--4646-- REDIR: 0x4cd6f20 (libc.so.6:__strncmp_avx2) redirected to 0x483f670 (strncmp)
--4646-- REDIR: 0x4cd6ae0 (libc.so.6:__strcmp_avx2) redirected to 0x483fed0 (strcmp)
--4646-- REDIR: 0x4cdb220 (libc.so.6:__strchrnul_avx2) redirected to 0x4843540 (strchrnul)
--4646-- REDIR: 0x4bed110 (libc.so.6:malloc) redirected to 0x483b780 (malloc)
--4646-- REDIR: 0x4cde5d0 (libc.so.6:__mempcpy_avx_unaligned_erms) redirected to 0x4843660 (mempcpy)
Starting test number 145, zero_rtt_many_losses
--4646-- REDIR: 0x4bf2fd0 (libc.so.6:__GI_strstr) redirected to 0x4843ca0 (__strstr_sse2)
--4646-- REDIR: 0x4bed700 (libc.so.6:free) redirected to 0x483c9d0 (free)
--4646-- REDIR: 0x4cdea70 (libc.so.6:__memset_avx2_unaligned_erms) redirected to 0x48428e0 (memset)
--4646-- REDIR: 0x4bedeb0 (libc.so.6:realloc) redirected to 0x483df30 (realloc)
--4646-- REDIR: 0x4cdd810 (libc.so.6:__stpcpy_avx2) redirected to 0x4842300 (stpcpy)
--4646-- REDIR: 0x4cde5f0 (libc.so.6:__memcpy_avx_unaligned_erms) redirected to 0x48429f0 (memmove)
--4646-- REDIR: 0x4cd7fb0 (libc.so.6:__strcasecmp_avx) redirected to 0x483f830 (strcasecmp)
--4646-- REDIR: 0x4cdaff0 (libc.so.6:__strchr_avx2) redirected to 0x483ebf0 (index)
--4646-- REDIR: 0x4cd7440 (libc.so.6:__memchr_avx2) redirected to 0x4840050 (memchr)
--4646-- REDIR: 0x4cd7bd0 (libc.so.6:__memcmp_avx2_movbe) redirected to 0x48421e0 (bcmp)
--4646-- REDIR: 0x4cde5e0 (libc.so.6:__memcpy_chk_avx_unaligned_erms) redirected to 0x4843b10 (__memcpy_chk)
--4646-- REDIR: 0x4cd9630 (libc.so.6:__strncasecmp_avx) redirected to 0x483f910 (strncasecmp)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153923: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0E3: zero_rtt_test_one (tls_api_test.c:3628)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x19D72A: picoquic_parse_packet_header (packet.c:373)
==4646==    by 0x19D72A: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Use of uninitialised value of size 8
==4646==    at 0x1A23EF: picohash_retrieve (picohash.c:56)
==4646==    by 0x1ADA33: picoquic_cnx_by_id (quicctx.c:3960)
==4646==    by 0x19DAED: picoquic_parse_short_packet_header (packet.c:302)
==4646==    by 0x19DAED: picoquic_parse_packet_header (packet.c:376)
==4646==    by 0x19DAED: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Use of uninitialised value of size 8
==4646==    at 0x1A23EF: picohash_retrieve (picohash.c:56)
==4646==    by 0x1ADC0D: picoquic_cnx_by_secret (quicctx.c:4020)
==4646==    by 0x19DC10: picoquic_parse_header_and_decrypt (packet.c:822)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA3F: picoquic_skip_frame (frames.c:4966)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA43: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Use of uninitialised value of size 8
==4646==    at 0x18DA50: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18FFD3: picoquic_varint_decode (intformat.c:145)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18FFDC: picoquic_varint_decode (intformat.c:152)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DFD6: picoquic_process_possible_ack_of_ack_frame (frames.c:2779)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DFE9: picoquic_process_possible_ack_of_ack_frame (frames.c:2783)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF44: picoquic_process_possible_ack_of_ack_frame (frames.c:2788)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF51: picoquic_process_possible_ack_of_ack_frame (frames.c:2793)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF5E: picoquic_process_possible_ack_of_ack_frame (frames.c:2797)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF68: picoquic_process_possible_ack_of_ack_frame (frames.c:2801)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF79: picoquic_process_possible_ack_of_ack_frame (frames.c:2805)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF87: picoquic_process_possible_ack_of_ack_frame (frames.c:2815)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA3F: picoquic_skip_frame (frames.c:4966)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA43: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Use of uninitialised value of size 8
==4646==    at 0x18DA50: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x484224E: bcmp (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A0842: picoquic_incoming_segment (packet.c:2168)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x4842225: bcmp (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A0842: picoquic_incoming_segment (packet.c:2168)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1A0848: picoquic_incoming_segment (packet.c:2168)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A88: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1596)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153930: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1596)
==4646==    by 0x15B0E3: zero_rtt_test_one (tls_api_test.c:3628)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 3, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 4, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 3, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 4 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 2, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 4, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 3, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 5, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 2, epoch: 0, pc: 2, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 1

==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA3F: picoquic_skip_frame (frames.c:4966)
==4646==    by 0x1B1619: picoquic_copy_before_retransmit (sender.c:1397)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA43: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x1B1619: picoquic_copy_before_retransmit (sender.c:1397)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Use of uninitialised value of size 8
==4646==    at 0x18DA50: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x1B1619: picoquic_copy_before_retransmit (sender.c:1397)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B1619: picoquic_copy_before_retransmit (sender.c:1397)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1B1642: picoquic_copy_before_retransmit (sender.c:1408)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 4, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

oquic/picoquic/packet.c:111 [picoquic_parse_long_packet_header]: Version is not recognized: 0xabcdeab4

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 3, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 2, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 3, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 4, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 4, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 4 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 4 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 3, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 1

==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1B1642: picoquic_copy_before_retransmit (sender.c:1408)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B8592: picoquic_prepare_packet_ready (sender.c:3793)
==4646==    by 0x1B9DDD: picoquic_prepare_segment (sender.c:4262)
==4646==    by 0x1B9DDD: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 4 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 2, epoch: 0, pc: 2, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 1

==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B28F7: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B28F7: picoquic_is_cnx_backlog_empty (sender.c:1855)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 2, epoch: 0, pc: 2, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 2, epoch: 0, pc: 2, pn: -1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 2, epoch: 0, pc: 2, pn: -1

==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x484224E: bcmp (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A2414: picohash_retrieve (picohash.c:59)
==4646==    by 0x1ADA33: picoquic_cnx_by_id (quicctx.c:3960)
==4646==    by 0x19DAED: picoquic_parse_short_packet_header (packet.c:302)
==4646==    by 0x19DAED: picoquic_parse_packet_header (packet.c:376)
==4646==    by 0x19DAED: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x4842225: bcmp (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A2414: picohash_retrieve (picohash.c:59)
==4646==    by 0x1ADA33: picoquic_cnx_by_id (quicctx.c:3960)
==4646==    by 0x19DAED: picoquic_parse_short_packet_header (packet.c:302)
==4646==    by 0x19DAED: picoquic_parse_packet_header (packet.c:376)
==4646==    by 0x19DAED: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1A2417: picohash_retrieve (picohash.c:59)
==4646==    by 0x1ADA33: picoquic_cnx_by_id (quicctx.c:3960)
==4646==    by 0x19DAED: picoquic_parse_short_packet_header (packet.c:302)
==4646==    by 0x19DAED: picoquic_parse_packet_header (packet.c:376)
==4646==    by 0x19DAED: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 2, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 4, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 5, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 4 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 3, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 3, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 4 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 2, epoch: 0, pc: 2, pn: -1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 2, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 5, epoch: 1, pc: 0, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18FFED: picoquic_varint_decode (intformat.c:152)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x19000C: picoquic_varint_decode (intformat.c:152)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x190037: picoquic_varint_decode (intformat.c:152)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1C9D69: picoquic_frames_varint_decode (util.c:654)
==4646==    by 0x18DC20: picoquic_skip_frame (frames.c:5063)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1C9D76: picoquic_frames_varint_decode (util.c:657)
==4646==    by 0x18DC20: picoquic_skip_frame (frames.c:5063)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1C9DA1: picoquic_frames_varint_decode (util.c:657)
==4646==    by 0x18DC20: picoquic_skip_frame (frames.c:5063)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DC29: picoquic_skip_frame (frames.c:5063)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DC3B: picoquic_skip_frame (frames.c:5064)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DC41: picoquic_skip_frame (frames.c:5064)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DE07: picoquic_skip_frame (frames.c:5068)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DE14: picoquic_skip_frame (frames.c:5068)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 2, epoch: 0, pc: 2, pn: -1

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 4 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 3, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:1606 [tls_api_synch_to_empty_loop]: Exit synch loop after 56 rounds, backlog or not enough paths (1 & 1).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 3, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 6, epoch: 3, pc: 0, pn: -1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 3, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 0

oquic/picoquic/packet.c:2511 [picoquic_process_sooner_packets]: Processing sooner packet type 6 returns -1 (0x-1)
oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 2, epoch: 0, pc: 2, pn: -1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 1, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 2, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 2, pc=2, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 1, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 5, pc=0, seq = 0, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 2, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 3, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 3, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 4, is_client = 0

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 4, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 5, is_client = 1

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 6, is_client = 0

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 0, ticket save error (0x0).

oquic/picoquic/sender.c:1660 [picoquic_retransmit_needed_packet]: Retransmit packet type 4, pc=1, seq = 0, is_client = 1

oquic/picoquic/packet.c:531 [picoquic_remove_header_protection]: PN dec not ready, type: 4, epoch: 2, pc: 1, pn: -1

quictest/tls_api_test.c:3698 [zero_rtt_test_one]: Zero RTT test (badcrypt: 0, hard: 0), cnx 1, ticket save error (0x0).

    Success.
==4646== 
==4646== HEAP SUMMARY:
==4646==     in use at exit: 0 bytes in 0 blocks
==4646==   total heap usage: 149,904 allocs, 149,904 frees, 28,160,581 bytes allocated
==4646== 
==4646== All heap blocks were freed -- no leaks are possible
==4646== 
==4646== ERROR SUMMARY: 2649 errors from 48 contexts (suppressed: 0 from 0)
==4646== 
==4646== 1 errors in context 1 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x190037: picoquic_varint_decode (intformat.c:152)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 1 errors in context 2 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1B1642: picoquic_copy_before_retransmit (sender.c:1408)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B8592: picoquic_prepare_packet_ready (sender.c:3793)
==4646==    by 0x1B9DDD: picoquic_prepare_segment (sender.c:4262)
==4646==    by 0x1B9DDD: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 2 errors in context 3 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DE14: picoquic_skip_frame (frames.c:5068)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 2 errors in context 4 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DE07: picoquic_skip_frame (frames.c:5068)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 2 errors in context 5 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DC41: picoquic_skip_frame (frames.c:5064)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 2 errors in context 6 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DC3B: picoquic_skip_frame (frames.c:5064)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 2 errors in context 7 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DC29: picoquic_skip_frame (frames.c:5063)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 2 errors in context 8 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1C9D76: picoquic_frames_varint_decode (util.c:657)
==4646==    by 0x18DC20: picoquic_skip_frame (frames.c:5063)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 2 errors in context 9 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1C9D69: picoquic_frames_varint_decode (util.c:654)
==4646==    by 0x18DC20: picoquic_skip_frame (frames.c:5063)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 2 errors in context 10 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x19000C: picoquic_varint_decode (intformat.c:152)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 2 errors in context 11 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18FFED: picoquic_varint_decode (intformat.c:152)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 4 errors in context 12 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1A2417: picohash_retrieve (picohash.c:59)
==4646==    by 0x1ADA33: picoquic_cnx_by_id (quicctx.c:3960)
==4646==    by 0x19DAED: picoquic_parse_short_packet_header (packet.c:302)
==4646==    by 0x19DAED: picoquic_parse_packet_header (packet.c:376)
==4646==    by 0x19DAED: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 4 errors in context 13 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x4842225: bcmp (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A2414: picohash_retrieve (picohash.c:59)
==4646==    by 0x1ADA33: picoquic_cnx_by_id (quicctx.c:3960)
==4646==    by 0x19DAED: picoquic_parse_short_packet_header (packet.c:302)
==4646==    by 0x19DAED: picoquic_parse_packet_header (packet.c:376)
==4646==    by 0x19DAED: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 4 errors in context 14 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x484224E: bcmp (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A2414: picohash_retrieve (picohash.c:59)
==4646==    by 0x1ADA33: picoquic_cnx_by_id (quicctx.c:3960)
==4646==    by 0x19DAED: picoquic_parse_short_packet_header (packet.c:302)
==4646==    by 0x19DAED: picoquic_parse_packet_header (packet.c:376)
==4646==    by 0x19DAED: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 5 errors in context 15 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1B1642: picoquic_copy_before_retransmit (sender.c:1408)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 5 errors in context 16 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1C9DA1: picoquic_frames_varint_decode (util.c:657)
==4646==    by 0x18DC20: picoquic_skip_frame (frames.c:5063)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 6 errors in context 17 of 48:
==4646== Use of uninitialised value of size 8
==4646==    at 0x18DA50: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x1B1619: picoquic_copy_before_retransmit (sender.c:1397)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 6 errors in context 18 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA43: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x1B1619: picoquic_copy_before_retransmit (sender.c:1397)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 6 errors in context 19 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA3F: picoquic_skip_frame (frames.c:4966)
==4646==    by 0x1B1619: picoquic_copy_before_retransmit (sender.c:1397)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 13 errors in context 20 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x1A0848: picoquic_incoming_segment (packet.c:2168)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 13 errors in context 21 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x4842225: bcmp (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A0842: picoquic_incoming_segment (packet.c:2168)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 13 errors in context 22 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x484224E: bcmp (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A0842: picoquic_incoming_segment (packet.c:2168)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 16 errors in context 23 of 48:
==4646== Use of uninitialised value of size 8
==4646==    at 0x18DA50: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 24 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA43: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 25 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA3F: picoquic_skip_frame (frames.c:4966)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 26 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF87: picoquic_process_possible_ack_of_ack_frame (frames.c:2815)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 27 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF79: picoquic_process_possible_ack_of_ack_frame (frames.c:2805)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 28 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF68: picoquic_process_possible_ack_of_ack_frame (frames.c:2801)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 29 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF5E: picoquic_process_possible_ack_of_ack_frame (frames.c:2797)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 30 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF51: picoquic_process_possible_ack_of_ack_frame (frames.c:2793)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 31 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DF44: picoquic_process_possible_ack_of_ack_frame (frames.c:2788)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 32 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DFE9: picoquic_process_possible_ack_of_ack_frame (frames.c:2783)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 33 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DFD6: picoquic_process_possible_ack_of_ack_frame (frames.c:2779)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 34 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18FFDC: picoquic_varint_decode (intformat.c:152)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 18 errors in context 35 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18FFD3: picoquic_varint_decode (intformat.c:145)
==4646==    by 0x18DFD2: picoquic_process_possible_ack_of_ack_frame (frames.c:2778)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 23 errors in context 36 of 48:
==4646== Use of uninitialised value of size 8
==4646==    at 0x18DA50: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 23 errors in context 37 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA43: picoquic_skip_frame (frames.c:4970)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 23 errors in context 38 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DA3F: picoquic_skip_frame (frames.c:4966)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 29 errors in context 39 of 48:
==4646== Use of uninitialised value of size 8
==4646==    at 0x1A23EF: picohash_retrieve (picohash.c:56)
==4646==    by 0x1ADC0D: picoquic_cnx_by_secret (quicctx.c:4020)
==4646==    by 0x19DC10: picoquic_parse_header_and_decrypt (packet.c:822)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 29 errors in context 40 of 48:
==4646== Use of uninitialised value of size 8
==4646==    at 0x1A23EF: picohash_retrieve (picohash.c:56)
==4646==    by 0x1ADA33: picoquic_cnx_by_id (quicctx.c:3960)
==4646==    by 0x19DAED: picoquic_parse_short_packet_header (packet.c:302)
==4646==    by 0x19DAED: picoquic_parse_packet_header (packet.c:376)
==4646==    by 0x19DAED: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 29 errors in context 41 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x19D72A: picoquic_parse_packet_header (packet.c:373)
==4646==    by 0x19D72A: picoquic_parse_header_and_decrypt (packet.c:711)
==4646==    by 0x1A03F9: picoquic_incoming_segment (packet.c:2146)
==4646==    by 0x1A15DE: picoquic_process_sooner_packets (packet.c:2497)
==4646==    by 0x1B9B7D: picoquic_prepare_packet_ex (sender.c:4566)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1A98D9: picoquic_stream_data_node_alloc (quicctx.c:2419)
==4646==    by 0x1A03A5: picoquic_incoming_segment (packet.c:2140)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 44 errors in context 42 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153930: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1596)
==4646==    by 0x15B0E3: zero_rtt_test_one (tls_api_test.c:3628)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 50 errors in context 43 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153923: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0E3: zero_rtt_test_one (tls_api_test.c:3628)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 60 errors in context 44 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B1619: picoquic_copy_before_retransmit (sender.c:1397)
==4646==    by 0x1B1C86: picoquic_retransmit_needed_packet (sender.c:1562)
==4646==    by 0x1B2284: picoquic_retransmit_needed_loop (sender.c:1722)
==4646==    by 0x1B2284: picoquic_retransmit_needed (sender.c:1806)
==4646==    by 0x1B79F5: picoquic_prepare_packet_almost_ready (sender.c:3521)
==4646==    by 0x1B9D8B: picoquic_prepare_segment (sender.c:4259)
==4646==    by 0x1B9D8B: picoquic_prepare_packet_ex (sender.c:4659)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 95 errors in context 45 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B28F7: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B28F7: picoquic_is_cnx_backlog_empty (sender.c:1855)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 156 errors in context 46 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x18DFA1: picoquic_process_possible_ack_of_ack_frame (frames.c:2834)
==4646==    by 0x18ED60: picoquic_process_ack_range (frames.c:2900)
==4646==    by 0x18ED60: picoquic_decode_ack_frame (frames.c:3001)
==4646==    by 0x18F3DE: picoquic_decode_frames (frames.c:4617)
==4646==    by 0x19FF26: picoquic_incoming_1rtt (packet.c:2018)
==4646==    by 0x1A0A20: picoquic_incoming_segment (packet.c:2315)
==4646==    by 0x1A1360: picoquic_incoming_packet_ex (packet.c:2420)
==4646==    by 0x1A1417: picoquic_incoming_packet (packet.c:2456)
==4646==    by 0x152F73: tls_api_one_sim_link_arrival (tls_api_test.c:1189)
==4646==    by 0x152F73: tls_api_one_sim_round (tls_api_test.c:1472)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 205 errors in context 47 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A88: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1596)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x152C62: tls_api_one_sim_round (tls_api_test.c:1371)
==4646==    by 0x153C98: tls_api_connection_loop (tls_api_test.c:1506)
==4646==    by 0x15AF32: zero_rtt_test_one (tls_api_test.c:3599)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== 
==4646== 1552 errors in context 48 of 48:
==4646== Conditional jump or move depends on uninitialised value(s)
==4646==    at 0x18DBFB: picoquic_skip_0len_frame (frames.c:3852)
==4646==    by 0x18DBFB: picoquic_skip_frame (frames.c:4978)
==4646==    by 0x1B2717: picoquic_is_pkt_ctx_backlog_empty (sender.c:1832)
==4646==    by 0x1B2717: picoquic_is_cnx_backlog_empty (sender.c:1867)
==4646==    by 0x153A7B: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1595)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646==  Uninitialised value was created by a heap allocation
==4646==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==4646==    by 0x1B9C72: picoquic_create_packet (sender.c:367)
==4646==    by 0x1B9C72: picoquic_prepare_packet_ex (sender.c:4652)
==4646==    by 0x1529D0: tls_api_one_sim_round (tls_api_test.c:1345)
==4646==    by 0x153A51: tls_api_synch_to_empty_loop.constprop.0 (tls_api_test.c:1586)
==4646==    by 0x15B0B2: zero_rtt_test_one (tls_api_test.c:3617)
==4646==    by 0x15BAEF: zero_rtt_many_losses_test (tls_api_test.c:3805)
==4646==    by 0x12E633: do_one_test (picoquic_t.c:367)
==4646==    by 0x12DDB6: main (picoquic_t.c:667)
==4646== 
==4646== ERROR SUMMARY: 2649 errors from 48 contexts (suppressed: 0 from 0)
Error: Process completed with exit code 1.
0s
0s
