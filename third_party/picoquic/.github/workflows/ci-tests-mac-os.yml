---
name: "CITestsMacOS"

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  citests:
    name: CI-Tests-MacOS
    runs-on: macos-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          # We must fetch at least the immediate parents so that if this is
          # a pull request then we can checkout the head.
          fetch-depth: 2
          submodules: 'recursive'

      - name: Building picoquic
        run: |
          # brew reinstall pkg-config
          brew install openssl
          whereis openssl
          export PKG_CONFIG_PATH="/usr/local/opt/openssl@3/lib/pkgconfig"
          ./ci/build_picotls.sh
          cmake -S . -B build
          cmake --build build

      - name: Perform Unit Tests
        run: |
            ulimit -c unlimited -S
            cd build
            ./picoquic_ct -S .. -n -r && QUICRESULT=$?
            ./picohttp_ct -S .. -n -r -x http_corrupt && HTTPRESULT=$?
            if [[ ${QUICRESULT} == 0 ]] && [[ ${HTTPRESULT} == 0 ]]; then exit 0; fi;
            exit 1
