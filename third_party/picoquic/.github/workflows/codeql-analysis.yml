---
name: "CodeQL"

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          # We must fetch at least the immediate parents so that if this is
          # a pull request then we can checkout the head.
          fetch-depth: 2
          submodules: 'recursive'

      # Initializes the CodeQL tools for scanning.
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: cpp
          config-file: ./.github/codeql/codeql-config.yml

      - name: Building picoquic
        run: |
          sudo apt-get install -y libssl-dev
          ./ci/build_picotls.sh
          mkdir build
          cd build
          cmake ..
          cmake --build .
          make

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
