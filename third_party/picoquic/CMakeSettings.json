{
    // See https://go.microsoft.com//fwlink//?linkid=834763 for more information about this file.
    "configurations": [
        {
        "name": "x86-Debug",
        "generator": "Visual Studio 15 2017",
        "configurationType" : "Debug",
        "buildRoot":  "${env.LOCALAPPDATA}\\CMakeBuild\\${workspaceHash}\\build\\${name}",
        "cmakeCommandArgs":  "",
        "buildCommandArgs": "-m -v:minimal"
        },
        {
        "name": "x86-Release",
        "generator": "Visual Studio 15 2017",
        "configurationType" : "Release",
        "buildRoot":  "${env.LOCALAPPDATA}\\CMakeBuild\\${workspaceHash}\\build\\${name}",
        "cmakeCommandArgs":  "",
        "buildCommandArgs": "-m -v:minimal"
        },
        {
        "name": "x64-Debug",
        "generator": "Visual Studio 15 2017 Win64",
        "configurationType" : "Debug",
        "buildRoot":  "${env.LOCALAPPDATA}\\CMakeBuild\\${workspaceHash}\\build\\${name}",
        "cmakeCommandArgs":  "",
        "buildCommandArgs": "-m -v:minimal"
        },
        {
        "name": "x64-Release",
        "generator": "Visual Studio 15 2017 Win64",
        "configurationType" : "Release",
        "buildRoot":  "${env.LOCALAPPDATA}\\CMakeBuild\\${workspaceHash}\\build\\${name}",
        "cmakeCommandArgs":  "",
        "buildCommandArgs": "-m -v:minimal"
        }
    ]
}