{"qlog_version": "draft-00", "title": "picoquic", "traces": [{"vantage_point": {"name": "backend-67", "type": "server"}, "title": "picoquic", "description": "0102030405060708", "event_fields": ["relative_time", "category", "event", "data"], "configuration": {"time_units": "us"}, "common_fields": {"protocol_type": "QUIC_HTTP3", "reference_time": "11001"}, "events": [[0, "transport", "datagram_received", {"byte_length": 1252, "addr_from": {"ip_v4": "********", "port_v4": 1234}, "addr_to": {"ip_v4": "********", "port_v4": 4321}}], [0, "transport", "packet_received", {"packet_type": "initial", "header": {"packet_size": 1252, "packet_number": 0, "version": "50435130", "payload_length": 1206, "scid": "0203040506070809", "dcid": "0102030405060708"}, "frames": [{"frame_type": "crypto", "offset": 0, "length": 279}, {"frame_type": "padding"}]}], [0, "info", "message", {"message": "ALPN[0] matches default alpn (picoquic-test)"}], [0, "transport", "parameters_set", {"owner": "remote", "sni": "test.example.com", "proposed_alpn": ["picoquic-test"], "alpn": "picoquic-test"}], [0, "transport", "parameters_set", {"owner": "remote", "initial_max_stream_data_bidi_local": 2097152, "initial_max_data": 1048576, "initial_max_streams_bidi": 512, "idle_timeout": 30000, "max_packet_size": 1440, "initial_max_streams_uni": 512, "initial_max_stream_data_bidi_remote": 65635, "initial_max_stream_data_uni": 65535, "active_connection_id_limit": 8, "max_ack_delay": 10, "handshake_connection_id": "0203040506070809", "enable_loss_bit": 1, "min_ack_delay": 1000}], [0, "transport", "parameters_set", {"owner": "local", "initial_max_stream_data_bidi_local": 2097152, "initial_max_data": 1048576, "initial_max_streams_bidi": 512, "idle_timeout": 30000, "max_packet_size": 1440, "initial_max_streams_uni": 512, "initial_max_stream_data_bidi_remote": 65635, "initial_max_stream_data_uni": 65535, "active_connection_id_limit": 8, "max_ack_delay": 10, "handshake_connection_id": "030405060708090a", "original_connection_id": "0102030405060708", "stateless_reset_token": "230e17ae186336770e58ec2e8cd5470e", "enable_loss_bit": 1, "min_ack_delay": 1000}], [0, "transport", "packet_sent", {"packet_type": "initial", "header": {"packet_size": 165, "packet_number": 0, "payload_length": 135, "scid": "030405060708090a", "dcid": "0203040506070809"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[0, 0]], "ect0": 1, "ect1": 0, "ce": 0}, {"frame_type": "crypto", "offset": 0, "length": 123}]}], [0, "transport", "packet_sent", {"packet_type": "handshake", "header": {"packet_size": 1055, "packet_number": 0, "payload_length": 1026, "scid": "030405060708090a", "dcid": "0203040506070809"}, "frames": [{"frame_type": "crypto", "offset": 0, "length": 1022}]}], [0, "transport", "datagram_sent", {"byte_length": 1252}], [0, "transport", "packet_sent", {"packet_type": "handshake", "header": {"packet_size": 275, "packet_number": 1, "payload_length": 246, "scid": "030405060708090a", "dcid": "0203040506070809"}, "frames": [{"frame_type": "crypto", "offset": 1022, "length": 241}]}], [0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 206, "packet_number": 0, "dcid": "0203040506070809", "key_phase": 0}, "frames": [{"frame_type": "new_connection_id", "sequence_number": 1, "retire_before": 0, "connection_id": "040405060708090a", "reset_token": "d6e409cb9d35622d49e3e583c238ea9e"}, {"frame_type": "new_connection_id", "sequence_number": 2, "retire_before": 0, "connection_id": "050405060708090a", "reset_token": "7ed70f3ad290d13907d8f7d09b75ee1f"}, {"frame_type": "new_connection_id", "sequence_number": 3, "retire_before": 0, "connection_id": "060405060708090a", "reset_token": "47b95f2634e0ca5b33fb859374efb571"}, {"frame_type": "new_connection_id", "sequence_number": 4, "retire_before": 0, "connection_id": "070405060708090a", "reset_token": "75da8ae8af3ddcff90c521a067d12e7c"}, {"frame_type": "new_connection_id", "sequence_number": 5, "retire_before": 0, "connection_id": "080405060708090a", "reset_token": "295a4f380f9f9fc3cdccc828fdee2e7c"}, {"frame_type": "new_connection_id", "sequence_number": 6, "retire_before": 0, "connection_id": "090405060708090a", "reset_token": "6f44e15195c11c82e4e48ba81cd58b13"}, {"frame_type": "new_connection_id", "sequence_number": 7, "retire_before": 0, "connection_id": "0a0405060708090a", "reset_token": "4a461f6bf59ca273a5b764438d7b7906"}]}], [0, "recovery", "metrics_updated", {"cwnd": 15360, "bytes_in_flight": 1765}], [0, "transport", "datagram_sent", {"byte_length": 513}], [0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 1, "dcid": "0203040506070809"}, "frames": [{"frame_type": "ping"}, {"frame_type": "padding"}]}], [0, "recovery", "metrics_updated", {"bytes_in_flight": 3205}], [0, "transport", "datagram_sent", {"byte_length": 1440}], [21043, "transport", "datagram_received", {"byte_length": 53}], [21043, "transport", "packet_received", {"packet_type": "handshake", "header": {"packet_size": 53, "packet_number": 0, "payload_length": 8, "scid": "0203040506070809", "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[0, 0]], "ect0": 1, "ect1": 0, "ce": 0}]}], [21662, "transport", "datagram_received", {"byte_length": 314}], [21662, "transport", "packet_received", {"packet_type": "handshake", "header": {"packet_size": 92, "packet_number": 1, "payload_length": 47, "scid": "0203040506070809", "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[0, 1]], "ect0": 2, "ect1": 0, "ce": 0}, {"frame_type": "crypto", "offset": 0, "length": 36}]}], [21662, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 222, "packet_number": 0, "dcid": "030405060708090a", "key_phase": 0}, "frames": [{"frame_type": "new_connection_id", "sequence_number": 1, "retire_before": 0, "connection_id": "0303040506070809", "reset_token": "053eb6a0de938613052030c0fbf65e05"}, {"frame_type": "new_connection_id", "sequence_number": 2, "retire_before": 0, "connection_id": "0403040506070809", "reset_token": "f6a690832b3e443e1cabc8ffcbf5b11a"}, {"frame_type": "new_connection_id", "sequence_number": 3, "retire_before": 0, "connection_id": "0503040506070809", "reset_token": "f4522daa3e83e5786ac2679fd899e971"}, {"frame_type": "new_connection_id", "sequence_number": 4, "retire_before": 0, "connection_id": "0603040506070809", "reset_token": "42ccd34b9fdca6a9efe68f7637904804"}, {"frame_type": "new_connection_id", "sequence_number": 5, "retire_before": 0, "connection_id": "0703040506070809", "reset_token": "cbf38915760d994ae0bdda3fd5000b90"}, {"frame_type": "new_connection_id", "sequence_number": 6, "retire_before": 0, "connection_id": "0803040506070809", "reset_token": "067b59f6cd452cc9b3d45e1349c3d427"}, {"frame_type": "new_connection_id", "sequence_number": 7, "retire_before": 0, "connection_id": "0903040506070809", "reset_token": "cdb10de67904bc850f26f43cede090d0"}]}], [21662, "recovery", "metrics_updated", {"pacing_rate": 7268505, "bytes_in_flight": 1662, "smoothed_rtt": 21120, "min_rtt": 21043, "latest_rtt": 21662, "app_limited": 1}], [21662, "transport", "spin_bit_updated", {"state": true}], [21662, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 39, "packet_number": 2, "dcid": "0203040506070809"}, "frames": [{"frame_type": "ack_frequency", "sequence_number": 0, "packet_tolerance": 2, "max_ack_delay": 5260, "reordering_threshold": 0}, {"frame_type": "handshake_done"}, {"frame_type": "padding"}]}], [21662, "recovery", "metrics_updated", {"bytes_in_flight": 1717, "smoothed_rtt": 21120}], [21662, "transport", "datagram_sent", {"byte_length": 55}], [22319, "transport", "datagram_received", {"byte_length": 822}], [22319, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 822, "packet_number": 1, "dcid": "030405060708090a"}, "frames": [{"frame_type": "stream", "id": 4, "offset": 0, "length": 257, "fin": true, "begins_with": "0001020304050607"}, {"frame_type": "stream", "id": 8, "offset": 0, "length": 531, "fin": true, "begins_with": "0001020304050607"}]}], [22319, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1236, "packet_number": 3, "dcid": "0203040506070809"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[0, 1]], "ect0": 2, "ect1": 0, "ce": 0}, {"frame_type": "stream", "id": 4, "offset": 0, "length": 1216, "fin": false, "has_length": false, "begins_with": "0001020304050607"}]}], [22319, "recovery", "metrics_updated", {"bytes_in_flight": 2969, "smoothed_rtt": 21120}], [22319, "transport", "datagram_sent", {"byte_length": 1252}], [22319, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1236, "packet_number": 4, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 4, "offset": 1216, "length": 784, "fin": true, "begins_with": "c0c1c2c3c4c5c6c7"}, {"frame_type": "stream", "id": 8, "offset": 0, "length": 434, "fin": false, "has_length": false, "begins_with": "0001020304050607"}]}], [22319, "recovery", "metrics_updated", {"bytes_in_flight": 4221, "smoothed_rtt": 21120}], [22319, "transport", "datagram_sent", {"byte_length": 1252}], [22319, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1236, "packet_number": 5, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 434, "length": 1222, "fin": false, "has_length": false, "begins_with": "b2b3b4b5b6b7b8b9"}]}], [22319, "recovery", "metrics_updated", {"bytes_in_flight": 5473, "smoothed_rtt": 21120}], [22319, "transport", "datagram_sent", {"byte_length": 1252}], [23470, "transport", "datagram_received", {"byte_length": 1440}], [23470, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 1440, "packet_number": 2, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ping"}, {"frame_type": "padding"}]}], [23604, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1236, "packet_number": 6, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 1656, "length": 1222, "fin": false, "has_length": false, "begins_with": "78797a7b7c7d7e7f"}]}], [23604, "recovery", "metrics_updated", {"bytes_in_flight": 6725, "smoothed_rtt": 21120}], [23604, "transport", "datagram_sent", {"byte_length": 1252}], [23605, "transport", "datagram_received", {"byte_length": 55}], [23605, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 4, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 125, "acked_ranges": [[0, 1]], "ect0": 2, "ect1": 0, "ce": 0}, {"frame_type": "padding"}]}], [23605, "recovery", "metrics_updated", {"pacing_rate": 7209011, "bytes_in_flight": 5063, "smoothed_rtt": 21305, "latest_rtt": 22605}], [25202, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 7, "dcid": "0203040506070809"}, "frames": [{"frame_type": "ack", "ack_delay": 199, "acked_ranges": [[4, 4], [0, 2]], "ect0": 4, "ect1": 0, "ce": 0}, {"frame_type": "stream", "id": 8, "offset": 2878, "length": 1399, "fin": false, "has_length": false, "begins_with": "3e3f404142434445"}]}], [25202, "recovery", "metrics_updated", {"bytes_in_flight": 6503, "smoothed_rtt": 21305}], [25202, "transport", "datagram_sent", {"byte_length": 1440}], [26800, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 8, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 4277, "length": 1410, "fin": false, "has_length": false, "begins_with": "b5b6b7b8b9babbbc"}]}], [26800, "recovery", "metrics_updated", {"bytes_in_flight": 7943, "smoothed_rtt": 21305}], [26800, "transport", "datagram_sent", {"byte_length": 1440}], [28398, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 9, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 5687, "length": 1410, "fin": false, "has_length": false, "begins_with": "3738393a3b3c3d3e"}]}], [28398, "recovery", "metrics_updated", {"bytes_in_flight": 9383, "smoothed_rtt": 21305}], [28398, "transport", "datagram_sent", {"byte_length": 1440}], [29996, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 10, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 7097, "length": 1410, "fin": false, "has_length": false, "begins_with": "b9babbbcbdbebfc0"}]}], [29996, "recovery", "metrics_updated", {"bytes_in_flight": 10823, "smoothed_rtt": 21305}], [29996, "transport", "datagram_sent", {"byte_length": 1440}], [31594, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 11, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 8507, "length": 1410, "fin": false, "has_length": false, "begins_with": "3b3c3d3e3f404142"}]}], [31594, "recovery", "metrics_updated", {"bytes_in_flight": 12263, "smoothed_rtt": 21305}], [31594, "transport", "datagram_sent", {"byte_length": 1440}], [33192, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1099, "packet_number": 12, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 9917, "length": 1083, "fin": true, "begins_with": "bdbebfc0c1c2c3c4"}]}], [33192, "recovery", "metrics_updated", {"bytes_in_flight": 13378, "smoothed_rtt": 21305}], [33192, "transport", "datagram_sent", {"byte_length": 1115}], [41748, "transport", "datagram_received", {"byte_length": 55}], [41748, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 5, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack_frequency", "sequence_number": 0, "packet_tolerance": 2, "max_ack_delay": 5500, "reordering_threshold": 0}, {"frame_type": "padding"}]}], [45365, "transport", "datagram_received", {"byte_length": 55}], [45365, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 7, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[0, 5]], "ect0": 6, "ect1": 0, "ce": 0}, {"frame_type": "padding"}]}], [45365, "recovery", "metrics_updated", {"pacing_rate": 7133126, "bytes_in_flight": 9567, "smoothed_rtt": 21522, "latest_rtt": 23046}], [45365, "transport", "spin_bit_updated", {"state": false}], [45365, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 39, "packet_number": 13, "dcid": "0203040506070809"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[7, 7], [4, 5], [0, 2]], "ect0": 6, "ect1": 0, "ce": 0}, {"frame_type": "padding"}]}], [45365, "recovery", "metrics_updated", {"bytes_in_flight": 9622, "smoothed_rtt": 21522}], [45365, "transport", "datagram_sent", {"byte_length": 55}], [48668, "transport", "datagram_received", {"byte_length": 55}], [48668, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 8, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[8, 8], [0, 6]], "ect0": 8, "ect1": 0, "ce": 0}, {"frame_type": "padding"}]}], [48668, "recovery", "metrics_updated", {"pacing_rate": 7119901, "bytes_in_flight": 6930, "smoothed_rtt": 21565, "latest_rtt": 21868}], [51190, "transport", "datagram_received", {"byte_length": 55}], [51190, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 9, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[10, 10], [8, 8], [0, 6]], "ect0": 9, "ect1": 0, "ce": 0}, {"frame_type": "padding"}]}], [51190, "recovery", "packet_lost", {"packet_type": "1RTT", "packet_number": 7, "trigger": "repeat", "header": {"packet_type": "1RTT", "packet_number": 7, "dcid": "0203040506070809", "packet_size": 1424}}], [51190, "recovery", "metrics_updated", {"cwnd": 7680, "pacing_rate": 11520000000, "bytes_in_flight": 4050, "smoothed_rtt": 21518, "latest_rtt": 21194}], [51190, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1415, "packet_number": 14, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 2878, "length": 1399, "fin": false, "begins_with": "3e3f404142434445"}]}], [51190, "recovery", "metrics_updated", {"bytes_in_flight": 5481, "smoothed_rtt": 21518}], [51190, "transport", "datagram_sent", {"byte_length": 1431}], [54126, "transport", "datagram_received", {"byte_length": 55}], [54126, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 10, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[10, 12], [8, 8], [0, 6]], "ect0": 11, "ect1": 0, "ce": 0}, {"frame_type": "padding"}]}], [54126, "recovery", "packet_lost", {"packet_type": "1RTT", "packet_number": 9, "trigger": "repeat", "header": {"packet_type": "1RTT", "packet_number": 9, "dcid": "0203040506070809", "packet_size": 1424}}], [54126, "recovery", "metrics_updated", {"bytes_in_flight": 1486, "smoothed_rtt": 21445, "min_rtt": 20934, "latest_rtt": 20934}], [54126, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 15, "dcid": "0203040506070809"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 5687, "length": 1410, "fin": false, "has_length": false, "begins_with": "3738393a3b3c3d3e"}]}], [54126, "recovery", "metrics_updated", {"bytes_in_flight": 2926, "smoothed_rtt": 21445}], [54126, "transport", "datagram_sent", {"byte_length": 1440}], [72377, "transport", "datagram_received", {"byte_length": 55}], [72377, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 11, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[10, 14], [8, 8], [0, 6]], "ect0": 13, "ect1": 0, "ce": 0}, {"frame_type": "padding"}]}], [72377, "recovery", "metrics_updated", {"bytes_in_flight": 1440, "smoothed_rtt": 21412, "latest_rtt": 21187}], [80580, "transport", "datagram_received", {"byte_length": 55}], [80580, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 12, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 657, "acked_ranges": [[10, 15], [8, 8], [0, 6]], "ect0": 14, "ect1": 0, "ce": 0}, {"frame_type": "padding"}]}], [80580, "recovery", "metrics_updated", {"bytes_in_flight": 0, "smoothed_rtt": 21385, "latest_rtt": 21198}], [90610, "transport", "datagram_received", {"byte_length": 38}], [90610, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 38, "packet_number": 13, "dcid": "030405060708090a"}, "frames": [{"frame_type": "ack", "ack_delay": 1912, "acked_ranges": [[10, 15]], "ect0": 14, "ect1": 0, "ce": 0}, {"frame_type": "connection_close", "error_space": "application", "error_code": 0}]}], [90610, "transport", "spin_bit_updated", {"state": true}], [90610, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 15, "packet_number": 16, "dcid": "0203040506070809"}, "frames": [{"frame_type": "connection_close", "error_space": "transport", "error_code": 0}]}], [90610, "transport", "datagram_sent", {"byte_length": 31}], [161718, "transport", "datagram_received", {"byte_length": 256}], [161718, "transport", "packet_dropped", {"packet_type": "1RTT", "packet_size": 256, "trigger": "payload_decrypt_error"}]]}]}