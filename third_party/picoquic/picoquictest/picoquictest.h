/*
* Author: <PERSON>
* Copyright (c) 2017, Private Octopus, Inc.
* All rights reserved.
*
* Permission to use, copy, modify, and distribute this software for any
* purpose with or without fee is hereby granted, provided that the above
* copyright notice and this permission notice appear in all copies.
*
* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CO<PERSON><PERSON>BUTORS "AS IS" AND
* ANY EXPRESS OR IMP<PERSON>IED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
* WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
* DISCLAIMED. IN NO EVENT SHALL Private Octopus, Inc. BE LIABLE FOR ANY
* DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
* LOSS OF USE, DATA, OR PROFI<PERSON>; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
* ON ANY THEORY OF LIABILITY, WH<PERSON>HER IN CONTRACT, STRICT LIABILITY, OR TORT
* (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
* SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

#ifndef PICOQUICTEST_H
#define PICOQUICTEST_H

#ifdef __cplusplus
extern "C" {
#endif

/* From picoquic/picoquic_utils.h */
void debug_printf_suspend();

/* Control variables for the duration of the stress test */

extern uint64_t picoquic_stress_test_duration; /* In microseconds; defaults to 1 minute */
extern size_t picoquic_stress_nb_clients; /* number of stress clients, defaults to 4 */

/* List of test functions */
int util_connection_id_print_test();
int util_connection_id_parse_test();
int util_sprintf_test();
int util_debug_print_test();
int util_uint8_to_str_test();
int util_memcmp_test();
int util_threading_test();
int picohash_test();
int picohash_bytes_test();
int siphash_test();
int picohash_embedded_test();
int picolog_basic_test();
int bytestream_test();
int create_cnx_test();
int create_quic_test();
int parseheadertest();
int incoming_initial_test();
int header_length_test();
int pn2pn64test();
int intformattest();
int sacktest();
int StreamZeroFrameTest();
int sendacktest();
int sendack_loop_test();
int ackfrq_basic_test();
int ackfrq_short_test();
#if 0
/* The TLS API connect test is only useful when debugging issues step by step */
int tls_api_connect_test();
#endif
int tls_api_test();
int tls_api_inject_hs_ack_test();
int tls_api_silence_test();
int tls_api_loss_test(uint64_t mask);
int tls_api_client_first_loss_test();
int tls_api_client_second_loss_test();
int tls_api_server_first_loss_test();
int tls_api_many_losses();
int initial_ping_test();
int initial_ping_ack_test();
int code_version_test();
int tls_api_version_negotiation_test();
int tls_api_version_invariant_test();
int test_version_negotiation_spoof();
int transport_param_test();
int tls_api_sni_test();
int tls_api_alpn_test();
int tls_api_wrong_alpn_test();
int tls_api_oneway_stream_test();
int tls_api_q_and_r_stream_test();
int tls_api_q2_and_r2_stream_test();
int implicit_ack_test();
int stateless_reset_test();
int stateless_reset_bad_test();
int stateless_reset_client_test();
int stateless_reset_handshake_test();
int immediate_close_test();
int sim_link_test();
int tls_api_very_long_stream_test();
int tls_api_very_long_max_test();
int tls_api_very_long_with_err_test();
int tls_api_very_long_congestion_test();
int tls_api_retry_test();
int tls_api_retry_large_test();
int ackrange_test();
int ack_of_ack_test();
int ack_disorder_test();
int ack_horizon_test();
int tls_api_two_connections_test();
int cleartext_aead_test();
int tls_api_multiple_versions_test();
int varint_test();
int sqrt_for_test_test();
int tls_api_client_losses_test();
int tls_api_server_losses_test();
int skip_frame_test();
int keep_alive_test();
int logger_test();
int binlog_test();
int app_message_overflow_test();
int socket_test();
int test_stateless_blowback();
int ticket_store_test();
int ticket_seed_test();
int ticket_seed_from_bdp_frame_test();
int token_store_test();
int session_resume_test();
int zero_rtt_test();
int zero_rtt_loss_test();
int stop_sending_test();
int stop_sending_loss_test();
int discard_stream_test();
int unidir_test();
int mtu_discovery_test();
int mtu_blocked_test();
int mtu_delayed_test();
int mtu_required_test();
int mtu_max_test();
int mtu_drop_bbr_test();
int mtu_drop_cubic_test();
int mtu_drop_dcubic_test();
int mtu_drop_fast_test();
int mtu_drop_newreno_test();
int spurious_retransmit_test();
int pn_ctr_test();
int cleartext_pn_enc_test();
int pn_enc_1rtt_test();
int tls_zero_share_test();
int transport_param_log_test();
int bad_certificate_test();
int set_verify_certificate_callback_test();
int virtual_time_test();
int tls_different_params_test();
int tls_quant_params_test();
int set_certificate_and_key_test();
int transport_param_stream_id_test();
int vn_tp_test();
int vn_compat_test();
int request_client_authentication_test();
int bad_client_certificate_test();
int nat_rebinding_test();
int nat_rebinding_loss_test();
int nat_rebinding_zero_test();
int nat_rebinding_latency_test();
int spinbit_test();
int spinbit_random_test();
int spinbit_randclient_test();
int spinbit_null_test();
int spinbit_bad_test();
int loss_bit_test();
int client_error_test();
int client_only_test();
int packet_enc_dec_test();
int cleartext_pn_vector_test();
int zero_rtt_spurious_test();
int zero_rtt_retry_test();
int zero_rtt_no_coal_test();
int zero_rtt_many_losses_test();
int zero_rtt_long_test();
int zero_rtt_delay_test();
int parse_frame_test();
int frames_repeat_test();
int frames_ackack_error_test();
int frames_format_test();
int stress_test();
int cnx_stress_unit_test();
int cnx_stress_do_test(uint64_t duration, int nb_clients, int do_report);
int cnx_ddos_unit_test();
int cnx_ddos_test_loop(int nb_connections, uint64_t ddos_interval, const char* qlogdir);
int sockloop_basic_test();
int sockloop_eio_test();
int sockloop_errsock_test();
int sockloop_ipv4_test();
int sockloop_migration_test();
int sockloop_nat_test();
int sockloop_thread_test();
int sockloop_thread_name_test();
int splay_test();
int TlsStreamFrameTest();
int draft17_vector_test();
int dtn_basic_test();
int dtn_data_test();
int dtn_silence_test();
int dtn_twenty_test();
int fuzz_test();
int random_tester_test();
int random_gauss_test();
int random_public_tester_test();
int cnxid_stash_test();
int new_cnxid_test();
int transmit_cnxid_test();
int transmit_cnxid_disable_test();
int transmit_cnxid_retire_before_test();
int transmit_cnxid_retire_disable_test();
int transmit_cnxid_retire_early_test();
int probe_api_test();
int memlog_test();
int migration_test();
int migration_test_long(); 
int migration_test_loss();
int migration_zero_test();
int migration_fail_test();
int cnxid_renewal_test();
int retire_cnxid_test();
int server_busy_test();
int initial_close_test();
int fuzz_initial_test();
int new_rotated_key_test();
int key_rotation_test();
int key_rotation_auto_server();
int key_rotation_auto_client();
int false_migration_test();
int nat_handshake_test();
int key_rotation_vector_test();
int key_rotation_stress_test();
int keylog_test();
int short_initial_cid_test();
int stream_id_max_test();
int padding_test();
int padding_null_test();
int padding_zero_min_test();
int packet_trace_test();
int qlog_auto_test();
int qlog_error_test();
int qlog_trace_test();
int qlog_trace_auto_test();
int qlog_trace_only_test();
int qlog_trace_ecn_test();
int perflog_test();
int rebinding_stress_test();
int many_short_loss_test();
int random_padding_test();
int ec00_zero_test();
int ec2f_second_flight_nack_test();
void eccf_corrupted_file_fuzz(int nb_trials, uint64_t seed, FILE* seed_report);
int eccf_corrupted_file_fuzz_test();
int eca1_amplification_loss_test();
int ecf1_final_loss_test();
int ec5c_silly_cid_test();
int ec9a_preemptive_amok_test();
int error_reason_test();
int idle_server_test();
int idle_timeout_test();
int reset_ack_max_test();
int reset_ack_reset_test();
int reset_extra_max_test();
int reset_extra_reset_test();
int reset_extra_stop_test();
int reset_need_max_test();
int reset_need_reset_test();
int reset_need_stop_test();
int initial_pto_test();
int initial_pto_srv_test();
int ready_to_send_test();
int ready_to_skip_test();
int ready_to_zero_test();
int ready_to_zfin_test();
int crypto_hs_offset_test();
int cubic_test();
int cubic_jitter_test();
int cc_compete_cubic2_test();
int cc_compete_prague2_test();
int cc_compete_d_cubic_test();
int cc_ns_asym_test();
int cc_ns_blackhole_test();
int cc_ns_drop_and_back_test();
int cc_ns_low_and_up_test();
int cc_ns_wifi_fade_test();
int cc_ns_wifi_suspension_test();
int cc_ns_wifi_bad_cubic_test();
int cc_ns_wifi_bad_bbr_test();
int cc_ns_varylink_test();
int cc_ns_satellite_test();
int cc_ns_media_test();
int satellite_basic_test();
int satellite_seeded_test();
int satellite_seeded_bbr1_test();
int satellite_loss_test();
int satellite_loss_fc_test();
int satellite_jitter_test();
int satellite_medium_test();
int satellite_preemptive_test();
int satellite_preemptive_fc_test();
int satellite_small_test();
int satellite_small_up_test();
int satellite_bbr1_test();
int satellite_cubic_test();
int satellite_cubic_seeded_test();
int satellite_cubic_loss_test();
int satellite_dcubic_seeded_test();
int satellite_prague_seeded_test();
int bdp_basic_test();
int bdp_reno_test();
int bdp_cubic_test();
int bdp_bbr1_test();
int bdp_rtt_test();
int bdp_ip_test();
int bdp_delay_test();
int bdp_short_test();
int bdp_short_hi_test();
int bdp_short_lo_test();
int long_rtt_test();
int high_latency_basic_test();
int high_latency_bbr_test();
int high_latency_cubic_test();
int high_latency_probeRTT_test();
int cid_length_test();
int initial_server_close_test();
int h3zero_integer_test();
int h3zero_varint_stream_test();
int h3zero_incoming_unidir_test();
int h3zero_unidir_error_test();
int h3zero_setting_error_test();
int h3zero_capsule_test();
int h3zero_client_data_test();
int qpack_huffman_test();
int qpack_huffman_base_test();
int h3zero_parse_qpack_test();
int h3zero_prepare_qpack_test();
int h3zero_user_agent_test();
int h3zero_uri_test();
int h3zero_null_sni_test();
int h3zero_qpack_fuzz_test();
int h3zero_stream_test();
int h3zero_stream_fuzz_test();
int parse_demo_scenario_test();
int h3zero_server_test();
int h09_server_test();
int h09_header_test();
int generic_server_test();
int tls_retry_token_test();
int tls_retry_token_valid_test();
int optimistic_ack_test();
int optimistic_hole_test();
int document_addresses_test();
int socket_ecn_test();
int null_sni_test();
int preferred_address_test();
int preferred_address_dis_mig_test();
int preferred_address_zero_test();
int cid_for_lb_test();
int cid_for_lb_cli_test();
int retry_protection_vector_test();
int retry_protection_v2_test();
int test_copy_for_retransmit();
int dataqueue_copy_test();
int dataqueue_packet_test();
int bad_coalesce_test();
int bad_cnxid_test();
int stream_splay_test();
int stream_output_test();
int stream_rank_test();
int provide_stream_buffer_test();
int not_before_cnxid_test();
int send_stream_blocked_test();
int stream_ack_test();
int queue_network_input_test();
int fastcc_test();
int fastcc_jitter_test();
int bbr_test();
int bbr_jitter_test();
int bbr_long_test();
int bbr_performance_test();
int bbr_slow_long_test();
int bbr_one_second_test();
int bbr1_test();
int bbr1_long_test();
int gbps_performance_test();
int bbr_asym100_test();
int bbr_asym100_nodelay_test();
int bbr_asym400_test();
int l4s_reno_test();
int l4s_prague_test();
int l4s_prague_updown_test();
int l4s_bbr_test();
int l4s_bbr_updown_test();
int large_client_hello_test();
int limited_reno_test();
int limited_cubic_test();
int limited_bbr_test();
int limited_batch_test();
int limited_safe_test();
int fast_nat_rebinding_test();
int datagram_test();
int datagram_rt_test();
int datagram_rt_skip_test();
int datagram_rtnew_skip_test();
int datagram_loss_test();
int datagram_size_test();
int datagram_small_test();
int datagram_small_new_test();
int datagram_small_packet_test();
int datagram_wifi_test();
int ddos_amplification_test();
int ddos_amplification_0rtt_test();
int ddos_amplification_8k_test();
int blackhole_test();
int no_ack_frequency_test();
int immediate_ack_test();
int connection_drop_test();
int pacing_update_test();
int quality_update_test();
int direct_receive_test();
int address_discovery_test();
int app_limit_cc_test();
int app_limited_bbr_test();
int app_limited_cubic_test();
int app_limited_reno_test();
int app_limited_rpr_test();
int cwin_max_test();
int initial_race_test();
int pacing_test();
int pacing_repeat_test();
int chacha20_test();
int cnx_limit_test();
int cert_verify_bad_cert_test();
int cert_verify_bad_sni_test();
int cert_verify_null_test();
int cert_verify_null_sni_test();
int cert_verify_rsa_test();
int cid_quiescence_test();
int mediatest_video_test();
int mediatest_video_audio_test();
int mediatest_video_data_audio_test();
int mediatest_video2_down_test();
int mediatest_video2_back_test();
int mediatest_video2_probe_test();
int mediatest_wifi_test();
int mediatest_suspension_test();
int mediatest_suspension2_test();
int mediatest_worst_test();
int warptest_video_test();
int warptest_video_audio_test();
int warptest_video_data_audio_test();
int warptest_worst_test();
int warptest_param_test();
int wifi_bbr_test();
int wifi_bbr_hard_test();
int wifi_bbr_long_test();
int wifi_bbr_many_test();
int wifi_bbr_shadow_test();
int wifi_bbr1_test();
int wifi_bbr1_hard_test();
int wifi_bbr1_long_test();
int wifi_cubic_test();
int wifi_cubic_hard_test();
int wifi_cubic_long_test();
int wifi_reno_test();
int wifi_reno_hard_test();
int wifi_reno_long_test();
int migration_controlled_test();
int migration_mtu_drop_test();
int minicrypto_test();
int minicrypto_is_last_test();
#ifdef PICOQUIC_WITH_MBEDTLS
int mbedtls_crypto_test();
int mbedtls_load_key_test();
int mbedtls_load_key_fail_test();
int mbedtls_retrieve_pubkey_test();
int mbedtls_sign_verify_test();
int mbedtls_configure_test();
#endif
int mbedtls_test();
int openssl_cert_test();
int monopath_basic_test();
int monopath_hole_test();
int monopath_rotation_test();
int monopath_0rtt_test();
int monopath_0rtt_loss_test();
int multipath_aead_test();
int multipath_basic_test();
int multipath_fail_test();
int multipath_ab1_test();
int multipath_drop_first_test();
int multipath_drop_second_test();
int multipath_sat_plus_test();
int multipath_renew_test();
int multipath_rotation_test();
int multipath_nat_test();
int multipath_nat_challenge_test();
int multipath_break1_test();
int multipath_socket_error_test();
int multipath_abandon_test();
int multipath_back1_test();
int multipath_perf_test();
int multipath_callback_test();
int multipath_quality_test();
int multipath_stream_af_test();
int multipath_datagram_test();
int multipath_dg_af_test();
int multipath_backup_test();
int multipath_standup_test();
int multipath_discovery_test();
int multipath_qlog_test();
int multipath_tunnel_test();
int token_reuse_api_test();
int get_hash_test();
int get_tls_errors_test();
int ech_rr_test();
int ech_e2e_test();
int ech_grease_test();
int ech_cert_test();
int getter_test();
int grease_quic_bit_test();
int grease_quic_bit_one_way_test();
int pn_random_test();
int port_blocked_test();
int red_bbr_test();
int red_cubic_test();
int red_dcubic_test();
int red_fast_test();
int red_newreno_test();
int multi_segment_test();
int pacing_bbr_test();
int pacing_cubic_test();
int pacing_dcubic_test();
int pacing_fast_test();
int pacing_newreno_test();
int heavy_loss_test();
int heavy_loss_inter_test();
int heavy_loss_total_test();
int integrity_limit_test();
int excess_repeat_test();
int netperf_basic_test();
int netperf_bbr_test();
int nat_attack_test();
int config_option_letters_test();
int config_option_test();
int config_quic_test();
int config_usage_test();
int h3zero_post_test();
int h09_post_test();
int demo_alpn_test();
int demo_file_sanitize_test();
int demo_file_access_test();
int demo_server_file_test();
int demo_ticket_test();
int demo_error_test();
int h3zero_satellite_test();
int h09_satellite_test();
int h09_lone_fin_test();
int http_stress_test();
int http_corrupt_test();
int http_corrupt_rdpn_test();
int http_drop_test();
int h3_grease_client_test();
int h3_grease_server_test();
int h3_long_file_name_test();
int h3_multi_file_test();
int h3_multi_file_loss_test();
int h3_multi_file_preemptive_test();
int h09_multi_file_test();
int h09_multi_file_loss_test();
int h09_multi_file_preemptive_test();
int h3zero_settings_test();
int h3zero_get_content_type_by_path_test();
int picowt_baton_basic_test();
int picowt_baton_error_test();
int picowt_baton_long_test();
int picowt_baton_multi_test();
int picowt_baton_random_test();
int picowt_baton_wrong_test();
int picowt_baton_uri_test();
int picowt_drain_test();
int picowt_tp_test();
int quicperf_parse_test();
int quicperf_batch_test();
int quicperf_datagram_test();
int quicperf_media_test();
int quicperf_multi_test();
int quicperf_overflow_test();
int cplusplustest();

#ifdef __cplusplus
}
#endif

#endif /* PICOQUICTEST_H */
