{"qlog_version": "draft-00", "title": "picoquic", "traces": [{"vantage_point": {"name": "backend-67", "type": "server"}, "title": "picoquic", "description": "0807060504030201", "event_fields": ["relative_time", "path_id", "category", "event", "data"], "configuration": {"time_units": "us"}, "common_fields": {"protocol_type": "QUIC_HTTP3", "reference_time": "11001"}, "events": [[0, 0, "transport", "datagram_received", {"byte_length": 1252, "addr_from": {"ip_v4": "********", "port_v4": 1234}, "addr_to": {"ip_v4": "********", "port_v4": 4321}}], [0, 0, "transport", "packet_received", {"packet_type": "initial", "header": {"packet_size": 1252, "packet_number": 0, "version": "50435130", "payload_length": 1206, "scid": "0908070605040302", "dcid": "0807060504030201"}, "frames": [{"frame_type": "crypto", "offset": 0, "length": 295}, {"frame_type": "padding"}]}], [0, 0, "info", "message", {"message": "ALPN[0] matches default alpn (picoquic-test)"}], [0, 0, "transport", "parameters_set", {"owner": "remote", "sni": "test.example.com", "proposed_alpn": ["picoquic-test"], "alpn": "picoquic-test"}], [0, 0, "transport", "parameters_set", {"owner": "remote", "initial_max_stream_data_bidi_local": 2097152, "initial_max_data": 1048576, "initial_max_streams_bidi": 512, "idle_timeout": 30000, "max_packet_size": 1440, "initial_max_streams_uni": 512, "initial_max_stream_data_bidi_remote": 65635, "initial_max_stream_data_uni": 65535, "active_connection_id_limit": 8, "max_ack_delay": 10, "handshake_connection_id": "0908070605040302", "enable_loss_bit": 1, "min_ack_delay": 1000, "enable_time_stamp": "03", "initial_max_path_id": 2}], [0, 0, "transport", "parameters_set", {"owner": "local", "initial_max_stream_data_bidi_local": 2097152, "initial_max_data": 1048576, "initial_max_streams_bidi": 512, "idle_timeout": 30000, "max_packet_size": 1440, "initial_max_streams_uni": 512, "initial_max_stream_data_bidi_remote": 65635, "initial_max_stream_data_uni": 65535, "active_connection_id_limit": 8, "max_ack_delay": 10, "handshake_connection_id": "0a09080706050403", "original_connection_id": "0807060504030201", "stateless_reset_token": "c42ae0c5504960c189c5a9b441341c2d", "enable_loss_bit": 1, "min_ack_delay": 1000, "enable_time_stamp": "03", "initial_max_path_id": 2}], [0, 0, "transport", "packet_sent", {"packet_type": "initial", "header": {"packet_size": 162, "packet_number": 0, "payload_length": 132, "scid": "0a09080706050403", "dcid": "0908070605040302"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "crypto", "offset": 0, "length": 123}]}], [0, 0, "transport", "packet_sent", {"packet_type": "handshake", "header": {"packet_size": 1058, "packet_number": 0, "payload_length": 1029, "scid": "0a09080706050403", "dcid": "0908070605040302"}, "frames": [{"frame_type": "crypto", "offset": 0, "length": 1025}]}], [0, 0, "transport", "datagram_sent", {"byte_length": 1252}], [0, 0, "transport", "packet_sent", {"packet_type": "handshake", "header": {"packet_size": 288, "packet_number": 1, "payload_length": 259, "scid": "0a09080706050403", "dcid": "0908070605040302"}, "frames": [{"frame_type": "crypto", "offset": 1025, "length": 254}]}], [0, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 746, "packet_number": 0, "dcid": "0908070605040302", "key_phase": 0}, "frames": [{"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 1, "retire_before": 0, "connection_id": "0b09080706050403", "reset_token": "40a1911f8a3bcd9ed73b3f4c97e701e3"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 2, "retire_before": 0, "connection_id": "0c09080706050403", "reset_token": "77570626a0b6e29bcfdd79322b56d430"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 3, "retire_before": 0, "connection_id": "0d09080706050403", "reset_token": "84de0f957002a5e8a54d6738a17f9635"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 4, "retire_before": 0, "connection_id": "0e09080706050403", "reset_token": "7e94695330a607bdd8f91a3165a51bd0"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 5, "retire_before": 0, "connection_id": "0f09080706050403", "reset_token": "87802f62b607b2cd0a414509af6b82e2"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 6, "retire_before": 0, "connection_id": "1009080706050403", "reset_token": "efb5e32cf080487c19f89be9fff07c1e"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 7, "retire_before": 0, "connection_id": "1109080706050403", "reset_token": "079eb77401f689b7e69fdbc6b1623ccd"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 0, "retire_before": 0, "connection_id": "1209080706050403", "reset_token": "533f5fe066cbe3253725242c99da48fb"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 1, "retire_before": 0, "connection_id": "1309080706050403", "reset_token": "1c9faa2f5f2ede860711c7f0ec4f2427"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 2, "retire_before": 0, "connection_id": "1409080706050403", "reset_token": "fd57b3efacc2092a79b1d549e8055944"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 3, "retire_before": 0, "connection_id": "1509080706050403", "reset_token": "2db738702c9f7ba880651b6ec68767f7"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 4, "retire_before": 0, "connection_id": "1609080706050403", "reset_token": "6c96c83a443a2ee8abd39b089f7cd1d5"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 5, "retire_before": 0, "connection_id": "1709080706050403", "reset_token": "e11af5201068e1dff681dc657876277c"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 6, "retire_before": 0, "connection_id": "1809080706050403", "reset_token": "8be199e184482198f475392d041b766b"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 7, "retire_before": 0, "connection_id": "1909080706050403", "reset_token": "03c576e20bbfb2d6f8228b7a37ccd690"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 0, "retire_before": 0, "connection_id": "1a09080706050403", "reset_token": "41ae1c02e9675556c232f408d0ca2f73"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 1, "retire_before": 0, "connection_id": "1b09080706050403", "reset_token": "be7b11e8c96e62caf7c1df3112884e0d"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 2, "retire_before": 0, "connection_id": "1c09080706050403", "reset_token": "064698b5ca043814ff6726b2029d87d8"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 3, "retire_before": 0, "connection_id": "1d09080706050403", "reset_token": "51dd5816eccc2fde2db148bf3e40f1d4"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 4, "retire_before": 0, "connection_id": "1e09080706050403", "reset_token": "546b995ca2d716bcc1c84275ccb3f01a"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 5, "retire_before": 0, "connection_id": "1f09080706050403", "reset_token": "95b47f48271fd59551552cbd1785e602"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 6, "retire_before": 0, "connection_id": "2009080706050403", "reset_token": "376590f7a3b634ff6c5be9a56da0a3cf"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 7, "retire_before": 0, "connection_id": "2109080706050403", "reset_token": "add250eb379dd5a6c1a136d092e480a4"}]}], [0, 0, "recovery", "metrics_updated", {"cwnd": 15360, "bytes_in_flight": 2318}], [0, 0, "transport", "datagram_sent", {"byte_length": 1066}], [0, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 1, "dcid": "0908070605040302"}, "frames": [{"frame_type": "ping"}, {"frame_type": "padding"}]}], [0, 0, "recovery", "metrics_updated", {"bytes_in_flight": 3758}], [0, 0, "transport", "datagram_sent", {"byte_length": 1440}], [21040, 0, "transport", "datagram_received", {"byte_length": 50}], [21040, 0, "transport", "packet_received", {"packet_type": "handshake", "header": {"packet_size": 50, "packet_number": 0, "payload_length": 5, "scid": "0908070605040302", "dcid": "0a09080706050403"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[0, 0]]}]}], [22533, 0, "transport", "datagram_received", {"byte_length": 851}], [22533, 0, "transport", "packet_received", {"packet_type": "handshake", "header": {"packet_size": 89, "packet_number": 1, "payload_length": 44, "scid": "0908070605040302", "dcid": "0a09080706050403"}, "frames": [{"frame_type": "ack", "ack_delay": 0, "acked_ranges": [[0, 1]]}, {"frame_type": "crypto", "offset": 0, "length": 36}]}], [22533, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 762, "packet_number": 0, "dcid": "0a09080706050403", "key_phase": 0}, "frames": [{"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 1, "retire_before": 0, "connection_id": "0a08070605040302", "reset_token": "db53433036fcb08707e5adb0ffbb4591"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 2, "retire_before": 0, "connection_id": "0b08070605040302", "reset_token": "a5c11e9f6d34f051680d90054e1d8e61"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 3, "retire_before": 0, "connection_id": "0c08070605040302", "reset_token": "0b469fe1699ea0075ec423596b71b119"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 4, "retire_before": 0, "connection_id": "0d08070605040302", "reset_token": "bd7c9323f23cd90a8a44948e3e589c3d"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 5, "retire_before": 0, "connection_id": "0e08070605040302", "reset_token": "7127e92d271885fd9593f52f98c1a1ad"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 6, "retire_before": 0, "connection_id": "0f08070605040302", "reset_token": "ac294d76d656f0439236df22560a7304"}, {"frame_type": "path_new_connection_id", "path_id": 0, "sequence_number": 7, "retire_before": 0, "connection_id": "1008070605040302", "reset_token": "995ca0346f5695c0bef97bb0dcdbd925"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 0, "retire_before": 0, "connection_id": "1108070605040302", "reset_token": "8800e531fc2f097805a4cdc89435631e"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 1, "retire_before": 0, "connection_id": "1208070605040302", "reset_token": "26bee8a0435bccb709656fc7ae217ff4"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 2, "retire_before": 0, "connection_id": "1308070605040302", "reset_token": "b13d2d1e9f3fa273c2c2c9707a21f040"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 3, "retire_before": 0, "connection_id": "1408070605040302", "reset_token": "e861563a086fa5bcb0e2f0154b2d9fc3"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 4, "retire_before": 0, "connection_id": "1508070605040302", "reset_token": "305134c88b4202f2b756a1ae0f3732a9"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 5, "retire_before": 0, "connection_id": "1608070605040302", "reset_token": "de74b29bde576c36798f053eec65b11d"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 6, "retire_before": 0, "connection_id": "1708070605040302", "reset_token": "373579eb397dc9c2db8f45ed78ed92a3"}, {"frame_type": "path_new_connection_id", "path_id": 1, "sequence_number": 7, "retire_before": 0, "connection_id": "1808070605040302", "reset_token": "9c9c89bb2c182bc979d97026f547bd34"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 0, "retire_before": 0, "connection_id": "1908070605040302", "reset_token": "20a3107a347ae231f50eb3a72953be69"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 1, "retire_before": 0, "connection_id": "1a08070605040302", "reset_token": "47232fe9e08f261b9a1327632a88c9a9"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 2, "retire_before": 0, "connection_id": "1b08070605040302", "reset_token": "980b215a7ea9addd9063e1f3e1e2ed3d"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 3, "retire_before": 0, "connection_id": "1c08070605040302", "reset_token": "bf8473df3ca6efbcf98c46a0bb444ced"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 4, "retire_before": 0, "connection_id": "1d08070605040302", "reset_token": "5507c7303031a9cdfb545b909c83498d"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 5, "retire_before": 0, "connection_id": "1e08070605040302", "reset_token": "fde72b3c8c4b927b19cc0c6a8a368446"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 6, "retire_before": 0, "connection_id": "1f08070605040302", "reset_token": "1075c995b30034e2d149b2a2389cfa4d"}, {"frame_type": "path_new_connection_id", "path_id": 2, "sequence_number": 7, "retire_before": 0, "connection_id": "2008070605040302", "reset_token": "fe5046f23a2e1ab6d465708cf1828f9a"}]}], [22533, 0, "recovery", "metrics_updated", {"pacing_rate": 7231768, "bytes_in_flight": 2202, "smoothed_rtt": 21226, "min_rtt": 21040, "latest_rtt": 22533, "app_limited": 1}], [22533, 0, "transport", "spin_bit_updated", {"state": true}], [22533, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 39, "packet_number": 2, "dcid": "0908070605040302"}, "frames": [{"frame_type": "ack_frequency", "sequence_number": 0, "packet_tolerance": 2, "max_ack_delay": 5260, "reordering_threshold": 0}, {"frame_type": "handshake_done"}, {"frame_type": "padding"}]}], [22533, 0, "recovery", "metrics_updated", {"bytes_in_flight": 2257, "smoothed_rtt": 21226}], [22533, 0, "transport", "datagram_sent", {"byte_length": 55}], [23533, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 39, "packet_number": 3, "dcid": "0908070605040302"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 2941}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [23533, 0, "recovery", "metrics_updated", {"bytes_in_flight": 2312, "smoothed_rtt": 21226}], [23533, 0, "transport", "datagram_sent", {"byte_length": 55}], [23684, 0, "transport", "datagram_received", {"byte_length": 1440}], [23684, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 1440, "packet_number": 1, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "ping"}, {"frame_type": "padding"}]}], [23727, 0, "transport", "datagram_received", {"byte_length": 55}], [23727, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 2, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 2981}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [23727, 0, "recovery", "metrics_updated", {"pacing_rate": 7128825, "bytes_in_flight": 1550, "smoothed_rtt": 21538, "latest_rtt": 23727}], [23727, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 39, "packet_number": 4, "dcid": "0908070605040302"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 2965}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[0, 2]]}, {"frame_type": "padding"}]}], [23727, 0, "recovery", "metrics_updated", {"bytes_in_flight": 1605, "smoothed_rtt": 21538}], [23727, 0, "transport", "datagram_sent", {"byte_length": 55}], [24047, 0, "transport", "datagram_received", {"byte_length": 55}], [24047, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 3, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 3125}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[0, 1]]}, {"frame_type": "padding"}]}], [24047, 0, "recovery", "metrics_updated", {"pacing_rate": 7028676, "bytes_in_flight": 165, "smoothed_rtt": 21851, "latest_rtt": 24047}], [42821, 0, "transport", "datagram_received", {"byte_length": 307}], [42821, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 307, "packet_number": 4, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 5447}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[0, 2]]}, {"frame_type": "ack_frequency", "sequence_number": 0, "packet_tolerance": 2, "max_ack_delay": 5500, "reordering_threshold": 0}, {"frame_type": "stream", "id": 4, "offset": 0, "length": 257, "fin": true, "begins_with": "0001020304050607"}]}], [42821, 0, "recovery", "metrics_updated", {"pacing_rate": 7089230, "bytes_in_flight": 110, "smoothed_rtt": 21655, "min_rtt": 20288, "latest_rtt": 20288}], [42821, 0, "transport", "spin_bit_updated", {"state": false}], [42821, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 5, "dcid": "0908070605040302"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 5352}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[0, 4]]}, {"frame_type": "stream", "id": 4, "offset": 0, "length": 1399, "fin": false, "has_length": false, "begins_with": "0001020304050607"}]}], [42821, 0, "recovery", "metrics_updated", {"bytes_in_flight": 1550, "smoothed_rtt": 21655}], [42821, 0, "transport", "datagram_sent", {"byte_length": 1440}], [42821, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 6, "dcid": "0908070605040302"}, "frames": [{"frame_type": "stream", "id": 4, "offset": 1399, "length": 1410, "fin": false, "has_length": false, "begins_with": "7778797a7b7c7d7e"}]}], [42821, 0, "recovery", "metrics_updated", {"bytes_in_flight": 2990, "smoothed_rtt": 21655}], [42821, 0, "transport", "datagram_sent", {"byte_length": 1440}], [43363, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 7, "dcid": "0908070605040302"}, "frames": [{"frame_type": "stream", "id": 4, "offset": 2809, "length": 1410, "fin": false, "has_length": false, "begins_with": "f9fafbfcfdfeff00"}]}], [43363, 0, "recovery", "metrics_updated", {"bytes_in_flight": 4430, "smoothed_rtt": 21655}], [43363, 0, "transport", "datagram_sent", {"byte_length": 1440}], [43577, 1, "transport", "datagram_received", {"byte_length": 1252, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [43577, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 1252, "packet_number": 0, "dcid": "1209080706050403"}, "frames": [{"frame_type": "path_challenge", "path_challenge": "00009410f57a3937"}, {"frame_type": "padding"}]}], [43577, 1, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1236, "packet_number": 0, "dcid": "1108070605040302"}, "frames": [{"frame_type": "path_challenge", "path_challenge": "0000b9720dcc25ae"}, {"frame_type": "path_response", "path_response": "00009410f57a3937"}, {"frame_type": "padding"}]}], [43577, 1, "recovery", "metrics_updated", {"pacing_rate": 10016000000, "bytes_in_flight": 1252, "min_rtt": 0, "latest_rtt": 0, "app_limited": 0}], [43577, 1, "transport", "datagram_sent", {"byte_length": 1252}], [44987, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 8, "dcid": "0908070605040302"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 5623}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[0, 4]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "stream", "id": 4, "offset": 4219, "length": 1388, "fin": false, "has_length": false, "begins_with": "7b7c7d7e7f808182"}]}], [44987, 0, "recovery", "metrics_updated", {"pacing_rate": 7089230, "bytes_in_flight": 5870, "smoothed_rtt": 21655, "min_rtt": 20288, "latest_rtt": 20288, "app_limited": 1}], [44987, 0, "transport", "datagram_sent", {"byte_length": 1440, "addr_to": {"ip_v4": "********", "port_v4": 1234}}], [46611, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 9, "dcid": "0908070605040302"}, "frames": [{"frame_type": "stream", "id": 4, "offset": 5607, "length": 1410, "fin": false, "has_length": false, "begins_with": "e7e8e9eaebecedee"}]}], [46611, 0, "recovery", "metrics_updated", {"bytes_in_flight": 7310, "smoothed_rtt": 21655}], [46611, 0, "transport", "datagram_sent", {"byte_length": 1440}], [48235, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 10, "dcid": "0908070605040302"}, "frames": [{"frame_type": "stream", "id": 4, "offset": 7017, "length": 1410, "fin": false, "has_length": false, "begins_with": "696a6b6c6d6e6f70"}]}], [48235, 0, "recovery", "metrics_updated", {"bytes_in_flight": 8750, "smoothed_rtt": 21655}], [48235, 0, "transport", "datagram_sent", {"byte_length": 1440}], [49859, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 11, "dcid": "0908070605040302"}, "frames": [{"frame_type": "stream", "id": 4, "offset": 8427, "length": 1410, "fin": false, "has_length": false, "begins_with": "ebecedeeeff0f1f2"}]}], [49859, 0, "recovery", "metrics_updated", {"bytes_in_flight": 10190, "smoothed_rtt": 21655}], [49859, 0, "transport", "datagram_sent", {"byte_length": 1440}], [51484, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 179, "packet_number": 12, "dcid": "0908070605040302"}, "frames": [{"frame_type": "stream", "id": 4, "offset": 9837, "length": 163, "fin": true, "begins_with": "6d6e6f7071727374"}]}], [51484, 0, "recovery", "metrics_updated", {"bytes_in_flight": 10385, "smoothed_rtt": 21655}], [51484, 0, "transport", "datagram_sent", {"byte_length": 195}], [64621, 0, "transport", "datagram_received", {"byte_length": 55}], [64621, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 5, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 8197}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 5]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [64621, 0, "recovery", "metrics_updated", {"pacing_rate": 7084870, "bytes_in_flight": 8835, "smoothed_rtt": 21673, "latest_rtt": 21800}], [64621, 0, "recovery", "metrics_updated", {"smoothed_rtt": 21673}], [65579, 1, "transport", "datagram_received", {"byte_length": 1252, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [65579, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 1252, "packet_number": 1, "dcid": "1209080706050403"}, "frames": [{"frame_type": "path_response", "path_response": "0000b9720dcc25ae"}, {"frame_type": "padding"}]}], [65579, 1, "recovery", "metrics_updated", {"pacing_rate": 10016000000, "bytes_in_flight": 0, "min_rtt": 0, "latest_rtt": 22002, "app_limited": 0}], [65579, 1, "transport", "spin_bit_updated", {"state": true}], [65579, 1, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 1, "dcid": "1108070605040302"}, "frames": [{"frame_type": "ping"}, {"frame_type": "padding"}]}], [65579, 1, "recovery", "metrics_updated", {"bytes_in_flight": 1440}], [65579, 1, "transport", "datagram_sent", {"byte_length": 1440}], [66317, 0, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [66317, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 6, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 8409}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 7]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [66317, 0, "recovery", "metrics_updated", {"cwnd": 18331, "pacing_rate": 8390386, "bytes_in_flight": 5955, "smoothed_rtt": 21833, "min_rtt": 20288, "latest_rtt": 22954, "app_limited": 1}], [66317, 1, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [66317, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 2, "dcid": "1209080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 8409}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 7]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [68619, 0, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [68619, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 7, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 8697}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 9]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [68619, 0, "recovery", "metrics_updated", {"cwnd": 18349, "bytes_in_flight": 3075, "smoothed_rtt": 21854, "latest_rtt": 22008}], [68619, 1, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [68619, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 3, "dcid": "1209080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 8697}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 9]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [71053, 0, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [71053, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 8, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 9001}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 11]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [71053, 0, "recovery", "metrics_updated", {"pacing_rate": 8427212, "bytes_in_flight": 195, "smoothed_rtt": 21771, "latest_rtt": 21194}], [71053, 1, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [71053, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 4, "dcid": "1209080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 9001}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 11]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [71868, 0, "transport", "datagram_received", {"byte_length": 287, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [71868, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 287, "packet_number": 9, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 0, "length": 257, "fin": true, "begins_with": "0001020304050607"}]}], [71868, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 13, "dcid": "0908070605040302"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 8983}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[4, 9]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 4]]}, {"frame_type": "stream", "id": 8, "offset": 0, "length": 1390, "fin": false, "has_length": false, "begins_with": "0001020304050607"}]}], [71868, 0, "recovery", "metrics_updated", {"bytes_in_flight": 1635, "smoothed_rtt": 21771}], [71868, 0, "transport", "datagram_sent", {"byte_length": 1440}], [71868, 1, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1236, "packet_number": 2, "dcid": "1108070605040302"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 8983}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[4, 9]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 4]]}, {"frame_type": "stream", "id": 8, "offset": 1390, "length": 1200, "fin": false, "has_length": false, "begins_with": "6e6f707172737475"}]}], [71868, 1, "recovery", "metrics_updated", {"cwnd": 15360, "pacing_rate": 10016000000, "bytes_in_flight": 2692, "min_rtt": 0, "latest_rtt": 22002, "app_limited": 0}], [71868, 1, "transport", "datagram_sent", {"byte_length": 1252, "addr_to": {"ip_v4": "********", "port_v4": 1251}}], [71868, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 14, "dcid": "0908070605040302"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 2590, "length": 1410, "fin": false, "has_length": false, "begins_with": "1e1f202122232425"}]}], [71868, 0, "recovery", "metrics_updated", {"cwnd": 18349, "pacing_rate": 8427212, "bytes_in_flight": 3075, "smoothed_rtt": 21771, "min_rtt": 20288, "latest_rtt": 21194, "app_limited": 1}], [71868, 0, "transport", "datagram_sent", {"byte_length": 1440, "addr_to": {"ip_v4": "********", "port_v4": 1234}}], [71868, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1424, "packet_number": 15, "dcid": "0908070605040302"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 4000, "length": 1410, "fin": false, "has_length": false, "begins_with": "a0a1a2a3a4a5a6a7"}]}], [71868, 0, "recovery", "metrics_updated", {"bytes_in_flight": 4515, "smoothed_rtt": 21771}], [71868, 0, "transport", "datagram_sent", {"byte_length": 1440}], [71868, 1, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1236, "packet_number": 3, "dcid": "1108070605040302"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 5410, "length": 1222, "fin": false, "has_length": false, "begins_with": "2223242526272829"}]}], [71868, 1, "recovery", "metrics_updated", {"cwnd": 15360, "pacing_rate": 10016000000, "bytes_in_flight": 3944, "min_rtt": 0, "latest_rtt": 22002, "app_limited": 0}], [71868, 1, "transport", "datagram_sent", {"byte_length": 1252, "addr_to": {"ip_v4": "********", "port_v4": 1251}}], [71868, 1, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1236, "packet_number": 4, "dcid": "1108070605040302"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 6632, "length": 1222, "fin": false, "has_length": false, "begins_with": "e8e9<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}], [71868, 1, "recovery", "metrics_updated", {"bytes_in_flight": 5196}], [71868, 1, "transport", "datagram_sent", {"byte_length": 1252}], [71868, 1, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 1236, "packet_number": 5, "dcid": "1108070605040302"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 7854, "length": 1222, "fin": false, "has_length": false, "begins_with": "aeafb0b1b2b3b4b5"}]}], [71868, 1, "recovery", "metrics_updated", {"bytes_in_flight": 6448}], [71868, 1, "transport", "datagram_sent", {"byte_length": 1252}], [71868, 1, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 940, "packet_number": 6, "dcid": "1108070605040302"}, "frames": [{"frame_type": "stream", "id": 8, "offset": 9076, "length": 924, "fin": true, "begins_with": "7475767778797a7b"}]}], [71868, 1, "recovery", "metrics_updated", {"bytes_in_flight": 7404}], [71868, 1, "transport", "datagram_sent", {"byte_length": 956}], [72790, 1, "transport", "datagram_received", {"byte_length": 1440}], [72790, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 1440, "packet_number": 5, "dcid": "1209080706050403"}, "frames": [{"frame_type": "ping"}, {"frame_type": "padding"}]}], [76942, 0, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [76942, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 10, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 9737}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 12]]}, {"frame_type": "padding"}]}], [76942, 0, "recovery", "metrics_updated", {"cwnd": 18665, "pacing_rate": 8390386, "bytes_in_flight": 4320, "smoothed_rtt": 22231, "min_rtt": 20288, "latest_rtt": 25458, "app_limited": 1}], [76942, 1, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [76942, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 6, "dcid": "1209080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 9737}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 12]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 0]]}, {"frame_type": "padding"}]}], [76942, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 39, "packet_number": 16, "dcid": "0908070605040302"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 9617}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[4, 10]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 6]]}, {"frame_type": "padding"}]}], [76942, 0, "recovery", "metrics_updated", {"bytes_in_flight": 4375, "smoothed_rtt": 22231}], [76942, 0, "transport", "datagram_sent", {"byte_length": 55, "addr_to": {"ip_v4": "********", "port_v4": 1234}}], [76942, 1, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 39, "packet_number": 7, "dcid": "1108070605040302"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 9617}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[4, 10]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 6]]}, {"frame_type": "padding"}]}], [76942, 1, "recovery", "metrics_updated", {"cwnd": 15360, "pacing_rate": 10016000000, "bytes_in_flight": 7459, "min_rtt": 0, "latest_rtt": 22002, "app_limited": 0}], [76942, 1, "transport", "datagram_sent", {"byte_length": 55, "addr_to": {"ip_v4": "********", "port_v4": 1251}}], [92033, 0, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [92033, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 11, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 11623}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 12]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 1]]}, {"frame_type": "padding"}]}], [92033, 1, "recovery", "metrics_updated", {"pacing_rate": 6628308, "bytes_in_flight": 6019, "smoothed_rtt": 23166, "min_rtt": 21044, "latest_rtt": 26454, "app_limited": 1}], [92033, 1, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [92033, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 7, "dcid": "1209080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 11623}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[1, 12]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[0, 1]]}, {"frame_type": "padding"}]}], [93913, 0, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [93913, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 12, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 11858}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[12, 13]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 3]]}, {"frame_type": "padding"}]}], [93913, 0, "recovery", "metrics_updated", {"cwnd": 18665, "pacing_rate": 8402625, "bytes_in_flight": 2935, "smoothed_rtt": 22207, "min_rtt": 20288, "latest_rtt": 22045}], [93913, 0, "recovery", "metrics_updated", {"smoothed_rtt": 22207}], [93913, 1, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [93913, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 8, "dcid": "1209080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 11858}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[12, 13]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 3]]}, {"frame_type": "padding"}]}], [95364, 0, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [95364, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 13, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 12040}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[12, 15]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 4]]}, {"frame_type": "padding"}]}], [95364, 0, "recovery", "metrics_updated", {"cwnd": 21876, "pacing_rate": 9779286, "bytes_in_flight": 55, "smoothed_rtt": 22368, "latest_rtt": 23496}], [95364, 0, "recovery", "metrics_updated", {"smoothed_rtt": 22368}], [95364, 1, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [95364, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 9, "dcid": "1209080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 12040}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[12, 15]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 4]]}, {"frame_type": "padding"}]}], [96679, 0, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [96679, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 14, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 12204}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[12, 15]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 6]]}, {"frame_type": "padding"}]}], [96679, 1, "recovery", "metrics_updated", {"cwnd": 19321, "pacing_rate": 8287769, "smoothed_rtt": 23299, "min_rtt": 21044, "latest_rtt": 24811}], [96679, 1, "transport", "datagram_received", {"byte_length": 55, "addr_from": {"ip_v4": "********", "port_v4": 1251}}], [96679, 1, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 55, "packet_number": 10, "dcid": "1209080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 12204}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[12, 15]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 6]]}, {"frame_type": "padding"}]}], [106719, 0, "transport", "datagram_received", {"byte_length": 51, "addr_from": {"ip_v4": "********", "port_v4": 1234}}], [106719, 0, "transport", "packet_received", {"packet_type": "1RTT", "header": {"packet_size": 51, "packet_number": 15, "dcid": "0a09080706050403"}, "frames": [{"frame_type": "time_stamp", "time_stamp": 13460}, {"frame_type": "path_ack", "path_id": 0, "ack_delay": 0, "acked_ranges": [[12, 16]]}, {"frame_type": "path_ack", "path_id": 1, "ack_delay": 0, "acked_ranges": [[1, 7]]}, {"frame_type": "connection_close", "error_space": "application", "error_code": 0}]}], [106719, 0, "recovery", "metrics_updated", {"cwnd": 22782, "pacing_rate": 9779286, "bytes_in_flight": 0, "smoothed_rtt": 23294, "min_rtt": 20288, "latest_rtt": 29777}], [106719, 0, "recovery", "metrics_updated", {"smoothed_rtt": 23294}], [106719, 0, "transport", "spin_bit_updated", {"state": false}], [106719, 0, "transport", "packet_sent", {"packet_type": "1RTT", "header": {"packet_size": 15, "packet_number": 17, "dcid": "0908070605040302"}, "frames": [{"frame_type": "connection_close", "error_space": "transport", "error_code": 0}]}], [106719, 0, "transport", "datagram_sent", {"byte_length": 31}], [199668, 0, "transport", "datagram_received", {"byte_length": 256}], [199668, 0, "transport", "packet_dropped", {"packet_type": "1RTT", "packet_size": 256, "trigger": "payload_decrypt_error"}]]}]}