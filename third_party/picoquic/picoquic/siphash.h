/*
   SipHash reference C implementation

   Copyright (c) 2012-2021 <PERSON><PERSON><PERSON>
   <<EMAIL>>
   Copyright (c) 2012-2014 <PERSON> <<EMAIL>>

   To the extent possible under law, the author(s) have dedicated all copyright
   and related and neighboring rights to this software to the public domain
   worldwide. This software is distributed without any warranty.

   You should have received a copy of the CC0 Public Domain Dedication along
   with
   this software. If not, see
   <http://creativecommons.org/publicdomain/zero/1.0/>.
 */

#ifndef SIPHASH_H
#define SIPHASH_H
#include <stddef.h>
#include <stdint.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

int siphash(const void *in, const size_t inlen, const void *k, uint8_t *out,
            const size_t outlen);

#ifdef __cplusplus
}
#endif
#endif /* SIPHASH_H */