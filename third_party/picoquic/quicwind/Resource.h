//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by quicwind.rc
//
#define IDC_MYICON                      2
#define IDD_QUICWIND_DIALOG             102
#define IDS_APP_TITLE                   103
#define IDD_ABOUTBOX                    103
#define IDM_ABOUT                       104
#define IDM_EXIT                        105
#define IDI_QUICWIND                    107
#define IDI_SMALL                       108
#define IDC_QUICWIND                    109
#define IDR_MAINFRAME                   128
#define IDD_CONNECT                     129
#define IDD_LOAD_DOC                    130
#define IDD_CLOSE_CNX                   131
#define IDC_SERVER_NAME                 1000
#define IDC_SERVER_BOX_TITLE            1001
#define IDC_PORT_BOX_TITLE              1002
#define IDC_PORT_NUMBER                 1003
#define IDC_CNX_LIST_LABEL              1004
#define IDC_DOC1_BOX_TITLE              1004
#define IDC_CNX_LIST1                   1005
#define IDC_DOC1                        1005
#define IDC_SNI_BOX_TITLE               1006
#define IDC_CNX_LIST_LABEL2             1006
#define IDC_SNI                         1007
#define IDC_CNX_LIST2                   1007
#define IDC_ALPN_BOX_TITLE              1008
#define IDC_DOC2                        1008
#define IDC_ALPN                        1009
#define ID_CONNECT_CONNECT              32771
#define ID_FILE_CONNECT                 32772
#define ID_FILE_LOADFILE                32773
#define ID_FILE_CLOSE                   32774
#define IDC_STATIC                      -1

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NO_MFC                     1
#define _APS_NEXT_RESOURCE_VALUE        133
#define _APS_NEXT_COMMAND_VALUE         32775
#define _APS_NEXT_CONTROL_VALUE         1009
#define _APS_NEXT_SYMED_VALUE           110
#endif
#endif
