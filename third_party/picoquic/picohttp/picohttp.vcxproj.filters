﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="democlient.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="demoserver.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="h3zero.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="quicperf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="webtransport.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="h3zero_server.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="h3zero_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="h3zero_client.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="wt_baton.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="h3zero_uri.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="democlient.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="demoserver.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="h3zero.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="wincompat.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="pico_webtransport.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="quicperf.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="h3zero_common.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="wt_baton.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="h3zero_uri.h">
      <Filter>Source Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{7408ec18-014f-45ef-90c2-ad17ce479d84}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>