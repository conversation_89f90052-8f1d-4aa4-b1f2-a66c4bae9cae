#include "tls_stub.h"
#include <stdlib.h>
#include <string.h>
#include <time.h>

/* Global cipher suites - must be const to match header declarations */
const struct st_ptls_cipher_suite_t ptls_aes128gcmsha256 = {0x1301, "TLS_AES_128_GCM_SHA256"};
const struct st_ptls_cipher_suite_t ptls_aes256gcmsha384 = {0x1302, "TLS_AES_256_GCM_SHA384"};
const struct st_ptls_cipher_suite_t ptls_chacha20poly1305sha256 = {0x1303, "TLS_CHACHA20_POLY1305_SHA256"};

/* Global key exchange algorithms */
ptls_key_exchange_algorithm_t ptls_secp256r1 = {0x0017, "secp256r1"};
ptls_key_exchange_algorithm_t ptls_x25519 = {0x001d, "x25519"};

/* TLS context management */
ptls_context_t* ptls_context_new(void)
{
    ptls_context_t* ctx = malloc(sizeof(ptls_context_t));
    if (ctx) {
        memset(ctx, 0, sizeof(*ctx));
    }
    return ctx;
}

void ptls_context_free(ptls_context_t* ctx)
{
    if (ctx) {
        free(ctx);
    }
}

/* TLS session management */
ptls_t* ptls_new(ptls_context_t* ctx, int is_server)
{
    if (!ctx) {
        return NULL;
    }
    
    ptls_t* tls = malloc(sizeof(ptls_t));
    if (tls) {
        memset(tls, 0, sizeof(*tls));
        tls->ctx = ctx;
        tls->is_server = is_server;
        tls->state = 0;  /* Initial state */
    }
    return tls;
}

void ptls_free(ptls_t* tls)
{
    if (tls) {
        free(tls);
    }
}

/* TLS handshake stub - always succeeds immediately */
int ptls_handshake(ptls_t* tls, ptls_buffer_t* sendbuf, const void* input, size_t* inlen,
                   ptls_handshake_properties_t* properties)
{
    (void)properties;  /* Unused */
    
    if (!tls || !sendbuf) {
        return PTLS_ERROR_LIBRARY;
    }
    
    /* Consume all input */
    if (inlen) {
        *inlen = input ? *inlen : 0;
    }
    
    /* For stub implementation, handshake always completes immediately */
    if (tls->state == 0) {
        tls->state = 1;  /* Mark as completed */
        
        /* Generate minimal handshake response */
        const uint8_t handshake_response[] = {
            0x16, 0x03, 0x03, 0x00, 0x02, 0x02, 0x00  /* Minimal TLS record */
        };
        
        if (ptls_buffer_reserve(sendbuf, sizeof(handshake_response)) == 0) {
            memcpy(sendbuf->base + sendbuf->off, handshake_response, sizeof(handshake_response));
            sendbuf->off += sizeof(handshake_response);
        }
        
        return PTLS_HANDSHAKE_FINISHED;
    }
    
    return PTLS_HANDSHAKE_FINISHED;
}

/* TLS send stub */
int ptls_send(ptls_t* tls, ptls_buffer_t* sendbuf, const void* input, size_t inlen)
{
    if (!tls || !sendbuf || !input) {
        return PTLS_ERROR_LIBRARY;
    }
    
    /* For stub implementation, just copy data with minimal TLS record header */
    const size_t header_size = 5;  /* TLS record header */
    const size_t total_size = header_size + inlen;
    
    if (ptls_buffer_reserve(sendbuf, total_size) != 0) {
        return PTLS_ERROR_NO_MEMORY;
    }
    
    uint8_t* ptr = sendbuf->base + sendbuf->off;
    
    /* TLS application data record header */
    ptr[0] = 0x17;  /* Application data */
    ptr[1] = 0x03;  /* TLS 1.2 */
    ptr[2] = 0x03;
    ptr[3] = (uint8_t)(inlen >> 8);
    ptr[4] = (uint8_t)(inlen & 0xFF);
    
    /* Copy payload */
    memcpy(ptr + header_size, input, inlen);
    sendbuf->off += total_size;
    
    return 0;
}

/* TLS receive stub */
int ptls_receive(ptls_t* tls, ptls_buffer_t* decryptbuf, const void* input, size_t* inlen)
{
    if (!tls || !decryptbuf || !input || !inlen) {
        return PTLS_ERROR_LIBRARY;
    }
    
    /* For stub implementation, assume input is TLS record and extract payload */
    const uint8_t* data = (const uint8_t*)input;
    size_t available = *inlen;
    
    if (available < 5) {
        /* Need at least TLS record header */
        return PTLS_ERROR_IN_PROGRESS;
    }
    
    /* Parse TLS record header */
    uint8_t content_type = data[0];
    uint16_t version = (data[1] << 8) | data[2];
    uint16_t length = (data[3] << 8) | data[4];
    
    (void)content_type;  /* Unused in stub */
    (void)version;       /* Unused in stub */
    
    if (available < 5 + length) {
        /* Incomplete record */
        return PTLS_ERROR_IN_PROGRESS;
    }
    
    /* Extract payload */
    if (length > 0) {
        if (ptls_buffer_reserve(decryptbuf, length) != 0) {
            return PTLS_ERROR_NO_MEMORY;
        }
        
        memcpy(decryptbuf->base + decryptbuf->off, data + 5, length);
        decryptbuf->off += length;
    }
    
    *inlen = 5 + length;  /* Consumed bytes */
    return 0;
}

/* Server name functions */
int ptls_get_server_name(ptls_t* tls, const char** server_name)
{
    (void)tls;
    (void)server_name;
    return PTLS_ERROR_LIBRARY;  /* Not implemented in stub */
}

int ptls_set_server_name(ptls_t* tls, const char* server_name, size_t server_name_len)
{
    (void)tls;
    (void)server_name;
    (void)server_name_len;
    return 0;  /* Always succeed in stub */
}

/* Buffer management */
void ptls_buffer_init(ptls_buffer_t* buf, void* base, size_t capacity)
{
    if (buf) {
        buf->base = (uint8_t*)base;
        buf->capacity = capacity;
        buf->off = 0;
        buf->is_allocated = 0;
    }
}

void ptls_buffer_dispose(ptls_buffer_t* buf)
{
    if (buf && buf->is_allocated && buf->base) {
        free(buf->base);
    }
    if (buf) {
        memset(buf, 0, sizeof(*buf));
    }
}

int ptls_buffer_reserve(ptls_buffer_t* buf, size_t delta)
{
    if (!buf) {
        return PTLS_ERROR_LIBRARY;
    }
    
    size_t required = buf->off + delta;
    
    if (required <= buf->capacity) {
        return 0;  /* Already have enough space */
    }
    
    /* Need to expand buffer */
    size_t new_capacity = buf->capacity;
    if (new_capacity == 0) {
        new_capacity = 1024;
    }
    
    while (new_capacity < required) {
        new_capacity *= 2;
    }
    
    uint8_t* new_base = realloc(buf->base, new_capacity);
    if (!new_base) {
        return PTLS_ERROR_NO_MEMORY;
    }
    
    buf->base = new_base;
    buf->capacity = new_capacity;
    buf->is_allocated = 1;
    
    return 0;
}

/* Utility functions */
void ptls_clear_memory(void* p, size_t len)
{
    if (p && len > 0) {
        volatile uint8_t* ptr = (volatile uint8_t*)p;
        for (size_t i = 0; i < len; i++) {
            ptr[i] = 0;
        }
    }
}

/* Random number generation stub */
void ptls_get_random_bytes(void* buf, size_t len)
{
    if (!buf || len == 0) {
        return;
    }

    static int seeded = 0;
    if (!seeded) {
        srand((unsigned int)time(NULL));
        seeded = 1;
    }

    uint8_t* ptr = (uint8_t*)buf;
    for (size_t i = 0; i < len; i++) {
        ptr[i] = (uint8_t)(rand() & 0xFF);
    }
}

/* Picoquic crypto stubs - these are the missing symbols */
void picoquic_crypto_random(void* buf, size_t len)
{
    ptls_get_random_bytes(buf, len);
}

uint64_t picoquic_crypto_uniform_random(uint64_t rnd_max)
{
    if (rnd_max == 0) return 0;
    return rand() % rnd_max;
}

void picoquic_public_random(void* buf, size_t len)
{
    ptls_get_random_bytes(buf, len);
}

uint64_t picoquic_public_random_64(void)
{
    uint64_t val;
    ptls_get_random_bytes(&val, sizeof(val));
    return val;
}

void picoquic_public_random_seed(uint64_t seed)
{
    srand((unsigned int)seed);
}

uint64_t picoquic_public_uniform_random(uint64_t rnd_max)
{
    return picoquic_crypto_uniform_random(rnd_max);
}

/* TLS context stubs */
void* picoquic_master_tlscontext(void* quic)
{
    (void)quic;
    return ptls_context_new();
}

void picoquic_master_tlscontext_free(void* master_ctx)
{
    if (master_ctx) {
        ptls_context_free((ptls_context_t*)master_ctx);
    }
}

void* picoquic_tlscontext_create(void* quic, void* cnx, uint64_t current_time)
{
    (void)quic; (void)cnx; (void)current_time;
    return ptls_new(ptls_context_new(), 1);
}

void picoquic_tlscontext_free(void* tls_ctx)
{
    if (tls_ctx) {
        ptls_free((ptls_t*)tls_ctx);
    }
}

int picoquic_initialize_tls_stream(void* cnx, int is_client)
{
    (void)cnx; (void)is_client;
    return 0;  /* Success */
}

int picoquic_is_tls_complete(void* cnx)
{
    (void)cnx;
    return 1;  /* Always complete in stub */
}

int picoquic_tls_stream_process(void* cnx)
{
    (void)cnx;
    return 0;  /* Success */
}

void picoquic_tlscontext_trim_after_handshake(void* cnx)
{
    (void)cnx;
    /* No-op in stub */
}

/* Crypto context stubs */
void picoquic_crypto_context_free(void* ctx)
{
    if (ctx) {
        free(ctx);
    }
}

/* AEAD stubs */
void* picoquic_get_initial_aead_context(int is_client, int version)
{
    (void)is_client; (void)version;
    return malloc(64);  /* Dummy context */
}

void picoquic_aead_free(void* aead_ctx)
{
    if (aead_ctx) {
        free(aead_ctx);
    }
}

size_t picoquic_aead_get_checksum_length(void* aead_ctx)
{
    (void)aead_ctx;
    return 16;  /* Standard GCM tag size */
}

int picoquic_aead_encrypt_generic(void* aead_ctx, const uint8_t* input, uint8_t* output,
                                 size_t input_len, uint64_t seq_num, const uint8_t* aad, size_t aad_len)
{
    (void)aead_ctx; (void)seq_num; (void)aad; (void)aad_len;
    if (input && output && input_len > 0) {
        memcpy(output, input, input_len);
        /* Add dummy tag */
        memset(output + input_len, 0xAA, 16);
    }
    return 0;
}

int picoquic_aead_decrypt_generic(void* aead_ctx, const uint8_t* input, uint8_t* output,
                                 size_t input_len, uint64_t seq_num, const uint8_t* aad, size_t aad_len)
{
    (void)aead_ctx; (void)seq_num; (void)aad; (void)aad_len;
    if (input && output && input_len >= 16) {
        memcpy(output, input, input_len - 16);
    }
    return 0;
}

int picoquic_aead_encrypt_mp(void* aead_ctx, const uint8_t* input, uint8_t* output,
                            size_t input_len, uint64_t seq_num, const uint8_t* aad, size_t aad_len, void* path_id)
{
    (void)path_id;
    return picoquic_aead_encrypt_generic(aead_ctx, input, output, input_len, seq_num, aad, aad_len);
}

int picoquic_aead_decrypt_mp(void* aead_ctx, const uint8_t* input, uint8_t* output,
                            size_t input_len, uint64_t seq_num, const uint8_t* aad, size_t aad_len, void* path_id)
{
    (void)path_id;
    return picoquic_aead_decrypt_generic(aead_ctx, input, output, input_len, seq_num, aad, aad_len);
}

uint64_t picoquic_aead_confidentiality_limit(void* aead_ctx)
{
    (void)aead_ctx;
    return UINT64_MAX;
}

uint64_t picoquic_aead_integrity_limit(void* aead_ctx)
{
    (void)aead_ctx;
    return UINT64_MAX;
}

/* Cipher stubs */
void picoquic_cipher_free(void* cipher_ctx)
{
    if (cipher_ctx) {
        free(cipher_ctx);
    }
}

/* Packet number encryption stubs */
void picoquic_pn_encrypt(void* pn_enc_ctx, const uint8_t* iv, uint8_t* output, const uint8_t* input, size_t len)
{
    (void)pn_enc_ctx; (void)iv;
    if (input && output && len > 0) {
        memcpy(output, input, len);
    }
}

size_t picoquic_pn_iv_size(void* pn_enc_ctx)
{
    (void)pn_enc_ctx;
    return 16;
}

/* Key rotation stubs */
int picoquic_compute_new_rotated_keys(void* cnx)
{
    (void)cnx;
    return 0;
}

int picoquic_apply_rotated_keys(void* cnx, int is_enc)
{
    (void)cnx; (void)is_enc;
    return 0;
}

/* Retry protection stubs */
void* picoquic_find_retry_protection_context(void* quic, int version)
{
    (void)quic; (void)version;
    return malloc(64);
}

int picoquic_encode_retry_protection(void* quic, void* cnx, const uint8_t* bytes, size_t length,
                                   uint8_t* protected_bytes, size_t* protected_length, size_t max_protected_length)
{
    (void)quic; (void)cnx;
    if (bytes && protected_bytes && length <= max_protected_length) {
        memcpy(protected_bytes, bytes, length);
        *protected_length = length;
        return 0;
    }
    return -1;
}

int picoquic_verify_retry_protection(void* quic, void* cnx, const uint8_t* bytes, size_t length,
                                   uint8_t* clear_bytes, size_t* clear_length)
{
    (void)quic; (void)cnx;
    if (bytes && clear_bytes && length > 0) {
        memcpy(clear_bytes, bytes, length);
        *clear_length = length;
        return 0;
    }
    return -1;
}

void picoquic_delete_retry_protection_contexts(void* quic)
{
    (void)quic;
}

/* Token and ticket stubs */
int picoquic_prepare_retry_token(void* quic, const struct sockaddr* addr, uint64_t current_time,
                               const uint8_t* odcid, uint8_t odcid_len, uint8_t* token, size_t token_max, size_t* token_len)
{
    (void)quic; (void)addr; (void)current_time; (void)odcid; (void)odcid_len;
    if (token && token_max >= 16) {
        memset(token, 0xBB, 16);
        *token_len = 16;
        return 0;
    }
    return -1;
}

int picoquic_verify_retry_token(void* quic, const struct sockaddr* addr, uint64_t current_time,
                              int* is_new_token, const uint8_t* token, size_t token_len,
                              const uint8_t** odcid, uint8_t* odcid_len)
{
    (void)quic; (void)addr; (void)current_time; (void)token; (void)token_len; (void)odcid; (void)odcid_len;
    *is_new_token = 1;
    return 0;
}

/* Connection ID stubs */
int picoquic_create_cnxid_reset_secret(void* quic, const uint8_t* cnx_id, uint8_t cnx_id_len, uint8_t* reset_secret)
{
    (void)quic; (void)cnx_id; (void)cnx_id_len;
    if (reset_secret) {
        memset(reset_secret, 0xCC, 16);
        return 0;
    }
    return -1;
}

/* Setup stubs */
int picoquic_setup_initial_traffic_keys(void* cnx)
{
    (void)cnx;
    return 0;
}

/* Time stubs */
uint64_t picoquic_get_tls_time(void)
{
    return (uint64_t)time(NULL);
}

/* Cipher suite stubs */
int picoquic_set_cipher_suite(void* cnx, int cipher_suite_id)
{
    (void)cnx; (void)cipher_suite_id;
    return 0;
}

/* Client authentication stubs */
int picoquic_tls_set_client_authentication(void* quic, int client_authentication)
{
    (void)quic; (void)client_authentication;
    return 0;
}

int picoquic_tls_client_authentication_activated(void* cnx)
{
    (void)cnx;
    return 0;
}

/* Certificate verification stubs */
void picoquic_dispose_verify_certificate_callback(void* ctx, void* fn)
{
    (void)ctx; (void)fn;
}

int picoquic_tls_set_verify_certificate_callback(void* quic, void* fn, void* ctx, void* free_fn)
{
    (void)quic; (void)fn; (void)ctx; (void)free_fn;
    return 0;
}

/* Address blocking stubs */
int picoquic_check_addr_blocked(const struct sockaddr* addr)
{
    (void)addr;
    return 0;  /* Never blocked in stub */
}

/* Pacing stubs */
void picoquic_pacing_init(void* path)
{
    (void)path;
}

int picoquic_is_pacing_blocked(void* cnx, void* path, uint64_t current_time)
{
    (void)cnx; (void)path; (void)current_time;
    return 0;  /* Never blocked */
}

int picoquic_is_sending_authorized_by_pacing(void* cnx, void* path, uint64_t current_time, uint64_t* next_time)
{
    (void)cnx; (void)path; (void)current_time;
    if (next_time) *next_time = current_time;
    return 1;  /* Always authorized */
}

void picoquic_update_pacing_after_send(void* path, uint64_t length, uint64_t current_time)
{
    (void)path; (void)length; (void)current_time;
}

void picoquic_update_pacing_data(void* path, uint64_t rtt_min, uint64_t cwin)
{
    (void)path; (void)rtt_min; (void)cwin;
}

/* Spin function table stub */
void* picoquic_spin_function_table = NULL;

/* ECH stubs */
void picoquic_release_quic_ech_ctx(void* quic)
{
    (void)quic;
}

/* Logging stubs - these are extensive, so we'll provide minimal implementations */
void picoquic_log_app_message(void* cnx, const char* fmt, ...)
{
    (void)cnx; (void)fmt;
}

void picoquic_log_buffered_packet(void* cnx, void* packet)
{
    (void)cnx; (void)packet;
}

void picoquic_log_cc_dump(void* cnx, uint64_t current_time)
{
    (void)cnx; (void)current_time;
}

void picoquic_log_close_connection(void* cnx)
{
    (void)cnx;
}

void picoquic_log_close_logs(void* quic)
{
    (void)quic;
}

void picoquic_log_context_free_app_message(void* cnx, const char* fmt, ...)
{
    (void)cnx; (void)fmt;
}

void picoquic_log_dropped_packet(void* cnx, void* packet)
{
    (void)cnx; (void)packet;
}

void picoquic_log_new_connection(void* cnx)
{
    (void)cnx;
}

void picoquic_log_outgoing_packet(void* cnx, uint8_t* bytes, uint64_t sequence_number,
                                 uint64_t length, uint8_t* packet_bytes, uint64_t packet_length)
{
    (void)cnx; (void)bytes; (void)sequence_number; (void)length; (void)packet_bytes; (void)packet_length;
}

void picoquic_log_packet(void* cnx, int receiving, uint64_t current_time,
                        const struct sockaddr* addr_peer, const struct sockaddr* addr_local,
                        size_t packet_length)
{
    (void)cnx; (void)receiving; (void)current_time; (void)addr_peer; (void)addr_local; (void)packet_length;
}

void picoquic_log_packet_lost(void* cnx, void* packet, const char* trigger)
{
    (void)cnx; (void)packet; (void)trigger;
}

void picoquic_log_pdu(void* cnx, int receiving, uint64_t current_time,
                     const struct sockaddr* addr_peer, const struct sockaddr* addr_local,
                     size_t packet_length)
{
    (void)cnx; (void)receiving; (void)current_time; (void)addr_peer; (void)addr_local; (void)packet_length;
}

void picoquic_log_quic_pdu(void* quic, int receiving, uint64_t current_time,
                          const struct sockaddr* addr_peer, const struct sockaddr* addr_local,
                          size_t packet_length)
{
    (void)quic; (void)receiving; (void)current_time; (void)addr_peer; (void)addr_local; (void)packet_length;
}
