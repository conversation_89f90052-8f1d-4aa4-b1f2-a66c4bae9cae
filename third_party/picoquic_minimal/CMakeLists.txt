cmake_minimum_required(VERSION 3.13)

project(picoquic_minimal
        VERSION ********
        DESCRIPTION "Minimal picoquic library for embedded systems"
        LANGUAGES C)

find_package(Threads REQUIRED)

# Minimal picoquic core files - only essential components
set(PICOQUIC_MINIMAL_FILES
    ../picoquic/picoquic/bytestream.c
    ../picoquic/picoquic/cc_common.c
    ../picoquic/picoquic/config.c
    ../picoquic/picoquic/frames.c
    ../picoquic/picoquic/intformat.c
    ../picoquic/picoquic/logger.c
    ../picoquic/picoquic/loss_recovery.c
    ../picoquic/picoquic/newreno.c
    ../picoquic/picoquic/packet.c
    ../picoquic/picoquic/paths.c
    ../picoquic/picoquic/picohash.c
    ../picoquic/picoquic/picosocks.c
    ../picoquic/picoquic/picosplay.c
    ../picoquic/picoquic/quicctx.c
    ../picoquic/picoquic/sacks.c
    ../picoquic/picoquic/sender.c
    ../picoquic/picoquic/siphash.c
    ../picoquic/picoquic/sockloop.c
    ../picoquic/picoquic/ticket_store.c
    ../picoquic/picoquic/timing.c
    ../picoquic/picoquic/token_store.c
    ../picoquic/picoquic/transport.c
    ../picoquic/picoquic/util.c
    # Minimal TLS implementation - stub
    tls_stub.c
)

set(PICOQUIC_MINIMAL_HEADERS
    ../picoquic/picoquic/picoquic.h
    ../picoquic/picoquic/picosocks.h
    ../picoquic/picoquic/picoquic_utils.h
    ../picoquic/picoquic/picoquic_packet_loop.h
    ../picoquic/picoquic/picoquic_config.h
    ../picoquic/picoquic/picoquic_newreno.h
    ../picoquic/picoquic/siphash.h
    tls_stub.h
)

# Create minimal picoquic library
add_library(picoquic-minimal STATIC ${PICOQUIC_MINIMAL_FILES})

target_include_directories(picoquic-minimal
    PUBLIC
        ../picoquic/picoquic
        .
    PRIVATE
        .  # For picotls.h stub
)

target_compile_definitions(picoquic-minimal 
    PRIVATE
        PICOQUIC_MINIMAL_BUILD=1
        PTLS_WITHOUT_OPENSSL=1
        PTLS_WITHOUT_FUSION=1
        DISABLE_DEBUG_PRINTF=1
)

target_link_libraries(picoquic-minimal
    PUBLIC
        Threads::Threads
)

# Compiler settings for embedded systems
target_compile_options(picoquic-minimal
    PRIVATE
        -Os                    # Optimize for size
        -ffunction-sections    # Enable function sections
        -fdata-sections        # Enable data sections
        -Wall
        -Wextra
        -std=c11
)

# Set properties
set_target_properties(picoquic-minimal
    PROPERTIES
        C_STANDARD 11
        C_STANDARD_REQUIRED YES
        C_EXTENSIONS NO
)

# Platform-specific settings
if(UNIX AND NOT APPLE)
    target_link_libraries(picoquic-minimal PUBLIC dl m)
elseif(WIN32)
    target_link_libraries(picoquic-minimal PUBLIC ws2_32 secur32)
endif()

# Install headers and library
install(TARGETS picoquic-minimal
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
)

install(FILES ${PICOQUIC_MINIMAL_HEADERS}
    DESTINATION include/picoquic
)
