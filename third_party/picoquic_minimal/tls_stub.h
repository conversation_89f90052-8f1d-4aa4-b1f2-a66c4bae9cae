#ifndef TLS_STUB_H
#define TLS_STUB_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Minimal TLS stub definitions for picoquic */

/* PTLS stub types */
typedef struct st_ptls_t ptls_t;
typedef struct st_ptls_context_t ptls_context_t;
typedef struct st_ptls_handshake_properties_t ptls_handshake_properties_t;
typedef struct st_ptls_buffer_t ptls_buffer_t;
typedef struct st_ptls_iovec_t ptls_iovec_t;

/* PTLS error codes */
#define PTLS_ERROR_NO_MEMORY -1
#define PTLS_ERROR_IN_PROGRESS -2
#define PTLS_ERROR_LIBRARY -3
#define PTLS_HANDSHAKE_IN_PROGRESS 1
#define PTLS_HANDSHAKE_FINISHED 0

/* PTLS alert codes */
#define PTLS_ALERT_CLOSE_NOTIFY 0
#define PTLS_ALERT_UNEXPECTED_MESSAGE 10
#define PTLS_ALERT_BAD_RECORD_MAC 20
#define PTLS_ALERT_HANDSHAKE_FAILURE 40
#define PTLS_ALERT_BAD_CERTIFICATE 42
#define PTLS_ALERT_UNSUPPORTED_CERTIFICATE 43
#define PTLS_ALERT_CERTIFICATE_REVOKED 44
#define PTLS_ALERT_CERTIFICATE_EXPIRED 45
#define PTLS_ALERT_CERTIFICATE_UNKNOWN 46
#define PTLS_ALERT_ILLEGAL_PARAMETER 47
#define PTLS_ALERT_UNKNOWN_CA 48
#define PTLS_ALERT_ACCESS_DENIED 49
#define PTLS_ALERT_DECODE_ERROR 50
#define PTLS_ALERT_DECRYPT_ERROR 51
#define PTLS_ALERT_PROTOCOL_VERSION 70
#define PTLS_ALERT_INSUFFICIENT_SECURITY 71
#define PTLS_ALERT_INTERNAL_ERROR 80
#define PTLS_ALERT_USER_CANCELED 90
#define PTLS_ALERT_NO_RENEGOTIATION 100
#define PTLS_ALERT_MISSING_EXTENSION 109
#define PTLS_ALERT_UNSUPPORTED_EXTENSION 110
#define PTLS_ALERT_UNRECOGNIZED_NAME 112
#define PTLS_ALERT_BAD_CERTIFICATE_STATUS_RESPONSE 113
#define PTLS_ALERT_UNKNOWN_PSK_IDENTITY 115
#define PTLS_ALERT_CERTIFICATE_REQUIRED 116
#define PTLS_ALERT_NO_APPLICATION_PROTOCOL 120

/* PTLS structures */
struct st_ptls_iovec_t {
    uint8_t* base;
    size_t len;
};

struct st_ptls_buffer_t {
    uint8_t* base;
    size_t capacity;
    size_t off;
    int is_allocated;
};

struct st_ptls_context_t {
    /* Minimal context for stub */
    void* dummy;
};

struct st_ptls_t {
    /* Minimal TLS state for stub */
    ptls_context_t* ctx;
    int is_server;
    int state;
    void* user_data;
};

struct st_ptls_handshake_properties_t {
    /* Minimal handshake properties */
    void* dummy;
};

/* PTLS function stubs */
ptls_context_t* ptls_context_new(void);
void ptls_context_free(ptls_context_t* ctx);

ptls_t* ptls_new(ptls_context_t* ctx, int is_server);
void ptls_free(ptls_t* tls);

int ptls_handshake(ptls_t* tls, ptls_buffer_t* sendbuf, const void* input, size_t* inlen,
                   ptls_handshake_properties_t* properties);

int ptls_send(ptls_t* tls, ptls_buffer_t* sendbuf, const void* input, size_t inlen);
int ptls_receive(ptls_t* tls, ptls_buffer_t* decryptbuf, const void* input, size_t* inlen);

int ptls_get_server_name(ptls_t* tls, const char** server_name);
int ptls_set_server_name(ptls_t* tls, const char* server_name, size_t server_name_len);

/* Buffer management */
void ptls_buffer_init(ptls_buffer_t* buf, void* base, size_t capacity);
void ptls_buffer_dispose(ptls_buffer_t* buf);
int ptls_buffer_reserve(ptls_buffer_t* buf, size_t delta);

/* Utility functions */
void ptls_clear_memory(void* p, size_t len);

/* Forward declare cipher suite structure - must match picoquic's expectation */
struct st_ptls_cipher_suite_t {
    uint16_t id;
    const char* name;
};

/* Cipher suite stubs - declare as const to match picoquic's typedef */
extern const struct st_ptls_cipher_suite_t ptls_aes128gcmsha256;
extern const struct st_ptls_cipher_suite_t ptls_aes256gcmsha384;
extern const struct st_ptls_cipher_suite_t ptls_chacha20poly1305sha256;

/* Key exchange stubs */
typedef struct st_ptls_key_exchange_algorithm_t {
    uint16_t id;
    const char* name;
} ptls_key_exchange_algorithm_t;

extern ptls_key_exchange_algorithm_t ptls_secp256r1;
extern ptls_key_exchange_algorithm_t ptls_x25519;

/* Certificate verification stub */
typedef struct st_ptls_verify_certificate_t {
    int (*verify)(void* verify_ctx, ptls_t* tls, const char* server_name,
                  ptls_iovec_t* certs, size_t num_certs);
} ptls_verify_certificate_t;

/* Random number generation stub */
void ptls_get_random_bytes(void* buf, size_t len);

#ifdef __cplusplus
}
#endif

#endif /* TLS_STUB_H */
