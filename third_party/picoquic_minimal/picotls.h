#ifndef PICOTLS_H
#define PICOTLS_H

/* Picotls stub header for minimal picoquic build */

#include "tls_stub.h"

/* Re-export all TLS stub definitions - types are already defined in tls_stub.h */
/* Add the typedef that pico<PERSON><PERSON> expects */
typedef const struct st_ptls_cipher_suite_t ptls_cipher_suite_t;

/* Additional picotls definitions that might be needed */
#define PTLS_MAX_SECRET_SIZE 64
#define PTLS_MAX_DIGEST_SIZE 64
#define PTLS_HKDF_EXPAND_LABEL_PREFIX "tls13 "

/* Hash algorithm identifiers */
#define PTLS_HASH_ALGORITHM_SHA256 4
#define PTLS_HASH_ALGORITHM_SHA384 5
#define PTLS_HASH_ALGORITHM_SHA512 6

/* Signature algorithm identifiers */
#define PTLS_SIGNATURE_RSA_PSS_RSAE_SHA256 0x0804
#define PTLS_SIGNATURE_ECDSA_SECP256R1_SHA256 0x0403
#define PTLS_SIGNATURE_ED25519 0x0807

/* Extension types */
#define PTLS_EXTENSION_TYPE_SERVER_NAME 0
#define PTLS_EXTENSION_TYPE_SUPPORTED_GROUPS 10
#define PTLS_EXTENSION_TYPE_SIGNATURE_ALGORITHMS 13
#define PTLS_EXTENSION_TYPE_APPLICATION_LAYER_PROTOCOL_NEGOTIATION 16
#define PTLS_EXTENSION_TYPE_KEY_SHARE 51

/* Content types */
#define PTLS_CONTENT_TYPE_CHANGE_CIPHER_SPEC 20
#define PTLS_CONTENT_TYPE_ALERT 21
#define PTLS_CONTENT_TYPE_HANDSHAKE 22
#define PTLS_CONTENT_TYPE_APPLICATION_DATA 23

/* Handshake types */
#define PTLS_HANDSHAKE_TYPE_CLIENT_HELLO 1
#define PTLS_HANDSHAKE_TYPE_SERVER_HELLO 2
#define PTLS_HANDSHAKE_TYPE_ENCRYPTED_EXTENSIONS 8
#define PTLS_HANDSHAKE_TYPE_CERTIFICATE 11
#define PTLS_HANDSHAKE_TYPE_CERTIFICATE_VERIFY 15
#define PTLS_HANDSHAKE_TYPE_FINISHED 20

/* Additional stub structures */
typedef struct st_ptls_hash_algorithm_t {
    uint8_t id;
    const char* name;
    size_t digest_size;
} ptls_hash_algorithm_t;

typedef struct st_ptls_hash_context_t {
    const ptls_hash_algorithm_t* algo;
    void* ctx;
} ptls_hash_context_t;

typedef struct st_ptls_aead_algorithm_t {
    const char* name;
    size_t key_size;
    size_t iv_size;
    size_t tag_size;
} ptls_aead_algorithm_t;

typedef struct st_ptls_aead_context_t {
    const ptls_aead_algorithm_t* algo;
    void* ctx;
} ptls_aead_context_t;

/* Hash algorithm stubs */
extern ptls_hash_algorithm_t ptls_sha256_algorithm;
extern ptls_hash_algorithm_t ptls_sha384_algorithm;
extern ptls_hash_algorithm_t ptls_sha512_algorithm;

/* AEAD algorithm stubs */
extern ptls_aead_algorithm_t ptls_aes128gcm_algorithm;
extern ptls_aead_algorithm_t ptls_aes256gcm_algorithm;
extern ptls_aead_algorithm_t ptls_chacha20poly1305_algorithm;

/* Function stubs for hash operations */
ptls_hash_context_t* ptls_hash_create(const ptls_hash_algorithm_t* algo);
void ptls_hash_update(ptls_hash_context_t* ctx, const void* input, size_t len);
void ptls_hash_final(ptls_hash_context_t* ctx, void* output, int free_ctx);

/* Function stubs for AEAD operations */
ptls_aead_context_t* ptls_aead_new(const ptls_aead_algorithm_t* algo, int is_enc, const void* key, const char* label_prefix);
void ptls_aead_free(ptls_aead_context_t* ctx);
size_t ptls_aead_encrypt(ptls_aead_context_t* ctx, void* output, const void* input, size_t inlen, uint64_t seq, const void* aad, size_t aadlen);
size_t ptls_aead_decrypt(ptls_aead_context_t* ctx, void* output, const void* input, size_t inlen, uint64_t seq, const void* aad, size_t aadlen);

/* HKDF functions */
int ptls_hkdf_extract(const ptls_hash_algorithm_t* hash, void* prk, const void* salt, size_t saltlen, const void* ikm, size_t ikmlen);
int ptls_hkdf_expand(const ptls_hash_algorithm_t* hash, void* okm, size_t okmlen, const void* prk, size_t prklen, const void* info, size_t infolen);
int ptls_hkdf_expand_label(const ptls_hash_algorithm_t* hash, void* output, size_t outlen, const void* secret, size_t secretlen, const char* label, const void* hash_value, size_t hash_len, const char* label_prefix);

/* Key schedule functions */
int ptls_key_schedule_extract(ptls_t* tls, void* secret, const void* ikm, size_t ikmlen);
int ptls_key_schedule_select_cipher(ptls_t* tls, const ptls_cipher_suite_t* cipher, int is_second_flight);

/* Certificate functions */
int ptls_load_certificates(ptls_context_t* ctx, char* cert_pem_file);
int ptls_load_private_key(ptls_context_t* ctx, char* key_pem_file);

/* Utility macros */
#define PTLS_CALLBACK_TYPE0(ret, name) \
    ret (*name)(ptls_t* tls)

#define PTLS_CALLBACK_TYPE(ret, name, ...) \
    ret (*name)(ptls_t* tls, __VA_ARGS__)

/* Additional constants that might be referenced */
#define PTLS_PROTOCOL_VERSION_TLS13 0x0304
#define PTLS_PROTOCOL_VERSION_DRAFT_23 0x7f17
#define PTLS_PROTOCOL_VERSION_DRAFT_24 0x7f18
#define PTLS_PROTOCOL_VERSION_DRAFT_25 0x7f19
#define PTLS_PROTOCOL_VERSION_DRAFT_26 0x7f1a
#define PTLS_PROTOCOL_VERSION_DRAFT_27 0x7f1b
#define PTLS_PROTOCOL_VERSION_DRAFT_28 0x7f1c
#define PTLS_PROTOCOL_VERSION_DRAFT_29 0x7f1d

#endif /* PICOTLS_H */
