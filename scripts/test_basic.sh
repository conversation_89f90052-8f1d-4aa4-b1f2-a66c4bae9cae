#!/bin/bash

# Basic functionality test script for QUIC Stub Client

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
STUB_BINARY="$BUILD_DIR/stub"

echo "=== QUIC Stub Client Basic Test ==="
echo "Project root: $PROJECT_ROOT"
echo "Build directory: $BUILD_DIR"
echo "Stub binary: $STUB_BINARY"
echo

# Check if binary exists
if [ ! -f "$STUB_BINARY" ]; then
    echo "Error: Stub binary not found at $STUB_BINARY"
    echo "Please build the project first:"
    echo "  mkdir -p build && cd build && cmake .. && make"
    exit 1
fi

echo "✓ Stub binary found"

# Test 1: Help message
echo
echo "Test 1: Help message"
echo "Command: $STUB_BINARY --help"
if $STUB_BINARY --help > /dev/null 2>&1; then
    echo "✓ Help message test passed"
else
    echo "✗ Help message test failed"
    exit 1
fi

# Test 2: Version information
echo
echo "Test 2: Version information"
echo "Command: $STUB_BINARY --version"
if $STUB_BINARY --version > /dev/null 2>&1; then
    echo "✓ Version information test passed"
else
    echo "✗ Version information test failed"
    exit 1
fi

# Test 3: Invalid arguments
echo
echo "Test 3: Invalid arguments handling"
echo "Command: $STUB_BINARY --invalid-option"
if $STUB_BINARY --invalid-option > /dev/null 2>&1; then
    echo "✗ Invalid arguments test failed (should have failed)"
    exit 1
else
    echo "✓ Invalid arguments test passed"
fi

# Test 4: Invalid port number
echo
echo "Test 4: Invalid port number handling"
echo "Command: $STUB_BINARY -p 99999"
if $STUB_BINARY -p 99999 > /dev/null 2>&1; then
    echo "✗ Invalid port test failed (should have failed)"
    exit 1
else
    echo "✓ Invalid port test passed"
fi

# Test 5: Invalid log level
echo
echo "Test 5: Invalid log level handling"
echo "Command: $STUB_BINARY -l 10"
if $STUB_BINARY -l 10 > /dev/null 2>&1; then
    echo "✗ Invalid log level test failed (should have failed)"
    exit 1
else
    echo "✓ Invalid log level test passed"
fi

# Test 6: Connection attempt (will fail but should not crash)
echo
echo "Test 6: Connection attempt to non-existent server"
echo "Command: $STUB_BINARY -s 127.0.0.1 -p 9999 (timeout after 5 seconds)"
timeout 5s $STUB_BINARY -s 127.0.0.1 -p 9999 > /dev/null 2>&1 || true
echo "✓ Connection attempt test passed (expected to timeout)"

# Test 7: Check binary size (for embedded systems)
echo
echo "Test 7: Binary size check"
BINARY_SIZE=$(stat -f%z "$STUB_BINARY" 2>/dev/null || stat -c%s "$STUB_BINARY" 2>/dev/null || echo "unknown")
echo "Binary size: $BINARY_SIZE bytes"
if [ "$BINARY_SIZE" != "unknown" ] && [ "$BINARY_SIZE" -lt 5000000 ]; then  # Less than 5MB
    echo "✓ Binary size is reasonable for embedded systems"
else
    echo "⚠ Binary size might be large for embedded systems"
fi

# Test 8: Check for required symbols
echo
echo "Test 8: Symbol check"
if command -v nm > /dev/null 2>&1; then
    echo "Checking for main symbols..."
    if nm "$STUB_BINARY" | grep -q "main"; then
        echo "✓ Main symbol found"
    else
        echo "✗ Main symbol not found"
        exit 1
    fi
    
    # Check for picoquic symbols
    if nm "$STUB_BINARY" | grep -q "picoquic"; then
        echo "✓ Picoquic symbols found"
    else
        echo "⚠ No picoquic symbols found (might be using stub implementation)"
    fi
else
    echo "⚠ nm command not available, skipping symbol check"
fi

# Test 9: Memory leak check (if valgrind is available)
echo
echo "Test 9: Memory leak check"
if command -v valgrind > /dev/null 2>&1; then
    echo "Running valgrind memory check..."
    timeout 10s valgrind --leak-check=summary --error-exitcode=1 \
        $STUB_BINARY --help > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✓ No memory leaks detected"
    else
        echo "⚠ Potential memory issues detected"
    fi
else
    echo "⚠ Valgrind not available, skipping memory leak check"
fi

# Test 10: File descriptor check
echo
echo "Test 10: File descriptor usage"
if command -v lsof > /dev/null 2>&1; then
    echo "Checking file descriptor usage..."
    # Start stub in background for a short time
    timeout 3s $STUB_BINARY -s 127.0.0.1 -p 9999 > /dev/null 2>&1 &
    STUB_PID=$!
    sleep 1
    
    if kill -0 $STUB_PID 2>/dev/null; then
        FD_COUNT=$(lsof -p $STUB_PID 2>/dev/null | wc -l || echo "0")
        echo "File descriptors in use: $FD_COUNT"
        if [ "$FD_COUNT" -lt 50 ]; then
            echo "✓ File descriptor usage is reasonable"
        else
            echo "⚠ High file descriptor usage: $FD_COUNT"
        fi
        kill $STUB_PID 2>/dev/null || true
    else
        echo "⚠ Could not check file descriptor usage"
    fi
else
    echo "⚠ lsof not available, skipping file descriptor check"
fi

echo
echo "=== Test Summary ==="
echo "✓ All basic tests completed successfully"
echo "✓ Binary is ready for deployment"
echo
echo "Next steps:"
echo "1. Test with actual relay server"
echo "2. Performance testing"
echo "3. Long-running stability test"
echo "4. Cross-compilation for target architecture"
echo
