# QUIC Stub Client

一个基于QUIC协议的嵌入式设备客户端，用于通过QUIC隧道转发SOCKS5流量。

## 项目概述

本项目是一个C语言实现的QUIC客户端（Stub），专为嵌入式设备设计，具有以下特点：

- **轻量级设计**：针对嵌入式设备优化，最小化内存和存储占用
- **QUIC协议支持**：使用裁剪版picoquic库，支持"hq-29"协议
- **SOCKS5流量转发**：接收来自relay的SOCKS5流量并转发到目标服务器
- **设备注册**：基于MAC地址生成设备ID并注册到relay
- **多架构支持**：支持arm、mipsel等多种指令集架构

## 架构说明

```
[Relay Server] <--QUIC--> [Stub Client] <--TCP/UDP--> [Target Server]
     ^                         ^
     |                         |
[SOCKS5 Client]         [Embedded Device]
```

- **Relay**: 公网中继服务器，转发SOCKS5流量（另一个仓库）
- **Stub**: 设备端客户端，通常运行在无公网地址的嵌入式设备上

## 核心功能

1. **QUIC连接管理**
   - 建立到relay的QUIC连接
   - 自动重连机制
   - 连接状态监控

2. **设备注册**
   - 基于MAC地址生成唯一设备ID
   - TLV格式的注册协议
   - 注册状态管理

3. **流量转发**
   - TCP连接的双向透传
   - UDP会话管理
   - 协议解析和封装

4. **日志系统**
   - 分级日志记录
   - 详细的调试信息
   - 便于问题排查

## 编译构建

### 依赖要求

- CMake 3.10+
- C11兼容的编译器
- POSIX兼容系统

### 构建步骤

```bash
# 克隆项目
git clone <repository-url>
cd stub

# 创建构建目录
mkdir build && cd build

# 配置和编译
cmake ..
make

# 安装（可选）
make install
```

### 交叉编译

```bash
# ARM架构示例
cmake -DCMAKE_TOOLCHAIN_FILE=arm-toolchain.cmake ..
make

# MIPSEL架构示例
cmake -DCMAKE_TOOLCHAIN_FILE=mipsel-toolchain.cmake ..
make
```

## 使用方法

### 基本用法

```bash
# 连接到relay服务器
./stub -s relay.example.com -p 4433

# 指定日志级别
./stub -s ************* -l 3

# 后台运行
./stub -s relay.example.com -d
```

### 命令行选项

```
-h, --help              显示帮助信息
-v, --version           显示版本信息
-s, --server HOST       服务器地址 (默认: 127.0.0.1)
-p, --port PORT         服务器端口 (默认: 4433)
-l, --log-level LEVEL   日志级别: 0=error, 1=warn, 2=info, 3=debug, 4=trace
-d, --daemon            后台运行
-c, --config FILE       配置文件 (未实现)
```

### 信号处理

```bash
# 优雅关闭
kill -TERM <pid>

# 打印统计信息
kill -USR1 <pid>
```

## 协议设计

详细的协议设计请参考 [docs/protocol.md](docs/protocol.md)

### 设备注册协议

使用TLV格式：
- Type: 消息类型 (1字节)
- Length: 数据长度 (2字节，网络字节序)
- Value: 数据内容

### 流量转发协议

支持两种命令：
- `CMD_TCP_CONNECT (0x01)`: TCP连接请求
- `CMD_UDP_RELAY (0x02)`: UDP转发请求

## 配置说明

### 编译时配置

在 `include/stub_config.h` 中可以调整：

```c
#define MAX_RECONNECT_ATTEMPTS 10
#define RECONNECT_BASE_DELAY_MS 1000
#define CONNECTION_TIMEOUT_MS 60000
#define MAX_CONCURRENT_STREAMS 100
```

### 运行时配置

通过命令行参数或环境变量配置运行参数。

## 性能优化

### 内存优化

- 使用对象池管理流上下文
- 预分配缓冲区
- 及时释放不用的资源

### 网络优化

- 非阻塞I/O
- 事件驱动架构
- 连接复用

### 编译优化

```bash
# 发布版本编译
cmake -DCMAKE_BUILD_TYPE=Release ..
make
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连通性
   - 确认服务器地址和端口
   - 查看防火墙设置

2. **注册失败**
   - 检查设备MAC地址获取
   - 确认注册协议格式
   - 查看服务器日志

3. **流量转发异常**
   - 检查目标服务器可达性
   - 确认协议解析正确
   - 查看详细日志

### 调试方法

```bash
# 启用详细日志
./stub -s server -l 4

# 使用调试器
gdb ./stub
```

## 开发指南

### 代码结构

```
src/
├── main.c              # 主程序入口
├── client/             # QUIC客户端
├── device/             # 设备管理
├── protocol/           # 协议处理
├── handler/            # 流处理器
└── utils/              # 工具函数
```

### 添加新功能

1. 在相应模块添加接口
2. 实现功能逻辑
3. 添加单元测试
4. 更新文档

### 测试

```bash
# 编译测试版本
cmake -DCMAKE_BUILD_TYPE=Debug ..
make

# 运行测试
make test
```

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue
- 发送邮件
- 技术讨论群
