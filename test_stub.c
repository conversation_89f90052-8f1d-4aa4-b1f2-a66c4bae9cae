#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

// Include picoquic headers
#include "third_party/picoquic/picoquic/picoquic.h"

int main() {
    printf("Testing QUIC stub implementation...\n");
    
    // Test 1: Create QUIC context
    printf("Test 1: Creating QUIC context...\n");
    uint64_t current_time = picoquic_current_time();
    printf("Current time: %llu\n", current_time);
    
    picoquic_quic_t* quic = picoquic_create(
        8,                              /* max_nb_connections */
        NULL,                           /* cert_file_name */
        NULL,                           /* key_file_name */
        NULL,                           /* cert_root_file_name */
        "hq-29",                        /* default_alpn */
        NULL,                           /* default_callback_fn */
        NULL,                           /* default_callback_ctx */
        NULL,                           /* cnx_id_callback */
        NULL,                           /* cnx_id_callback_data */
        NULL,                           /* reset_seed */
        current_time,                   /* current_time */
        NULL,                           /* p_simulated_time */
        NULL,                           /* ticket_file_name */
        NULL,                           /* ticket_encryption_key */
        0                               /* ticket_encryption_key_length */
    );
    
    if (quic) {
        printf("✓ QUIC context created successfully\n");
        
        // Test 2: Create client connection
        printf("Test 2: Creating client connection...\n");
        
        struct sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(4433);
        server_addr.sin_addr.s_addr = inet_addr("127.0.0.1");
        
        picoquic_cnx_t* cnx = picoquic_create_client_cnx(
            quic,
            (struct sockaddr*)&server_addr,
            current_time,
            0,                          /* preferred_version */
            "localhost",                /* sni */
            "hq-29",                    /* alpn */
            NULL,                       /* callback_fn */
            NULL                        /* callback_ctx */
        );
        
        if (cnx) {
            printf("✓ Client connection created successfully\n");
            
            // Test 3: Start connection
            printf("Test 3: Starting connection...\n");
            int ret = picoquic_start_client_cnx(cnx);
            if (ret == 0) {
                printf("✓ Connection started successfully\n");
            } else {
                printf("✗ Failed to start connection: %d\n", ret);
            }
            
            // Clean up connection
            picoquic_delete_cnx(cnx);
        } else {
            printf("✗ Failed to create client connection\n");
        }
        
        // Clean up QUIC context
        picoquic_free(quic);
    } else {
        printf("✗ Failed to create QUIC context\n");
        return 1;
    }
    
    printf("All tests completed!\n");
    return 0;
}
