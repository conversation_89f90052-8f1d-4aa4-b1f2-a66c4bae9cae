#ifndef STREAM_HANDLER_H
#define STREAM_HANDLER_H

#include <stdint.h>
#include <stddef.h>
#include "stub_config.h"
#include "protocol.h"

#if HAVE_PICOQUIC
#include <picoquic.h>
#else
/* Stub definitions for picoquic types when not available */
typedef struct st_picoquic_cnx_t picoquic_cnx_t;
typedef enum {
    picoquic_callback_stream_data = 0,
    picoquic_callback_stream_fin,
    picoquic_callback_stream_reset
} picoquic_call_back_event_t;
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* Stream context types */
typedef enum {
    STREAM_TYPE_UNKNOWN = 0,
    STREAM_TYPE_TCP,
    STREAM_TYPE_UDP,
    STREAM_TYPE_REGISTRATION
} stream_type_t;

/* Stream state */
typedef enum {
    STREAM_STATE_INIT = 0,
    STREAM_STATE_HEADER_PARSING,
    STREAM_STATE_ACTIVE,
    STREAM_STATE_CLOSING,
    STREAM_STATE_CLOSED,
    STREAM_STATE_ERROR
} stream_state_t;

/* Base stream context */
typedef struct {
    uint64_t stream_id;
    stream_type_t type;
    stream_state_t state;
    
    /* Protocol parsing state */
    uint8_t header_buffer[32];
    size_t header_received;
    size_t header_expected;
    protocol_message_t message;
    int header_parsed;
    
    /* Statistics */
    uint64_t bytes_received;
    uint64_t bytes_sent;
    uint64_t created_time;
    
    /* Type-specific context */
    void* type_ctx;
} stream_context_t;

/* Stream handler functions */
int stream_handler_init(void);
void stream_handler_cleanup(void);

/* Stream processing functions */
int stream_handler_process_data(picoquic_cnx_t* cnx, uint64_t stream_id, uint8_t* bytes, size_t length,
                               picoquic_call_back_event_t fin_or_event, void* callback_ctx, void* stream_ctx);
int stream_handler_prepare_data(picoquic_cnx_t* cnx, uint64_t stream_id, uint8_t* bytes, size_t length,
                               void* callback_ctx, void* stream_ctx);

/* Stream context management */
stream_context_t* stream_handler_create_context(uint64_t stream_id);
void stream_handler_destroy_context(stream_context_t* ctx);
stream_context_t* stream_handler_get_context(uint64_t stream_id);

/* Stream state management */
int stream_handler_set_state(stream_context_t* ctx, stream_state_t new_state);
const char* stream_handler_get_state_string(stream_state_t state);
const char* stream_handler_get_type_string(stream_type_t type);

/* Protocol parsing helpers */
int stream_handler_parse_header(stream_context_t* ctx, const uint8_t* data, size_t length);
int stream_handler_determine_type(const protocol_message_t* message, stream_type_t* type);
int stream_handler_validate_message(const protocol_message_t* message);

/* Stream delegation */
int stream_handler_delegate_tcp(picoquic_cnx_t* cnx, stream_context_t* ctx, 
                               const uint8_t* data, size_t length, 
                               picoquic_call_back_event_t fin_or_event);
int stream_handler_delegate_udp(picoquic_cnx_t* cnx, stream_context_t* ctx, 
                               const uint8_t* data, size_t length, 
                               picoquic_call_back_event_t fin_or_event);

/* Statistics and debugging */
void stream_handler_print_stats(void);
void stream_handler_dump_context(const stream_context_t* ctx);
int stream_handler_get_active_count(void);

#ifdef __cplusplus
}
#endif

#endif /* STREAM_HANDLER_H */
