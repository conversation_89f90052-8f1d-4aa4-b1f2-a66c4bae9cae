#ifndef UDP_HANDLER_H
#define UDP_HANDLER_H

#include <stdint.h>
#include <stddef.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include "stub_config.h"
#include "stream_handler.h"

#if HAVE_PICOQUIC
#include <picoquic.h>
#else
typedef struct st_picoquic_cnx_t picoquic_cnx_t;
typedef enum {
    picoquic_callback_stream_data = 0,
    picoquic_callback_stream_fin
} picoquic_call_back_event_t;
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* UDP session state */
typedef enum {
    UDP_STATE_INIT = 0,
    UDP_STATE_ACTIVE,
    UDP_STATE_CLOSING,
    UDP_STATE_CLOSED,
    UDP_STATE_ERROR
} udp_state_t;

/* UDP target entry */
typedef struct udp_target {
    struct sockaddr_storage addr;
    socklen_t addr_len;
    uint64_t last_activity;
    uint64_t bytes_sent;
    uint64_t bytes_received;
    struct udp_target* next;
} udp_target_t;

/* UDP context */
typedef struct {
    /* Socket */
    int sockfd;
    struct sockaddr_storage local_addr;
    socklen_t local_addr_len;
    
    /* State */
    udp_state_t state;
    uint64_t created_time;
    
    /* Target management */
    udp_target_t* targets;
    udp_target_t* current_target;
    int target_count;
    
    /* Buffers */
    uint8_t* recv_buffer;
    size_t recv_buffer_size;
    size_t recv_buffer_used;
    
    uint8_t* send_buffer;
    size_t send_buffer_size;
    
    /* Statistics */
    uint64_t total_bytes_sent;
    uint64_t total_bytes_received;
    uint64_t packets_sent;
    uint64_t packets_received;
    
    /* Control flags */
    int should_close;
} udp_context_t;

/* UDP handler functions */
int udp_handler_init(void);
void udp_handler_cleanup(void);

/* Stream processing */
int udp_handler_process_stream(picoquic_cnx_t* cnx, stream_context_t* stream_ctx, 
                              const uint8_t* data, size_t length, 
                              picoquic_call_back_event_t fin_or_event);
int udp_handler_prepare_data(picoquic_cnx_t* cnx, stream_context_t* stream_ctx, 
                            uint8_t* bytes, size_t length);

/* UDP context management */
udp_context_t* udp_handler_create_context(void);
void udp_handler_cleanup_context(udp_context_t* ctx);

/* UDP socket management */
int udp_handler_create_socket(udp_context_t* ctx);
int udp_handler_close_socket(udp_context_t* ctx);

/* Target management */
udp_target_t* udp_handler_find_target(udp_context_t* ctx, const struct sockaddr_storage* addr);
udp_target_t* udp_handler_add_target(udp_context_t* ctx, const struct sockaddr_storage* addr, socklen_t addr_len);
void udp_handler_remove_target(udp_context_t* ctx, udp_target_t* target);
void udp_handler_cleanup_targets(udp_context_t* ctx);

/* Data processing */
int udp_handler_process_packet(udp_context_t* ctx, const uint8_t* data, size_t length, 
                              const protocol_address_t* target_addr);
int udp_handler_send_to_target(udp_context_t* ctx, const uint8_t* data, size_t length, 
                              const struct sockaddr_storage* target_addr);
int udp_handler_receive_from_targets(udp_context_t* ctx);

/* Protocol handling */
int udp_handler_parse_packet(const uint8_t* data, size_t length, 
                            protocol_address_t* addr, const uint8_t** payload, size_t* payload_len);
int udp_handler_build_response(const struct sockaddr_storage* from_addr, const uint8_t* payload, size_t payload_len,
                              uint8_t* buffer, size_t buffer_size, size_t* response_len);

/* State management */
int udp_handler_set_state(udp_context_t* ctx, udp_state_t new_state);
const char* udp_handler_get_state_string(udp_state_t state);
int udp_handler_should_close(const udp_context_t* ctx);

/* Utilities */
void udp_handler_dump_context(const udp_context_t* ctx);
void udp_handler_print_stats(const udp_context_t* ctx);
void udp_handler_dump_target(const udp_target_t* target);

#ifdef __cplusplus
}
#endif

#endif /* UDP_HANDLER_H */
