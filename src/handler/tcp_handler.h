#ifndef TCP_HANDLER_H
#define TCP_HANDLER_H

#include <stdint.h>
#include <stddef.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include "stub_config.h"
#include "stream_handler.h"

#if HAVE_PICOQUIC
#include <picoquic.h>
#else
typedef struct st_picoquic_cnx_t picoquic_cnx_t;
typedef enum {
    picoquic_callback_stream_data = 0,
    picoquic_callback_stream_fin
} picoquic_call_back_event_t;
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* TCP connection state */
typedef enum {
    TCP_STATE_INIT = 0,
    TCP_STATE_CONNECTING,
    TCP_STATE_CONNECTED,
    TCP_STATE_CLOSING,
    TCP_STATE_CLOSED,
    TCP_STATE_ERROR
} tcp_state_t;

/* TCP context */
typedef struct {
    /* Connection info */
    int sockfd;
    struct sockaddr_storage target_addr;
    socklen_t target_addr_len;
    
    /* State */
    tcp_state_t state;
    uint64_t connect_time;
    
    /* Buffers */
    uint8_t* send_buffer;
    size_t send_buffer_size;
    size_t send_buffer_used;
    size_t send_buffer_sent;
    
    uint8_t* recv_buffer;
    size_t recv_buffer_size;
    size_t recv_buffer_used;
    
    /* Statistics */
    uint64_t bytes_to_target;
    uint64_t bytes_from_target;
    
    /* Control flags */
    int target_eof;
    int quic_eof;
    int should_close;
} tcp_context_t;

/* TCP handler functions */
int tcp_handler_init(void);
void tcp_handler_cleanup(void);

/* Stream processing */
int tcp_handler_process_stream(picoquic_cnx_t* cnx, stream_context_t* stream_ctx, 
                              const uint8_t* data, size_t length, 
                              picoquic_call_back_event_t fin_or_event);
int tcp_handler_prepare_data(picoquic_cnx_t* cnx, stream_context_t* stream_ctx, 
                            uint8_t* bytes, size_t length);

/* TCP context management */
tcp_context_t* tcp_handler_create_context(const protocol_address_t* target_addr);
void tcp_handler_cleanup_context(tcp_context_t* ctx);

/* TCP connection management */
int tcp_handler_connect_target(tcp_context_t* ctx);
int tcp_handler_check_connection(tcp_context_t* ctx);
int tcp_handler_close_connection(tcp_context_t* ctx);

/* Data transfer */
int tcp_handler_send_to_target(tcp_context_t* ctx, const uint8_t* data, size_t length);
int tcp_handler_receive_from_target(tcp_context_t* ctx);
int tcp_handler_flush_send_buffer(tcp_context_t* ctx);

/* Buffer management */
int tcp_handler_buffer_send_data(tcp_context_t* ctx, const uint8_t* data, size_t length);
int tcp_handler_get_recv_data(tcp_context_t* ctx, uint8_t** data, size_t* length);
void tcp_handler_consume_recv_data(tcp_context_t* ctx, size_t consumed);

/* State management */
int tcp_handler_set_state(tcp_context_t* ctx, tcp_state_t new_state);
const char* tcp_handler_get_state_string(tcp_state_t state);
int tcp_handler_is_connected(const tcp_context_t* ctx);
int tcp_handler_should_close(const tcp_context_t* ctx);

/* Utilities */
void tcp_handler_dump_context(const tcp_context_t* ctx);
void tcp_handler_print_stats(const tcp_context_t* ctx);

#ifdef __cplusplus
}
#endif

#endif /* TCP_HANDLER_H */
