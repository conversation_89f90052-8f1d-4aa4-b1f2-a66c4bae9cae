#include "device_id.h"
#include "logger.h"
#include <string.h>
#include <stdio.h>
#include <ctype.h>
#include <errno.h>
#include <ifaddrs.h>
#include <net/if.h>
#include <sys/socket.h>

#ifdef __linux__
#include <linux/if_packet.h>
#include <net/ethernet.h>
#elif defined(__APPLE__)
#include <net/if_dl.h>
#endif

int device_id_init(device_id_t* device_id)
{
    if (!device_id) {
        return STUB_ERROR_PROTOCOL;
    }
    
    memset(device_id, 0, sizeof(*device_id));
    return STUB_SUCCESS;
}

int device_id_generate(device_id_t* device_id)
{
    if (!device_id) {
        return STUB_ERROR_PROTOCOL;
    }
    
    /* Get the first valid MAC address */
    int ret = get_first_valid_mac_address(device_id->mac_addr);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to get MAC address for device ID generation");
        return ret;
    }
    
    /* Convert MAC address to hex string */
    snprintf(device_id->device_id, sizeof(device_id->device_id),
             "%02x%02x%02x%02x%02x%02x",
             device_id->mac_addr[0], device_id->mac_addr[1], device_id->mac_addr[2],
             device_id->mac_addr[3], device_id->mac_addr[4], device_id->mac_addr[5]);
    
    device_id->is_valid = 1;
    
    LOG_INFO("Generated device ID: %s", device_id->device_id);
    LOG_HEX_DEBUG("MAC address", device_id->mac_addr, 6);
    
    return STUB_SUCCESS;
}

const char* device_id_get_string(const device_id_t* device_id)
{
    if (!device_id || !device_id->is_valid) {
        return NULL;
    }
    
    return device_id->device_id;
}

int device_id_is_valid(const device_id_t* device_id)
{
    return device_id && device_id->is_valid;
}

void device_id_cleanup(device_id_t* device_id)
{
    if (device_id) {
        memset(device_id, 0, sizeof(*device_id));
    }
}

int get_first_valid_mac_address(uint8_t mac[6])
{
    struct ifaddrs *ifaddrs_ptr, *ifa;
    int ret = STUB_ERROR_NETWORK;
    
    if (getifaddrs(&ifaddrs_ptr) == -1) {
        LOG_ERROR("Failed to get network interfaces: %s", strerror(errno));
        return STUB_ERROR_NETWORK;
    }
    
    for (ifa = ifaddrs_ptr; ifa != NULL; ifa = ifa->ifa_next) {
        if (!ifa->ifa_addr) {
            continue;
        }
        
        /* Skip loopback interfaces */
        if (ifa->ifa_flags & IFF_LOOPBACK) {
            continue;
        }
        
        /* Skip interfaces that are down */
        if (!(ifa->ifa_flags & IFF_UP)) {
            continue;
        }
        
#ifdef __linux__
        if (ifa->ifa_addr->sa_family == AF_PACKET) {
            struct sockaddr_ll *s = (struct sockaddr_ll*)ifa->ifa_addr;
            if (s->sll_halen == 6) {
                memcpy(mac, s->sll_addr, 6);
                
                /* Validate MAC address */
                if (validate_mac_address(mac)) {
                    LOG_DEBUG("Found valid MAC address on interface %s", ifa->ifa_name);
                    ret = STUB_SUCCESS;
                    break;
                }
            }
        }
#elif defined(__APPLE__)
        if (ifa->ifa_addr->sa_family == AF_LINK) {
            struct sockaddr_dl *s = (struct sockaddr_dl*)ifa->ifa_addr;
            if (s->sdl_alen == 6) {
                memcpy(mac, LLADDR(s), 6);
                
                /* Validate MAC address */
                if (validate_mac_address(mac)) {
                    LOG_DEBUG("Found valid MAC address on interface %s", ifa->ifa_name);
                    ret = STUB_SUCCESS;
                    break;
                }
            }
        }
#endif
    }
    
    freeifaddrs(ifaddrs_ptr);
    
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("No valid MAC address found");
    }
    
    return ret;
}

void format_mac_address(const uint8_t mac[6], char* buffer, size_t buffer_size)
{
    if (!mac || !buffer || buffer_size < 18) {
        return;
    }
    
    snprintf(buffer, buffer_size, "%02x:%02x:%02x:%02x:%02x:%02x",
             mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
}

int parse_mac_address(const char* mac_str, uint8_t mac[6])
{
    if (!mac_str || !mac) {
        return STUB_ERROR_PROTOCOL;
    }
    
    int values[6];
    int count = sscanf(mac_str, "%02x:%02x:%02x:%02x:%02x:%02x",
                      &values[0], &values[1], &values[2],
                      &values[3], &values[4], &values[5]);
    
    if (count != 6) {
        return STUB_ERROR_PROTOCOL;
    }
    
    for (int i = 0; i < 6; i++) {
        mac[i] = (uint8_t)values[i];
    }
    
    return STUB_SUCCESS;
}

int validate_device_id_format(const char* device_id)
{
    if (!device_id) {
        return 0;
    }
    
    size_t len = strlen(device_id);
    if (len != MAX_DEVICE_ID_LENGTH) {
        return 0;
    }
    
    for (size_t i = 0; i < len; i++) {
        if (!isxdigit(device_id[i]) || isupper(device_id[i])) {
            return 0;  /* Must be lowercase hex */
        }
    }
    
    return 1;
}

int validate_mac_address(const uint8_t mac[6])
{
    if (!mac) {
        return 0;
    }
    
    /* Check for all zeros */
    int all_zero = 1;
    for (int i = 0; i < 6; i++) {
        if (mac[i] != 0) {
            all_zero = 0;
            break;
        }
    }
    
    if (all_zero) {
        return 0;
    }
    
    /* Check for broadcast address */
    int all_ff = 1;
    for (int i = 0; i < 6; i++) {
        if (mac[i] != 0xFF) {
            all_ff = 0;
            break;
        }
    }
    
    if (all_ff) {
        return 0;
    }
    
    /* Check for multicast bit (bit 0 of first octet) */
    if (mac[0] & 0x01) {
        return 0;
    }
    
    return 1;
}
