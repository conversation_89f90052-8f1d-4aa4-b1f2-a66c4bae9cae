#ifndef DEVICE_ID_H
#define DEVICE_ID_H

#include <stdint.h>
#include <stddef.h>
#include "stub_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Device ID structure */
typedef struct {
    uint8_t mac_addr[6];
    char device_id[MAX_DEVICE_ID_LENGTH + 1];  /* Hex string + null terminator */
    int is_valid;
} device_id_t;

/* Device ID generation and management */
int device_id_init(device_id_t* device_id);
int device_id_generate(device_id_t* device_id);
const char* device_id_get_string(const device_id_t* device_id);
int device_id_is_valid(const device_id_t* device_id);
void device_id_cleanup(device_id_t* device_id);

/* MAC address utilities */
int get_first_valid_mac_address(uint8_t mac[6]);
int get_mac_address_from_interface(const char* interface_name, uint8_t mac[6]);
void format_mac_address(const uint8_t mac[6], char* buffer, size_t buffer_size);
int parse_mac_address(const char* mac_str, uint8_t mac[6]);

/* Device ID validation */
int validate_device_id_format(const char* device_id);
int validate_mac_address(const uint8_t mac[6]);

#ifdef __cplusplus
}
#endif

#endif /* DEVICE_ID_H */
