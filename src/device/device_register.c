#include "device_register.h"
#include "logger.h"
#include "network_utils.h"
#include <string.h>
#include <stdlib.h>

int device_register_init(device_register_ctx_t* ctx, picoquic_cnx_t* cnx)
{
    if (!ctx || !cnx) {
        return STUB_ERROR_PROTOCOL;
    }
    
    memset(ctx, 0, sizeof(*ctx));
    ctx->cnx = cnx;
    
    /* Generate device ID */
    int ret = device_id_init(&ctx->device_id);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to initialize device ID");
        return ret;
    }
    
    ret = device_id_generate(&ctx->device_id);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to generate device ID");
        return ret;
    }
    
    LOG_INFO("Device registration context initialized with ID: %s", 
             device_id_get_string(&ctx->device_id));
    
    return STUB_SUCCESS;
}

int device_register_start(device_register_ctx_t* ctx)
{
    if (!ctx || !ctx->cnx) {
        return STUB_ERROR_PROTOCOL;
    }
    
    /* Create a new stream for registration */
    ctx->stream_id = picoquic_get_next_local_stream_id(ctx->cnx, 0);
    
    /* Build registration request message */
    uint8_t request_buffer[256];
    size_t request_len;
    
    int ret = build_registration_request(&ctx->device_id, request_buffer, 
                                       sizeof(request_buffer), &request_len);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to build registration request");
        return ret;
    }
    
    LOG_INFO("Starting device registration, stream ID: %llu", 
             (unsigned long long)ctx->stream_id);
    LOG_HEX_DEBUG("Registration request", request_buffer, request_len);
    
    /* Send registration request */
    ret = picoquic_add_to_stream(ctx->cnx, ctx->stream_id, request_buffer, request_len, 1);
    if (ret != 0) {
        LOG_ERROR("Failed to send registration request: %d", ret);
        return STUB_ERROR_NETWORK;
    }
    
    ctx->registration_attempts++;
    ctx->last_attempt_time = picoquic_get_quic_time(picoquic_get_quic_ctx(ctx->cnx));
    
    LOG_INFO("Registration request sent, attempt %d", ctx->registration_attempts);
    
    return STUB_SUCCESS;
}

int device_register_handle_response(device_register_ctx_t* ctx, const uint8_t* data, size_t length)
{
    if (!ctx || !data || length == 0) {
        return STUB_ERROR_PROTOCOL;
    }
    
    LOG_INFO("Received registration response (%zu bytes)", length);
    LOG_HEX_DEBUG("Registration response", data, length);
    
    uint8_t status_code;
    int ret = parse_registration_response(data, length, &status_code);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to parse registration response");
        return ret;
    }
    
    ctx->last_status_code = status_code;
    
    if (is_registration_status_success(status_code)) {
        ctx->is_registered = 1;
        LOG_INFO("Device registration successful, status: %s", 
                get_registration_status_string(status_code));
    } else {
        LOG_WARN("Device registration failed, status: %s", 
                get_registration_status_string(status_code));
        
        if (is_registration_status_retryable(status_code) && 
            ctx->registration_attempts < MAX_RECONNECT_ATTEMPTS) {
            LOG_INFO("Registration failure is retryable, will retry");
        } else {
            LOG_ERROR("Registration failure is not retryable or max attempts reached");
            return STUB_ERROR_REGISTRATION;
        }
    }
    
    return STUB_SUCCESS;
}

int device_register_is_complete(const device_register_ctx_t* ctx)
{
    return ctx && (ctx->is_registered || 
                  (ctx->last_status_code != 0 && !is_registration_status_retryable(ctx->last_status_code)));
}

int device_register_is_successful(const device_register_ctx_t* ctx)
{
    return ctx && ctx->is_registered;
}

void device_register_cleanup(device_register_ctx_t* ctx)
{
    if (ctx) {
        device_id_cleanup(&ctx->device_id);
        memset(ctx, 0, sizeof(*ctx));
    }
}

int tlv_create_message(uint8_t type, const uint8_t* value, uint16_t value_len, 
                      uint8_t* buffer, size_t buffer_size, size_t* message_len)
{
    if (!buffer || !message_len) {
        return STUB_ERROR_PROTOCOL;
    }
    
    size_t required_size = 3 + value_len;  /* Type(1) + Length(2) + Value(value_len) */
    if (buffer_size < required_size) {
        LOG_ERROR("Buffer too small for TLV message: need %zu, have %zu", 
                 required_size, buffer_size);
        return STUB_ERROR_MEMORY;
    }
    
    buffer[0] = type;
    write_uint16_be(&buffer[1], value_len);
    
    if (value && value_len > 0) {
        memcpy(&buffer[3], value, value_len);
    }
    
    *message_len = required_size;
    
    LOG_TRACE("Created TLV message: type=0x%02x, length=%u", type, value_len);
    
    return STUB_SUCCESS;
}

int tlv_parse_message(const uint8_t* data, size_t data_len, tlv_message_t* message)
{
    if (!data || !message || data_len < 3) {
        return STUB_ERROR_PROTOCOL;
    }
    
    message->type = data[0];
    message->length = read_uint16_be(&data[1]);
    
    if (data_len < 3 + message->length) {
        LOG_ERROR("TLV message truncated: expected %u bytes, have %zu", 
                 3 + message->length, data_len);
        return STUB_ERROR_PROTOCOL;
    }
    
    if (message->length > 0) {
        message->value = malloc(message->length);
        if (!message->value) {
            LOG_ERROR("Failed to allocate memory for TLV value");
            return STUB_ERROR_MEMORY;
        }
        memcpy(message->value, &data[3], message->length);
    } else {
        message->value = NULL;
    }
    
    LOG_TRACE("Parsed TLV message: type=0x%02x, length=%u", message->type, message->length);
    
    return STUB_SUCCESS;
}

void tlv_free_message(tlv_message_t* message)
{
    if (message) {
        free(message->value);
        memset(message, 0, sizeof(*message));
    }
}

int build_registration_request(const device_id_t* device_id, uint8_t* buffer, 
                              size_t buffer_size, size_t* message_len)
{
    if (!device_id || !device_id_is_valid(device_id) || !buffer || !message_len) {
        return STUB_ERROR_PROTOCOL;
    }
    
    const char* device_id_str = device_id_get_string(device_id);
    if (!device_id_str) {
        return STUB_ERROR_PROTOCOL;
    }
    
    uint16_t device_id_len = (uint16_t)strlen(device_id_str);
    
    return tlv_create_message(MSG_DEVICE_REGISTER_REQ, 
                             (const uint8_t*)device_id_str, device_id_len,
                             buffer, buffer_size, message_len);
}

int parse_registration_response(const uint8_t* data, size_t data_len, uint8_t* status_code)
{
    if (!data || !status_code) {
        return STUB_ERROR_PROTOCOL;
    }
    
    tlv_message_t message;
    int ret = tlv_parse_message(data, data_len, &message);
    if (ret != STUB_SUCCESS) {
        return ret;
    }
    
    if (message.type != MSG_DEVICE_REGISTER_ACK) {
        LOG_ERROR("Unexpected message type in registration response: 0x%02x", message.type);
        tlv_free_message(&message);
        return STUB_ERROR_PROTOCOL;
    }
    
    if (message.length != 1 || !message.value) {
        LOG_ERROR("Invalid registration response format: length=%u", message.length);
        tlv_free_message(&message);
        return STUB_ERROR_PROTOCOL;
    }
    
    *status_code = message.value[0];
    tlv_free_message(&message);
    
    return STUB_SUCCESS;
}

const char* get_registration_status_string(uint8_t status_code)
{
    switch (status_code) {
        case REG_STATUS_SUCCESS:
            return "Success";
        case REG_STATUS_FORMAT_ERROR:
            return "Format Error";
        case REG_STATUS_DEVICE_EXISTS:
            return "Device Already Exists";
        case REG_STATUS_SERVER_ERROR:
            return "Server Error";
        default:
            return "Unknown Status";
    }
}

int is_registration_status_success(uint8_t status_code)
{
    return status_code == REG_STATUS_SUCCESS;
}

int is_registration_status_retryable(uint8_t status_code)
{
    return status_code == REG_STATUS_SERVER_ERROR;
}
