#ifndef DEVICE_REGISTER_H
#define DEVICE_REGISTER_H

#include <stdint.h>
#include <stddef.h>
#include <picoquic.h>
#include "device_id.h"
#include "stub_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/* TLV message structure */
typedef struct {
    uint8_t type;
    uint16_t length;  /* Network byte order */
    uint8_t* value;
} tlv_message_t;

/* Registration context */
typedef struct {
    device_id_t device_id;
    picoquic_cnx_t* cnx;
    uint64_t stream_id;
    int is_registered;
    int registration_attempts;
    uint64_t last_attempt_time;
    uint8_t last_status_code;
} device_register_ctx_t;

/* Device registration functions */
int device_register_init(device_register_ctx_t* ctx, picoquic_cnx_t* cnx);
int device_register_start(device_register_ctx_t* ctx);
int device_register_handle_response(device_register_ctx_t* ctx, const uint8_t* data, size_t length);
int device_register_is_complete(const device_register_ctx_t* ctx);
int device_register_is_successful(const device_register_ctx_t* ctx);
void device_register_cleanup(device_register_ctx_t* ctx);

/* TLV message utilities */
int tlv_create_message(uint8_t type, const uint8_t* value, uint16_t value_len, 
                      uint8_t* buffer, size_t buffer_size, size_t* message_len);
int tlv_parse_message(const uint8_t* data, size_t data_len, tlv_message_t* message);
void tlv_free_message(tlv_message_t* message);

/* Registration message builders */
int build_registration_request(const device_id_t* device_id, uint8_t* buffer, 
                              size_t buffer_size, size_t* message_len);
int parse_registration_response(const uint8_t* data, size_t data_len, uint8_t* status_code);

/* Status code utilities */
const char* get_registration_status_string(uint8_t status_code);
int is_registration_status_success(uint8_t status_code);
int is_registration_status_retryable(uint8_t status_code);

#ifdef __cplusplus
}
#endif

#endif /* DEVICE_REGISTER_H */
