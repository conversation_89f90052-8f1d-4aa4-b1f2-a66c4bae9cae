#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <signal.h>
#include <unistd.h>
#include <getopt.h>
#include <errno.h>

#include "stub_config.h"
#include "logger.h"
#include "quic_client.h"
#include "stream_handler.h"
#include "tcp_handler.h"
#include "udp_handler.h"

/* Global variables */
static volatile int g_should_stop = 0;
static quic_client_t g_client;

/* Signal handler */
static void signal_handler(int sig)
{
    switch (sig) {
        case SIGINT:
        case SIGTERM:
            LOG_INFO("Received signal %d, shutting down gracefully", sig);
            g_should_stop = 1;
            g_client.should_stop = 1;
            break;
        case SIGUSR1:
            LOG_INFO("Received SIGUSR1, printing statistics");
            quic_client_print_stats(&g_client);
            stream_handler_print_stats();
            break;
        default:
            LOG_WARN("Received unexpected signal %d", sig);
            break;
    }
}

/* Setup signal handlers */
static int setup_signals(void)
{
    struct sigaction sa;
    
    memset(&sa, 0, sizeof(sa));
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = SA_RESTART;
    
    if (sigaction(SIGINT, &sa, NULL) < 0) {
        LOG_ERROR("Failed to setup SIGINT handler: %s", strerror(errno));
        return STUB_ERROR_NETWORK;
    }
    
    if (sigaction(SIGTERM, &sa, NULL) < 0) {
        LOG_ERROR("Failed to setup SIGTERM handler: %s", strerror(errno));
        return STUB_ERROR_NETWORK;
    }
    
    if (sigaction(SIGUSR1, &sa, NULL) < 0) {
        LOG_ERROR("Failed to setup SIGUSR1 handler: %s", strerror(errno));
        return STUB_ERROR_NETWORK;
    }
    
    /* Ignore SIGPIPE */
    signal(SIGPIPE, SIG_IGN);
    
    return STUB_SUCCESS;
}

/* Print usage information */
static void print_usage(const char* program_name)
{
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("\n");
    printf("QUIC Stub Client - Forward SOCKS5 traffic through QUIC tunnel\n");
    printf("\n");
    printf("Options:\n");
    printf("  -h, --help              Show this help message\n");
    printf("  -v, --version           Show version information\n");
    printf("  -s, --server HOST       Server hostname or IP address (default: %s)\n", DEFAULT_SERVER_HOST);
    printf("  -p, --port PORT         Server port (default: %d)\n", DEFAULT_SERVER_PORT);
    printf("  -l, --log-level LEVEL   Log level: 0=error, 1=warn, 2=info, 3=debug, 4=trace (default: %d)\n", DEFAULT_LOG_LEVEL);
    printf("  -d, --daemon            Run as daemon (background)\n");
    printf("  -c, --config FILE       Configuration file (not implemented)\n");
    printf("\n");
    printf("Signals:\n");
    printf("  SIGINT/SIGTERM          Graceful shutdown\n");
    printf("  SIGUSR1                 Print statistics\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s -s relay.example.com -p 4433\n", program_name);
    printf("  %s -s ************* -l 3\n", program_name);
    printf("\n");
}

/* Print version information */
static void print_version(void)
{
    printf("QUIC Stub Client %s\n", STUB_VERSION);
    printf("Built with picoquic library\n");
    printf("Protocol: %s\n", STUB_ALPN);
    printf("\n");
}

/* Parse command line arguments */
static int parse_arguments(int argc, char* argv[], client_config_t* config)
{
    static struct option long_options[] = {
        {"help",      no_argument,       0, 'h'},
        {"version",   no_argument,       0, 'v'},
        {"server",    required_argument, 0, 's'},
        {"port",      required_argument, 0, 'p'},
        {"log-level", required_argument, 0, 'l'},
        {"daemon",    no_argument,       0, 'd'},
        {"config",    required_argument, 0, 'c'},
        {0, 0, 0, 0}
    };
    
    int daemon_mode = 0;
    int opt;
    int option_index = 0;
    
    while ((opt = getopt_long(argc, argv, "hvs:p:l:dc:", long_options, &option_index)) != -1) {
        switch (opt) {
            case 'h':
                print_usage(argv[0]);
                return 1;  /* Exit successfully */
                
            case 'v':
                print_version();
                return 1;  /* Exit successfully */
                
            case 's':
                if (quic_client_config_set_server(config, optarg, config->server_port) != STUB_SUCCESS) {
                    fprintf(stderr, "Error: Invalid server address: %s\n", optarg);
                    return -1;
                }
                break;
                
            case 'p': {
                long port = strtol(optarg, NULL, 10);
                if (port <= 0 || port > 65535) {
                    fprintf(stderr, "Error: Invalid port number: %s\n", optarg);
                    return -1;
                }
                config->server_port = (uint16_t)port;
                break;
            }
            
            case 'l': {
                long level = strtol(optarg, NULL, 10);
                if (level < 0 || level > 4) {
                    fprintf(stderr, "Error: Invalid log level: %s\n", optarg);
                    return -1;
                }
                config->log_level = (log_level_t)level;
                break;
            }
            
            case 'd':
                daemon_mode = 1;
                break;
                
            case 'c':
                fprintf(stderr, "Warning: Configuration file not implemented yet\n");
                break;
                
            case '?':
                /* getopt_long already printed an error message */
                return -1;
                
            default:
                fprintf(stderr, "Error: Unknown option\n");
                return -1;
        }
    }
    
    /* Check for extra arguments */
    if (optind < argc) {
        fprintf(stderr, "Error: Unexpected arguments:");
        for (int i = optind; i < argc; i++) {
            fprintf(stderr, " %s", argv[i]);
        }
        fprintf(stderr, "\n");
        return -1;
    }
    
    /* Handle daemon mode */
    if (daemon_mode) {
        if (daemon(0, 0) < 0) {
            fprintf(stderr, "Error: Failed to daemonize: %s\n", strerror(errno));
            return -1;
        }
    }
    
    return 0;
}

/* Initialize all subsystems */
static int initialize_subsystems(log_level_t log_level)
{
    /* Initialize logger */
    int ret = logger_init(log_level, stdout);
    if (ret != STUB_SUCCESS) {
        fprintf(stderr, "Failed to initialize logger\n");
        return ret;
    }
    
    LOG_INFO("Starting QUIC Stub Client %s", STUB_VERSION);
    
    /* Initialize handlers */
    ret = stream_handler_init();
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to initialize stream handler");
        return ret;
    }
    
    ret = tcp_handler_init();
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to initialize TCP handler");
        return ret;
    }
    
    ret = udp_handler_init();
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to initialize UDP handler");
        return ret;
    }
    
    LOG_INFO("All subsystems initialized successfully");
    return STUB_SUCCESS;
}

/* Cleanup all subsystems */
static void cleanup_subsystems(void)
{
    LOG_INFO("Cleaning up subsystems");
    
    udp_handler_cleanup();
    tcp_handler_cleanup();
    stream_handler_cleanup();
    logger_cleanup();
}

/* Main function */
int main(int argc, char* argv[])
{
    int ret = 0;
    
    /* Initialize default configuration */
    client_config_t config;
    quic_client_config_init_default(&config);
    
    /* Parse command line arguments */
    ret = parse_arguments(argc, argv, &config);
    if (ret != 0) {
        return (ret > 0) ? 0 : 1;  /* 1 means help/version, -1 means error */
    }
    
    /* Initialize subsystems */
    ret = initialize_subsystems(config.log_level);
    if (ret != STUB_SUCCESS) {
        fprintf(stderr, "Failed to initialize subsystems\n");
        return 1;
    }
    
    /* Setup signal handlers */
    ret = setup_signals();
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to setup signal handlers");
        cleanup_subsystems();
        return 1;
    }
    
    /* Initialize QUIC client */
    ret = quic_client_init(&g_client, &config);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to initialize QUIC client");
        cleanup_subsystems();
        return 1;
    }
    
    LOG_INFO("QUIC Stub Client initialized, connecting to %s:%u", 
             config.server_host, config.server_port);
    
    /* Run main loop */
    ret = quic_client_run(&g_client);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("QUIC client run failed: %d", ret);
    }
    
    /* Cleanup */
    LOG_INFO("Shutting down QUIC Stub Client");
    quic_client_cleanup(&g_client);
    cleanup_subsystems();
    
    LOG_INFO("QUIC Stub Client shutdown complete");
    return (ret == STUB_SUCCESS) ? 0 : 1;
}
