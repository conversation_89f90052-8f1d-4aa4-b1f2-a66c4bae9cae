#ifndef LOGGER_H
#define LOGGER_H

#include <stdio.h>
#include <stdarg.h>
#include <time.h>
#include "stub_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Logger context */
typedef struct {
    log_level_t level;
    FILE* output;
    int use_colors;
    int show_timestamp;
} logger_t;

/* Global logger instance */
extern logger_t g_logger;

/* Logger initialization and cleanup */
int logger_init(log_level_t level, FILE* output);
void logger_cleanup(void);
void logger_set_level(log_level_t level);
void logger_enable_colors(int enable);
void logger_enable_timestamp(int enable);

/* Core logging functions */
void logger_log(log_level_t level, const char* file, int line, const char* func, const char* fmt, ...);
void logger_log_hex(log_level_t level, const char* file, int line, const char* func, 
                   const char* prefix, const uint8_t* data, size_t len);

/* Convenience macros */
#define LOG_ERROR(fmt, ...) logger_log(LOG_LEVEL_ERROR, __FILE__, __LINE__, __func__, fmt, ##__VA_ARGS__)
#define LOG_WARN(fmt, ...)  logger_log(LOG_LEVEL_WARN,  __FILE__, __LINE__, __func__, fmt, ##__VA_ARGS__)
#define LOG_INFO(fmt, ...)  logger_log(LOG_LEVEL_INFO,  __FILE__, __LINE__, __func__, fmt, ##__VA_ARGS__)
#define LOG_DEBUG(fmt, ...) logger_log(LOG_LEVEL_DEBUG, __FILE__, __LINE__, __func__, fmt, ##__VA_ARGS__)
#define LOG_TRACE(fmt, ...) logger_log(LOG_LEVEL_TRACE, __FILE__, __LINE__, __func__, fmt, ##__VA_ARGS__)

/* Hex dump macros */
#define LOG_HEX_ERROR(prefix, data, len) logger_log_hex(LOG_LEVEL_ERROR, __FILE__, __LINE__, __func__, prefix, data, len)
#define LOG_HEX_WARN(prefix, data, len)  logger_log_hex(LOG_LEVEL_WARN,  __FILE__, __LINE__, __func__, prefix, data, len)
#define LOG_HEX_INFO(prefix, data, len)  logger_log_hex(LOG_LEVEL_INFO,  __FILE__, __LINE__, __func__, prefix, data, len)
#define LOG_HEX_DEBUG(prefix, data, len) logger_log_hex(LOG_LEVEL_DEBUG, __FILE__, __LINE__, __func__, prefix, data, len)
#define LOG_HEX_TRACE(prefix, data, len) logger_log_hex(LOG_LEVEL_TRACE, __FILE__, __LINE__, __func__, prefix, data, len)

/* Conditional logging for performance */
#define LOG_IF_ERROR(condition, fmt, ...) do { if (condition) LOG_ERROR(fmt, ##__VA_ARGS__); } while(0)
#define LOG_IF_WARN(condition, fmt, ...)  do { if (condition) LOG_WARN(fmt, ##__VA_ARGS__); } while(0)
#define LOG_IF_INFO(condition, fmt, ...)  do { if (condition) LOG_INFO(fmt, ##__VA_ARGS__); } while(0)
#define LOG_IF_DEBUG(condition, fmt, ...) do { if (condition) LOG_DEBUG(fmt, ##__VA_ARGS__); } while(0)
#define LOG_IF_TRACE(condition, fmt, ...) do { if (condition) LOG_TRACE(fmt, ##__VA_ARGS__); } while(0)

#ifdef __cplusplus
}
#endif

#endif /* LOGGER_H */
