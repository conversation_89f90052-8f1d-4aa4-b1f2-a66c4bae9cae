#include "network_utils.h"
#include "logger.h"
#include <string.h>
#include <errno.h>
#include <fcntl.h>
#include <unistd.h>
#include <netdb.h>
#include <ifaddrs.h>
#include <net/if.h>
#include <sys/ioctl.h>

#ifdef __linux__
#include <linux/if_packet.h>
#include <net/ethernet.h>
#elif defined(__APPLE__)
#include <net/if_dl.h>
#include <sys/sysctl.h>
#endif

int parse_address(const char* addr_str, struct sockaddr_storage* addr, socklen_t* addr_len)
{
    if (!addr_str || !addr || !addr_len) {
        return STUB_ERROR_PROTOCOL;
    }
    
    memset(addr, 0, sizeof(*addr));
    
    /* Try IPv4 first */
    struct sockaddr_in* addr4 = (struct sockaddr_in*)addr;
    if (inet_pton(AF_INET, addr_str, &addr4->sin_addr) == 1) {
        addr4->sin_family = AF_INET;
        *addr_len = sizeof(*addr4);
        return STUB_SUCCESS;
    }
    
    /* Try IPv6 */
    struct sockaddr_in6* addr6 = (struct sockaddr_in6*)addr;
    if (inet_pton(AF_INET6, addr_str, &addr6->sin6_addr) == 1) {
        addr6->sin6_family = AF_INET6;
        *addr_len = sizeof(*addr6);
        return STUB_SUCCESS;
    }
    
    LOG_ERROR("Invalid address format: %s", addr_str);
    return STUB_ERROR_PROTOCOL;
}

int format_address(const struct sockaddr_storage* addr, char* buffer, size_t buffer_size)
{
    if (!addr || !buffer || buffer_size == 0) {
        return STUB_ERROR_PROTOCOL;
    }
    
    if (addr->ss_family == AF_INET) {
        const struct sockaddr_in* addr4 = (const struct sockaddr_in*)addr;
        if (inet_ntop(AF_INET, &addr4->sin_addr, buffer, buffer_size)) {
            return STUB_SUCCESS;
        }
    } else if (addr->ss_family == AF_INET6) {
        const struct sockaddr_in6* addr6 = (const struct sockaddr_in6*)addr;
        if (inet_ntop(AF_INET6, &addr6->sin6_addr, buffer, buffer_size)) {
            return STUB_SUCCESS;
        }
    }
    
    LOG_ERROR("Failed to format address, family: %d", addr->ss_family);
    return STUB_ERROR_PROTOCOL;
}

int get_address_family(const struct sockaddr_storage* addr)
{
    return addr ? addr->ss_family : -1;
}

uint16_t get_port_from_address(const struct sockaddr_storage* addr)
{
    if (!addr) return 0;
    
    if (addr->ss_family == AF_INET) {
        return ntohs(((const struct sockaddr_in*)addr)->sin_port);
    } else if (addr->ss_family == AF_INET6) {
        return ntohs(((const struct sockaddr_in6*)addr)->sin6_port);
    }
    
    return 0;
}

void set_port_in_address(struct sockaddr_storage* addr, uint16_t port)
{
    if (!addr) return;
    
    if (addr->ss_family == AF_INET) {
        ((struct sockaddr_in*)addr)->sin_port = htons(port);
    } else if (addr->ss_family == AF_INET6) {
        ((struct sockaddr_in6*)addr)->sin6_port = htons(port);
    }
}

int create_socket(int family, int type, int protocol)
{
    int sockfd = socket(family, type, protocol);
    if (sockfd < 0) {
        LOG_ERROR("Failed to create socket: %s", strerror(errno));
        return STUB_ERROR_NETWORK;
    }
    
    return sockfd;
}

int set_socket_nonblocking(int sockfd)
{
    int flags = fcntl(sockfd, F_GETFL, 0);
    if (flags < 0) {
        LOG_ERROR("Failed to get socket flags: %s", strerror(errno));
        return STUB_ERROR_NETWORK;
    }
    
    if (fcntl(sockfd, F_SETFL, flags | O_NONBLOCK) < 0) {
        LOG_ERROR("Failed to set socket non-blocking: %s", strerror(errno));
        return STUB_ERROR_NETWORK;
    }
    
    return STUB_SUCCESS;
}

int set_socket_reuse_addr(int sockfd)
{
    int reuse = 1;
    if (setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR, &reuse, sizeof(reuse)) < 0) {
        LOG_ERROR("Failed to set SO_REUSEADDR: %s", strerror(errno));
        return STUB_ERROR_NETWORK;
    }
    
    return STUB_SUCCESS;
}

uint16_t read_uint16_be(const uint8_t* data)
{
    return (uint16_t)((data[0] << 8) | data[1]);
}

uint32_t read_uint32_be(const uint8_t* data)
{
    return (uint32_t)((data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3]);
}

void write_uint16_be(uint8_t* data, uint16_t value)
{
    data[0] = (uint8_t)(value >> 8);
    data[1] = (uint8_t)(value & 0xFF);
}

void write_uint32_be(uint8_t* data, uint32_t value)
{
    data[0] = (uint8_t)(value >> 24);
    data[1] = (uint8_t)(value >> 16);
    data[2] = (uint8_t)(value >> 8);
    data[3] = (uint8_t)(value & 0xFF);
}

int is_ipv4_address(const char* addr_str)
{
    struct in_addr addr;
    return inet_pton(AF_INET, addr_str, &addr) == 1;
}

int is_ipv6_address(const char* addr_str)
{
    struct in6_addr addr;
    return inet_pton(AF_INET6, addr_str, &addr) == 1;
}

int is_domain_name(const char* addr_str)
{
    return !is_ipv4_address(addr_str) && !is_ipv6_address(addr_str);
}

int resolve_hostname(const char* hostname, struct sockaddr_storage* addr, socklen_t* addr_len)
{
    struct addrinfo hints, *result;
    
    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_UNSPEC;
    hints.ai_socktype = SOCK_STREAM;
    
    int ret = getaddrinfo(hostname, NULL, &hints, &result);
    if (ret != 0) {
        LOG_ERROR("Failed to resolve hostname %s: %s", hostname, gai_strerror(ret));
        return STUB_ERROR_NETWORK;
    }
    
    memcpy(addr, result->ai_addr, result->ai_addrlen);
    *addr_len = result->ai_addrlen;
    
    freeaddrinfo(result);
    return STUB_SUCCESS;
}
