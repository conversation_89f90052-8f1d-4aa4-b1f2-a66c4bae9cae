#include "logger.h"
#include <string.h>
#include <sys/time.h>

/* Global logger instance */
logger_t g_logger = {0};

/* ANSI color codes */
static const char* level_colors[] = {
    "\033[31m", /* ERROR - Red */
    "\033[33m", /* WARN  - Yellow */
    "\033[32m", /* INFO  - Green */
    "\033[36m", /* DEBUG - Cyan */
    "\033[37m"  /* TRACE - White */
};

static const char* level_names[] = {
    "ERROR",
    "WARN ",
    "INFO ",
    "DEBUG",
    "TRACE"
};

static const char* color_reset = "\033[0m";

int logger_init(log_level_t level, FILE* output)
{
    g_logger.level = level;
    g_logger.output = output ? output : stdout;
    g_logger.use_colors = 1;
    g_logger.show_timestamp = 1;
    
    LOG_INFO("Logger initialized with level %d", level);
    return STUB_SUCCESS;
}

void logger_cleanup(void)
{
    if (g_logger.output && g_logger.output != stdout && g_logger.output != stderr) {
        fclose(g_logger.output);
    }
    memset(&g_logger, 0, sizeof(g_logger));
}

void logger_set_level(log_level_t level)
{
    g_logger.level = level;
}

void logger_enable_colors(int enable)
{
    g_logger.use_colors = enable;
}

void logger_enable_timestamp(int enable)
{
    g_logger.show_timestamp = enable;
}

static void get_timestamp(char* buffer, size_t size)
{
    struct timeval tv;
    struct tm* tm_info;
    
    gettimeofday(&tv, NULL);
    tm_info = localtime(&tv.tv_sec);
    
    snprintf(buffer, size, "%04d/%02d/%02d %02d:%02d:%02d.%03d",
             tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
             tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec,
             (int)(tv.tv_usec / 1000));
}

void logger_log(log_level_t level, const char* file, int line, const char* func, const char* fmt, ...)
{
    if (level > g_logger.level || !g_logger.output) {
        return;
    }
    
    char timestamp[32] = {0};
    const char* filename = strrchr(file, '/');
    filename = filename ? filename + 1 : file;
    
    if (g_logger.show_timestamp) {
        get_timestamp(timestamp, sizeof(timestamp));
    }
    
    /* Print log header */
    if (g_logger.use_colors) {
        fprintf(g_logger.output, "%s[%s]%s ", 
                level_colors[level], level_names[level], color_reset);
    } else {
        fprintf(g_logger.output, "[%s] ", level_names[level]);
    }
    
    if (g_logger.show_timestamp) {
        fprintf(g_logger.output, "%s ", timestamp);
    }
    
    fprintf(g_logger.output, "%s:%d %s() - ", filename, line, func);
    
    /* Print log message */
    va_list args;
    va_start(args, fmt);
    vfprintf(g_logger.output, fmt, args);
    va_end(args);
    
    fprintf(g_logger.output, "\n");
    fflush(g_logger.output);
}

void logger_log_hex(log_level_t level, const char* file, int line, const char* func, 
                   const char* prefix, const uint8_t* data, size_t len)
{
    if (level > g_logger.level || !g_logger.output || !data || len == 0) {
        return;
    }
    
    logger_log(level, file, line, func, "%s (%zu bytes):", prefix, len);
    
    char hex_line[80];
    char ascii_line[17];
    size_t hex_pos, ascii_pos;
    
    for (size_t i = 0; i < len; i++) {
        if (i % 16 == 0) {
            hex_pos = 0;
            ascii_pos = 0;
            hex_pos += snprintf(hex_line + hex_pos, sizeof(hex_line) - hex_pos, "%04zx: ", i);
        }
        
        hex_pos += snprintf(hex_line + hex_pos, sizeof(hex_line) - hex_pos, "%02x ", data[i]);
        ascii_line[ascii_pos++] = (data[i] >= 32 && data[i] <= 126) ? data[i] : '.';
        
        if ((i + 1) % 16 == 0 || i == len - 1) {
            /* Pad hex line if needed */
            while ((i + 1) % 16 != 0 && i != len - 1) {
                hex_pos += snprintf(hex_line + hex_pos, sizeof(hex_line) - hex_pos, "   ");
                i++;
            }
            
            ascii_line[ascii_pos] = '\0';
            fprintf(g_logger.output, "    %s |%s|\n", hex_line, ascii_line);
        }
    }
    
    fflush(g_logger.output);
}
