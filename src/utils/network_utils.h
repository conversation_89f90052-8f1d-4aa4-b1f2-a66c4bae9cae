#ifndef NETWORK_UTILS_H
#define NETWORK_UTILS_H

#include <stdint.h>
#include <stddef.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "stub_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Address parsing and formatting */
int parse_address(const char* addr_str, struct sockaddr_storage* addr, socklen_t* addr_len);
int format_address(const struct sockaddr_storage* addr, char* buffer, size_t buffer_size);
int get_address_family(const struct sockaddr_storage* addr);
uint16_t get_port_from_address(const struct sockaddr_storage* addr);
void set_port_in_address(struct sockaddr_storage* addr, uint16_t port);

/* Socket utilities */
int create_socket(int family, int type, int protocol);
int set_socket_nonblocking(int sockfd);
int set_socket_reuse_addr(int sockfd);
int bind_socket_to_address(int sockfd, const struct sockaddr_storage* addr);
int connect_socket_to_address(int sockfd, const struct sockaddr_storage* addr);

/* Network interface utilities */
int get_local_interfaces(char interfaces[][256], int max_interfaces);
int is_interface_up(const char* interface_name);
int get_interface_mac_address(const char* interface_name, uint8_t mac[6]);

/* Byte order utilities */
uint16_t read_uint16_be(const uint8_t* data);
uint32_t read_uint32_be(const uint8_t* data);
void write_uint16_be(uint8_t* data, uint16_t value);
void write_uint32_be(uint8_t* data, uint32_t value);

/* Address type detection */
int is_ipv4_address(const char* addr_str);
int is_ipv6_address(const char* addr_str);
int is_domain_name(const char* addr_str);

/* DNS resolution */
int resolve_hostname(const char* hostname, struct sockaddr_storage* addr, socklen_t* addr_len);

/* Network error handling */
const char* get_network_error_string(int error_code);
int is_network_error_recoverable(int error_code);

#ifdef __cplusplus
}
#endif

#endif /* NETWORK_UTILS_H */
