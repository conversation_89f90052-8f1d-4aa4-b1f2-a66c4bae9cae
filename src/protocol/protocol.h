#ifndef PROTOCOL_H
#define PROTOCOL_H

#include <stdint.h>
#include <stddef.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include "stub_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Protocol header structure */
typedef struct {
    uint8_t cmd;
    uint8_t atyp;
} protocol_header_t;

/* Address structure */
typedef struct {
    uint8_t atyp;
    union {
        struct {
            uint8_t addr[4];
        } ipv4;
        struct {
            uint8_t len;
            char name[255];
        } domain;
        struct {
            uint8_t addr[16];
        } ipv6;
    } addr;
    uint16_t port;
} protocol_address_t;

/* Parsed protocol message */
typedef struct {
    uint8_t cmd;
    protocol_address_t address;
    const uint8_t* payload;
    size_t payload_len;
} protocol_message_t;

/* Protocol parsing functions */
int protocol_parse_header(const uint8_t* data, size_t data_len, protocol_header_t* header);
int protocol_parse_address(const uint8_t* data, size_t data_len, protocol_address_t* addr, size_t* consumed);
int protocol_parse_message(const uint8_t* data, size_t data_len, protocol_message_t* message);

/* Protocol building functions */
int protocol_build_header(uint8_t cmd, uint8_t atyp, uint8_t* buffer, size_t buffer_size);
int protocol_build_address(const protocol_address_t* addr, uint8_t* buffer, size_t buffer_size, size_t* written);
int protocol_build_message(uint8_t cmd, const protocol_address_t* addr, const uint8_t* payload, 
                          size_t payload_len, uint8_t* buffer, size_t buffer_size, size_t* message_len);

/* Address utilities */
int protocol_address_from_sockaddr(const struct sockaddr_storage* sockaddr, protocol_address_t* addr);
int protocol_address_to_sockaddr(const protocol_address_t* addr, struct sockaddr_storage* sockaddr, socklen_t* socklen);
int protocol_address_from_string(const char* addr_str, uint16_t port, protocol_address_t* addr);
int protocol_address_to_string(const protocol_address_t* addr, char* buffer, size_t buffer_size);
int protocol_address_get_family(const protocol_address_t* addr);

/* Validation functions */
int protocol_validate_cmd(uint8_t cmd);
int protocol_validate_atyp(uint8_t atyp);
int protocol_validate_address(const protocol_address_t* addr);
int protocol_validate_message(const protocol_message_t* message);

/* Utility functions */
size_t protocol_get_address_size(const protocol_address_t* addr);
size_t protocol_get_message_header_size(const protocol_message_t* message);
int protocol_is_tcp_command(uint8_t cmd);
int protocol_is_udp_command(uint8_t cmd);

/* Debug functions */
void protocol_dump_header(const protocol_header_t* header);
void protocol_dump_address(const protocol_address_t* addr);
void protocol_dump_message(const protocol_message_t* message);

#ifdef __cplusplus
}
#endif

#endif /* PROTOCOL_H */
