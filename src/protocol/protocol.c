#include "protocol.h"
#include "logger.h"
#include "network_utils.h"
#include <string.h>
#include <arpa/inet.h>

int protocol_parse_header(const uint8_t* data, size_t data_len, protocol_header_t* header)
{
    if (!data || !header || data_len < 2) {
        return STUB_ERROR_PROTOCOL;
    }
    
    header->cmd = data[0];
    header->atyp = data[1];
    
    if (!protocol_validate_cmd(header->cmd)) {
        LOG_ERROR("Invalid command: 0x%02x", header->cmd);
        return STUB_ERROR_PROTOCOL;
    }
    
    if (!protocol_validate_atyp(header->atyp)) {
        LOG_ERROR("Invalid address type: 0x%02x", header->atyp);
        return STUB_ERROR_PROTOCOL;
    }
    
    LOG_TRACE("Parsed protocol header: cmd=0x%02x, atyp=0x%02x", header->cmd, header->atyp);
    
    return STUB_SUCCESS;
}

int protocol_parse_address(const uint8_t* data, size_t data_len, protocol_address_t* addr, size_t* consumed)
{
    if (!data || !addr || !consumed || data_len < 1) {
        return STUB_ERROR_PROTOCOL;
    }
    
    memset(addr, 0, sizeof(*addr));
    addr->atyp = data[0];
    size_t offset = 1;
    
    switch (addr->atyp) {
        case ATYP_IPV4:
            if (data_len < offset + 4 + 2) {
                LOG_ERROR("Insufficient data for IPv4 address");
                return STUB_ERROR_PROTOCOL;
            }
            memcpy(addr->addr.ipv4.addr, &data[offset], 4);
            offset += 4;
            addr->port = read_uint16_be(&data[offset]);
            offset += 2;
            break;
            
        case ATYP_DOMAIN:
            if (data_len < offset + 1) {
                LOG_ERROR("Insufficient data for domain length");
                return STUB_ERROR_PROTOCOL;
            }
            addr->addr.domain.len = data[offset];
            offset += 1;
            
            if (data_len < offset + addr->addr.domain.len + 2) {
                LOG_ERROR("Insufficient data for domain name");
                return STUB_ERROR_PROTOCOL;
            }
            
            if (addr->addr.domain.len >= sizeof(addr->addr.domain.name)) {
                LOG_ERROR("Domain name too long: %u", addr->addr.domain.len);
                return STUB_ERROR_PROTOCOL;
            }
            
            memcpy(addr->addr.domain.name, &data[offset], addr->addr.domain.len);
            addr->addr.domain.name[addr->addr.domain.len] = '\0';
            offset += addr->addr.domain.len;
            addr->port = read_uint16_be(&data[offset]);
            offset += 2;
            break;
            
        case ATYP_IPV6:
            if (data_len < offset + 16 + 2) {
                LOG_ERROR("Insufficient data for IPv6 address");
                return STUB_ERROR_PROTOCOL;
            }
            memcpy(addr->addr.ipv6.addr, &data[offset], 16);
            offset += 16;
            addr->port = read_uint16_be(&data[offset]);
            offset += 2;
            break;
            
        default:
            LOG_ERROR("Invalid address type: 0x%02x", addr->atyp);
            return STUB_ERROR_PROTOCOL;
    }
    
    *consumed = offset;
    
    LOG_TRACE("Parsed protocol address: atyp=0x%02x, port=%u", addr->atyp, addr->port);
    
    return STUB_SUCCESS;
}

int protocol_parse_message(const uint8_t* data, size_t data_len, protocol_message_t* message)
{
    if (!data || !message || data_len < 2) {
        return STUB_ERROR_PROTOCOL;
    }
    
    memset(message, 0, sizeof(*message));
    
    /* Parse command */
    message->cmd = data[0];
    if (!protocol_validate_cmd(message->cmd)) {
        LOG_ERROR("Invalid command in message: 0x%02x", message->cmd);
        return STUB_ERROR_PROTOCOL;
    }
    
    /* Parse address */
    size_t addr_consumed;
    int ret = protocol_parse_address(&data[1], data_len - 1, &message->address, &addr_consumed);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to parse address in message");
        return ret;
    }
    
    /* Set payload */
    size_t header_size = 1 + addr_consumed;
    if (data_len > header_size) {
        message->payload = &data[header_size];
        message->payload_len = data_len - header_size;
    }
    
    LOG_DEBUG("Parsed protocol message: cmd=0x%02x, payload_len=%zu", 
             message->cmd, message->payload_len);
    
    return STUB_SUCCESS;
}

int protocol_build_address(const protocol_address_t* addr, uint8_t* buffer, size_t buffer_size, size_t* written)
{
    if (!addr || !buffer || !written) {
        return STUB_ERROR_PROTOCOL;
    }
    
    size_t offset = 0;
    
    if (buffer_size < 1) {
        return STUB_ERROR_MEMORY;
    }
    
    buffer[offset++] = addr->atyp;
    
    switch (addr->atyp) {
        case ATYP_IPV4:
            if (buffer_size < offset + 4 + 2) {
                return STUB_ERROR_MEMORY;
            }
            memcpy(&buffer[offset], addr->addr.ipv4.addr, 4);
            offset += 4;
            write_uint16_be(&buffer[offset], addr->port);
            offset += 2;
            break;
            
        case ATYP_DOMAIN:
            if (buffer_size < offset + 1 + addr->addr.domain.len + 2) {
                return STUB_ERROR_MEMORY;
            }
            buffer[offset++] = addr->addr.domain.len;
            memcpy(&buffer[offset], addr->addr.domain.name, addr->addr.domain.len);
            offset += addr->addr.domain.len;
            write_uint16_be(&buffer[offset], addr->port);
            offset += 2;
            break;
            
        case ATYP_IPV6:
            if (buffer_size < offset + 16 + 2) {
                return STUB_ERROR_MEMORY;
            }
            memcpy(&buffer[offset], addr->addr.ipv6.addr, 16);
            offset += 16;
            write_uint16_be(&buffer[offset], addr->port);
            offset += 2;
            break;
            
        default:
            LOG_ERROR("Invalid address type for building: 0x%02x", addr->atyp);
            return STUB_ERROR_PROTOCOL;
    }
    
    *written = offset;
    
    LOG_TRACE("Built protocol address: atyp=0x%02x, size=%zu", addr->atyp, offset);
    
    return STUB_SUCCESS;
}

int protocol_build_message(uint8_t cmd, const protocol_address_t* addr, const uint8_t* payload, 
                          size_t payload_len, uint8_t* buffer, size_t buffer_size, size_t* message_len)
{
    if (!addr || !buffer || !message_len) {
        return STUB_ERROR_PROTOCOL;
    }
    
    if (buffer_size < 1) {
        return STUB_ERROR_MEMORY;
    }
    
    /* Write command */
    buffer[0] = cmd;
    size_t offset = 1;
    
    /* Write address */
    size_t addr_size;
    int ret = protocol_build_address(addr, &buffer[offset], buffer_size - offset, &addr_size);
    if (ret != STUB_SUCCESS) {
        return ret;
    }
    offset += addr_size;
    
    /* Write payload */
    if (payload && payload_len > 0) {
        if (buffer_size < offset + payload_len) {
            return STUB_ERROR_MEMORY;
        }
        memcpy(&buffer[offset], payload, payload_len);
        offset += payload_len;
    }
    
    *message_len = offset;
    
    LOG_DEBUG("Built protocol message: cmd=0x%02x, total_size=%zu", cmd, offset);
    
    return STUB_SUCCESS;
}

int protocol_address_to_sockaddr(const protocol_address_t* addr, struct sockaddr_storage* sockaddr, socklen_t* socklen)
{
    if (!addr || !sockaddr || !socklen) {
        return STUB_ERROR_PROTOCOL;
    }
    
    memset(sockaddr, 0, sizeof(*sockaddr));
    
    switch (addr->atyp) {
        case ATYP_IPV4: {
            struct sockaddr_in* addr4 = (struct sockaddr_in*)sockaddr;
            addr4->sin_family = AF_INET;
            memcpy(&addr4->sin_addr, addr->addr.ipv4.addr, 4);
            addr4->sin_port = htons(addr->port);
            *socklen = sizeof(*addr4);
            break;
        }
        
        case ATYP_IPV6: {
            struct sockaddr_in6* addr6 = (struct sockaddr_in6*)sockaddr;
            addr6->sin6_family = AF_INET6;
            memcpy(&addr6->sin6_addr, addr->addr.ipv6.addr, 16);
            addr6->sin6_port = htons(addr->port);
            *socklen = sizeof(*addr6);
            break;
        }
        
        case ATYP_DOMAIN: {
            /* Need to resolve domain name first */
            int ret = resolve_hostname(addr->addr.domain.name, sockaddr, socklen);
            if (ret != STUB_SUCCESS) {
                return ret;
            }
            set_port_in_address(sockaddr, addr->port);
            break;
        }
        
        default:
            LOG_ERROR("Unsupported address type for sockaddr conversion: 0x%02x", addr->atyp);
            return STUB_ERROR_PROTOCOL;
    }
    
    return STUB_SUCCESS;
}

int protocol_validate_cmd(uint8_t cmd)
{
    return cmd == CMD_TCP_CONNECT || cmd == CMD_UDP_RELAY;
}

int protocol_validate_atyp(uint8_t atyp)
{
    return atyp == ATYP_IPV4 || atyp == ATYP_DOMAIN || atyp == ATYP_IPV6;
}

int protocol_is_tcp_command(uint8_t cmd)
{
    return cmd == CMD_TCP_CONNECT;
}

int protocol_is_udp_command(uint8_t cmd)
{
    return cmd == CMD_UDP_RELAY;
}
