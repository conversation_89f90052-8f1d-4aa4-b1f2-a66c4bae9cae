#ifndef QUIC_CLIENT_H
#define QUIC_CLIENT_H

#include <stdint.h>
#include <stddef.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include "stub_config.h"
#include "device_register.h"

#if HAVE_PICOQUIC
#include <picoquic.h>
#else
/* Stub definitions for picoquic types when not available */
typedef struct st_picoquic_quic_t picoquic_quic_t;
typedef struct st_picoquic_cnx_t picoquic_cnx_t;
typedef enum {
    picoquic_callback_stream_data = 0,
    picoquic_callback_stream_fin,
    picoquic_callback_stream_reset,
    picoquic_callback_stop_sending,
    picoquic_callback_stateless_reset,
    picoquic_callback_close,
    picoquic_callback_application_close,
    picoquic_callback_stream_gap,
    picoquic_callback_prepare_to_send,
    picoquic_callback_almost_ready,
    picoquic_callback_ready
} picoquic_call_back_event_t;
#define PICOQUIC_ERROR_UNEXPECTED_ERROR -1
#define PICOQUIC_ERROR_MEMORY -2
#define PICOQUIC_NO_ERROR_TERMINATE_PACKET_LOOP 1
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* Client state enumeration */
typedef enum {
    CLIENT_STATE_DISCONNECTED = 0,
    CLIENT_STATE_CONNECTING,
    CLIENT_STATE_REGISTERING,
    CLIENT_STATE_CONNECTED,
    CLIENT_STATE_ERROR
} client_state_t;

/* Client configuration */
typedef struct {
    char server_host[MAX_HOSTNAME_LENGTH];
    uint16_t server_port;
    char alpn[32];
    int max_reconnect_attempts;
    uint32_t reconnect_base_delay_ms;
    uint32_t reconnect_max_delay_ms;
    uint32_t connection_timeout_ms;
    log_level_t log_level;
} client_config_t;

/* Client context */
typedef struct {
    /* Configuration */
    client_config_t config;
    
    /* QUIC context */
    picoquic_quic_t* quic;
    picoquic_cnx_t* cnx;
    
    /* Network */
    int sockfd;
    struct sockaddr_storage server_addr;
    socklen_t server_addr_len;
    
    /* State management */
    client_state_t state;
    int reconnect_attempts;
    uint64_t last_connect_time;
    uint64_t next_reconnect_time;
    
    /* Device registration */
    device_register_ctx_t registration;
    
    /* Statistics */
    uint64_t bytes_sent;
    uint64_t bytes_received;
    uint64_t packets_sent;
    uint64_t packets_received;
    
    /* Control flags */
    int should_stop;
    int is_registered;
} quic_client_t;

/* Client lifecycle functions */
int quic_client_init(quic_client_t* client, const client_config_t* config);
int quic_client_connect(quic_client_t* client);
int quic_client_disconnect(quic_client_t* client);
void quic_client_cleanup(quic_client_t* client);

/* Client operation functions */
int quic_client_run(quic_client_t* client);
int quic_client_process_events(quic_client_t* client, uint64_t current_time);
int quic_client_handle_packet(quic_client_t* client, const uint8_t* packet, size_t packet_len, 
                             const struct sockaddr_storage* from_addr);

/* Connection management */
int quic_client_should_reconnect(const quic_client_t* client);
int quic_client_start_reconnect(quic_client_t* client);
uint64_t quic_client_get_reconnect_delay(const quic_client_t* client);

/* State management */
client_state_t quic_client_get_state(const quic_client_t* client);
const char* quic_client_get_state_string(client_state_t state);
int quic_client_is_connected(const quic_client_t* client);
int quic_client_is_ready(const quic_client_t* client);

/* Statistics */
void quic_client_get_stats(const quic_client_t* client, uint64_t* bytes_sent, uint64_t* bytes_received,
                          uint64_t* packets_sent, uint64_t* packets_received);
void quic_client_print_stats(const quic_client_t* client);

/* Configuration helpers */
void quic_client_config_init_default(client_config_t* config);
int quic_client_config_set_server(client_config_t* config, const char* host, uint16_t port);
int quic_client_config_validate(const client_config_t* config);

/* Callback function for QUIC events */
int quic_client_callback(picoquic_cnx_t* cnx, uint64_t stream_id, uint8_t* bytes, size_t length,
                        picoquic_call_back_event_t fin_or_event, void* callback_ctx, void* stream_ctx);

#ifdef __cplusplus
}
#endif

#endif /* QUIC_CLIENT_H */
