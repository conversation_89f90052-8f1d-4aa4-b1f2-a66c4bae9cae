#include "quic_client.h"
#include "logger.h"
#include "network_utils.h"
#include "stream_handler.h"
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <sys/time.h>

#if HAVE_PICOQUIC
#include <picosocks.h>
#include <picoquic_utils.h>
#endif

static uint64_t get_current_time_ms(void)
{
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

void quic_client_config_init_default(client_config_t* config)
{
    if (!config) return;
    
    memset(config, 0, sizeof(*config));
    strncpy(config->server_host, DEFAULT_SERVER_HOST, sizeof(config->server_host) - 1);
    config->server_port = DEFAULT_SERVER_PORT;
    strncpy(config->alpn, STUB_ALPN, sizeof(config->alpn) - 1);
    config->max_reconnect_attempts = MAX_RECONNECT_ATTEMPTS;
    config->reconnect_base_delay_ms = RECONNECT_BASE_DELAY_MS;
    config->reconnect_max_delay_ms = RECONNECT_MAX_DELAY_MS;
    config->connection_timeout_ms = CONNECTION_TIMEOUT_MS;
    config->log_level = DEFAULT_LOG_LEVEL;
}

int quic_client_config_set_server(client_config_t* config, const char* host, uint16_t port)
{
    if (!config || !host) {
        return STUB_ERROR_PROTOCOL;
    }
    
    if (strlen(host) >= sizeof(config->server_host)) {
        LOG_ERROR("Server hostname too long: %s", host);
        return STUB_ERROR_PROTOCOL;
    }
    
    strncpy(config->server_host, host, sizeof(config->server_host) - 1);
    config->server_port = port;
    
    return STUB_SUCCESS;
}

int quic_client_config_validate(const client_config_t* config)
{
    if (!config) {
        return STUB_ERROR_PROTOCOL;
    }
    
    if (strlen(config->server_host) == 0) {
        LOG_ERROR("Server host not specified");
        return STUB_ERROR_PROTOCOL;
    }
    
    if (config->server_port == 0) {
        LOG_ERROR("Server port not specified");
        return STUB_ERROR_PROTOCOL;
    }
    
    if (strlen(config->alpn) == 0) {
        LOG_ERROR("ALPN not specified");
        return STUB_ERROR_PROTOCOL;
    }
    
    return STUB_SUCCESS;
}

int quic_client_init(quic_client_t* client, const client_config_t* config)
{
    if (!client || !config) {
        return STUB_ERROR_PROTOCOL;
    }
    
    int ret = quic_client_config_validate(config);
    if (ret != STUB_SUCCESS) {
        return ret;
    }
    
    memset(client, 0, sizeof(*client));
    memcpy(&client->config, config, sizeof(*config));
    
    client->state = CLIENT_STATE_DISCONNECTED;
    client->sockfd = -1;
    
    /* Resolve server address - try hostname resolution first for domains */
    if (is_ipv4_address(config->server_host) || is_ipv6_address(config->server_host)) {
        /* Direct IP address */
        ret = parse_address(config->server_host, &client->server_addr, &client->server_addr_len);
        if (ret != STUB_SUCCESS) {
            LOG_ERROR("Failed to parse server IP address: %s", config->server_host);
            return ret;
        }
    } else {
        /* Domain name - resolve it */
        ret = resolve_hostname(config->server_host, &client->server_addr, &client->server_addr_len);
        if (ret != STUB_SUCCESS) {
            LOG_ERROR("Failed to resolve server hostname: %s", config->server_host);
            return ret;
        }
    }
    
    set_port_in_address(&client->server_addr, config->server_port);
    
    char addr_str[256];
    format_address(&client->server_addr, addr_str, sizeof(addr_str));
    LOG_INFO("QUIC client initialized, server: %s:%u", addr_str, config->server_port);
    
    return STUB_SUCCESS;
}

int quic_client_connect(quic_client_t* client)
{
    if (!client) {
        return STUB_ERROR_PROTOCOL;
    }
    
    if (client->state != CLIENT_STATE_DISCONNECTED) {
        LOG_WARN("Client already connecting or connected");
        return STUB_SUCCESS;
    }
    
    LOG_INFO("Starting QUIC connection to server");
    client->state = CLIENT_STATE_CONNECTING;
    client->last_connect_time = get_current_time_ms();
    
    /* Create QUIC context */
    uint64_t current_time = picoquic_current_time();
    client->quic = picoquic_create(
        8,                              /* max_nb_connections */
        NULL,                           /* cert_file_name */
        NULL,                           /* key_file_name */
        NULL,                           /* cert_root_file_name */
        client->config.alpn,            /* default_alpn */
        quic_client_callback,           /* default_callback_fn */
        client,                         /* default_callback_ctx */
        NULL,                           /* cnx_id_callback */
        NULL,                           /* cnx_id_callback_data */
        NULL,                           /* reset_seed */
        current_time,                   /* current_time */
        NULL,                           /* p_simulated_time */
        NULL,                           /* ticket_file_name */
        NULL,                           /* ticket_encryption_key */
        0                               /* ticket_encryption_key_length */
    );
    
    if (!client->quic) {
        LOG_ERROR("Failed to create QUIC context");
        LOG_DEBUG("QUIC create parameters: max_connections=8, alpn=%s", client->config.alpn);
        client->state = CLIENT_STATE_ERROR;
        return STUB_ERROR_NETWORK;
    }

    LOG_DEBUG("QUIC context created successfully");
    
    /* Create socket */
    int family = get_address_family(&client->server_addr);
    client->sockfd = create_socket(family, SOCK_DGRAM, 0);
    if (client->sockfd < 0) {
        LOG_ERROR("Failed to create UDP socket");
        client->state = CLIENT_STATE_ERROR;
        return STUB_ERROR_NETWORK;
    }
    
    /* Set socket non-blocking */
    int ret = set_socket_nonblocking(client->sockfd);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to set socket non-blocking");
        client->state = CLIENT_STATE_ERROR;
        return ret;
    }
    
    /* Create QUIC connection */
    char addr_str[256];
    format_address(&client->server_addr, addr_str, sizeof(addr_str));

    client->cnx = picoquic_create_client_cnx(client->quic,
                                           (struct sockaddr*)&client->server_addr,
                                           current_time,
                                           0,                    /* preferred_version */
                                           addr_str,             /* sni */
                                           client->config.alpn,  /* alpn */
                                           quic_client_callback, /* callback_fn */
                                           client);              /* callback_ctx */
    
    if (!client->cnx) {
        LOG_ERROR("Failed to create QUIC connection (TLS initialization may have failed)");
        client->state = CLIENT_STATE_ERROR;
        return STUB_ERROR_NETWORK;
    }

    /* Connection is already started by picoquic_create_client_cnx */
    LOG_DEBUG("QUIC connection created and started successfully");
    LOG_DEBUG("Connection state: %d", picoquic_get_cnx_state(client->cnx));
    
    /* Initialize device registration */
    ret = device_register_init(&client->registration, client->cnx);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Failed to initialize device registration");
        client->state = CLIENT_STATE_ERROR;
        return ret;
    }
    
    LOG_INFO("QUIC connection initiated to %s:%u", addr_str, client->config.server_port);
    
    return STUB_SUCCESS;
}

client_state_t quic_client_get_state(const quic_client_t* client)
{
    return client ? client->state : CLIENT_STATE_ERROR;
}

const char* quic_client_get_state_string(client_state_t state)
{
    switch (state) {
        case CLIENT_STATE_DISCONNECTED: return "Disconnected";
        case CLIENT_STATE_CONNECTING: return "Connecting";
        case CLIENT_STATE_REGISTERING: return "Registering";
        case CLIENT_STATE_CONNECTED: return "Connected";
        case CLIENT_STATE_ERROR: return "Error";
        default: return "Unknown";
    }
}

int quic_client_is_connected(const quic_client_t* client)
{
    return client && client->state == CLIENT_STATE_CONNECTED;
}

int quic_client_is_ready(const quic_client_t* client)
{
    return client && client->state == CLIENT_STATE_CONNECTED && client->is_registered;
}

void quic_client_get_stats(const quic_client_t* client, uint64_t* bytes_sent, uint64_t* bytes_received,
                          uint64_t* packets_sent, uint64_t* packets_received)
{
    if (!client) return;
    
    if (bytes_sent) *bytes_sent = client->bytes_sent;
    if (bytes_received) *bytes_received = client->bytes_received;
    if (packets_sent) *packets_sent = client->packets_sent;
    if (packets_received) *packets_received = client->packets_received;
}

void quic_client_print_stats(const quic_client_t* client)
{
    if (!client) return;

    LOG_INFO("Client statistics:");
    LOG_INFO("  State: %s", quic_client_get_state_string(client->state));
    LOG_INFO("  Bytes sent: %llu", (unsigned long long)client->bytes_sent);
    LOG_INFO("  Bytes received: %llu", (unsigned long long)client->bytes_received);
    LOG_INFO("  Packets sent: %llu", (unsigned long long)client->packets_sent);
    LOG_INFO("  Packets received: %llu", (unsigned long long)client->packets_received);
    LOG_INFO("  Reconnect attempts: %d", client->reconnect_attempts);
}

int quic_client_disconnect(quic_client_t* client)
{
    if (!client) {
        return STUB_ERROR_PROTOCOL;
    }

    LOG_INFO("Disconnecting QUIC client");

    if (client->cnx) {
        picoquic_close(client->cnx, 0);
        client->cnx = NULL;
    }

    if (client->quic) {
        picoquic_free(client->quic);
        client->quic = NULL;
    }

    if (client->sockfd >= 0) {
        close(client->sockfd);
        client->sockfd = -1;
    }

    device_register_cleanup(&client->registration);

    client->state = CLIENT_STATE_DISCONNECTED;
    client->is_registered = 0;

    return STUB_SUCCESS;
}

void quic_client_cleanup(quic_client_t* client)
{
    if (!client) return;

    quic_client_disconnect(client);
    memset(client, 0, sizeof(*client));
    client->sockfd = -1;
}

int quic_client_should_reconnect(const quic_client_t* client)
{
    if (!client) return 0;

    return (client->state == CLIENT_STATE_ERROR || client->state == CLIENT_STATE_DISCONNECTED) &&
           client->reconnect_attempts < client->config.max_reconnect_attempts &&
           !client->should_stop;
}

uint64_t quic_client_get_reconnect_delay(const quic_client_t* client)
{
    if (!client) return 0;

    uint64_t delay = client->config.reconnect_base_delay_ms;

    /* Exponential backoff */
    for (int i = 0; i < client->reconnect_attempts && delay < client->config.reconnect_max_delay_ms / 2; i++) {
        delay *= 2;
    }

    if (delay > client->config.reconnect_max_delay_ms) {
        delay = client->config.reconnect_max_delay_ms;
    }

    return delay;
}

int quic_client_start_reconnect(quic_client_t* client)
{
    if (!client) {
        return STUB_ERROR_PROTOCOL;
    }

    if (!quic_client_should_reconnect(client)) {
        return STUB_ERROR_PROTOCOL;
    }

    uint64_t delay = quic_client_get_reconnect_delay(client);
    client->next_reconnect_time = get_current_time_ms() + delay;
    client->reconnect_attempts++;

    LOG_INFO("Scheduling reconnect attempt %d in %llu ms",
             client->reconnect_attempts, (unsigned long long)delay);

    /* Clean up current connection */
    quic_client_disconnect(client);

    return STUB_SUCCESS;
}

int quic_client_handle_packet(quic_client_t* client, const uint8_t* packet, size_t packet_len,
                             const struct sockaddr_storage* from_addr)
{
    if (!client || !packet || packet_len == 0) {
        return STUB_ERROR_PROTOCOL;
    }

    if (!client->quic) {
        LOG_WARN("Received packet but QUIC context not initialized");
        return STUB_ERROR_PROTOCOL;
    }

    uint64_t current_time = picoquic_current_time();

    int ret = picoquic_incoming_packet(client->quic, (uint8_t*)packet, packet_len,
                                      (struct sockaddr*)from_addr,
                                      (struct sockaddr*)&client->server_addr,
                                      0, 0, current_time);

    if (ret != 0) {
        LOG_DEBUG("picoquic_incoming_packet returned %d", ret);
        return STUB_ERROR_NETWORK;
    }

    client->packets_received++;
    client->bytes_received += packet_len;

    return STUB_SUCCESS;
}

int quic_client_process_events(quic_client_t* client, uint64_t current_time)
{
    if (!client) {
        return STUB_ERROR_PROTOCOL;
    }

    /* Check for reconnect timeout */
    if (client->state == CLIENT_STATE_DISCONNECTED &&
        client->next_reconnect_time > 0 &&
        get_current_time_ms() >= client->next_reconnect_time) {

        client->next_reconnect_time = 0;
        LOG_INFO("Reconnect timeout reached, attempting to connect");
        return quic_client_connect(client);
    }

    if (!client->quic || !client->cnx) {
        return STUB_SUCCESS;
    }

    /* Check connection state */
    picoquic_state_enum cnx_state = picoquic_get_cnx_state(client->cnx);

    switch (cnx_state) {
        case picoquic_state_ready:
            if (client->state == CLIENT_STATE_CONNECTING) {
                LOG_INFO("QUIC connection established, starting device registration");
                client->state = CLIENT_STATE_REGISTERING;

                int ret = device_register_start(&client->registration);
                if (ret != STUB_SUCCESS) {
                    LOG_ERROR("Failed to start device registration");
                    client->state = CLIENT_STATE_ERROR;
                    return ret;
                }
            } else if (client->state == CLIENT_STATE_REGISTERING &&
                      device_register_is_complete(&client->registration)) {

                if (device_register_is_successful(&client->registration)) {
                    LOG_INFO("Device registration successful, client ready");
                    client->state = CLIENT_STATE_CONNECTED;
                    client->is_registered = 1;
                    client->reconnect_attempts = 0;  /* Reset on successful connection */
                } else {
                    LOG_ERROR("Device registration failed");
                    client->state = CLIENT_STATE_ERROR;
                    return STUB_ERROR_REGISTRATION;
                }
            }
            break;

        case picoquic_state_disconnected:
        case picoquic_state_handshake_failure:
        case picoquic_state_closing:
        case picoquic_state_draining:
            if (client->state != CLIENT_STATE_DISCONNECTED) {
                LOG_WARN("QUIC connection lost, state: %d", cnx_state);
                client->state = CLIENT_STATE_ERROR;
                return quic_client_start_reconnect(client);
            }
            break;

        default:
            /* Connection in progress */
            break;
    }

    return STUB_SUCCESS;
}

int quic_client_callback(picoquic_cnx_t* cnx, uint64_t stream_id, uint8_t* bytes, size_t length,
                        picoquic_call_back_event_t fin_or_event, void* callback_ctx, void* stream_ctx)
{
    quic_client_t* client = (quic_client_t*)callback_ctx;

    if (!client) {
        LOG_ERROR("QUIC callback called with null client context");
        return PICOQUIC_ERROR_UNEXPECTED_ERROR;
    }

    switch (fin_or_event) {
        case picoquic_callback_stream_data:
        case picoquic_callback_stream_fin:
            /* Handle incoming stream data */
            if (client->state == CLIENT_STATE_REGISTERING &&
                stream_id == client->registration.stream_id) {
                /* This is registration response data */
                int ret = device_register_handle_response(&client->registration, bytes, length);
                if (ret != STUB_SUCCESS) {
                    LOG_ERROR("Failed to handle registration response");
                    return PICOQUIC_ERROR_UNEXPECTED_ERROR;
                }
            } else if (client->state == CLIENT_STATE_CONNECTED) {
                /* This is normal stream data, delegate to stream handler */
                return stream_handler_process_data(cnx, stream_id, bytes, length,
                                                 fin_or_event, client, stream_ctx);
            }
            break;

        case picoquic_callback_stream_reset:
            LOG_WARN("Stream %llu reset", (unsigned long long)stream_id);
            if (client->state == CLIENT_STATE_REGISTERING &&
                stream_id == client->registration.stream_id) {
                LOG_ERROR("Registration stream reset");
                client->state = CLIENT_STATE_ERROR;
                return PICOQUIC_ERROR_UNEXPECTED_ERROR;
            }
            break;

        case picoquic_callback_ready:
            LOG_INFO("QUIC connection ready");
            break;

        case picoquic_callback_almost_ready:
            LOG_DEBUG("QUIC connection almost ready");
            break;

        case picoquic_callback_close:
        case picoquic_callback_application_close:
            LOG_INFO("QUIC connection closed, reason: %llu", (unsigned long long)fin_or_event);
            if (cnx) {
                uint64_t local_error = picoquic_get_local_error(cnx);
                uint64_t remote_error = picoquic_get_remote_error(cnx);
                LOG_INFO("Local error: %llu, Remote error: %llu",
                         (unsigned long long)local_error, (unsigned long long)remote_error);
            }
            client->state = CLIENT_STATE_ERROR;
            break;

        case picoquic_callback_stateless_reset:
            LOG_WARN("QUIC stateless reset received");
            client->state = CLIENT_STATE_ERROR;
            break;

        case picoquic_callback_prepare_to_send:
            /* Handle outgoing stream data preparation */
            if (client->state == CLIENT_STATE_CONNECTED) {
                return stream_handler_prepare_data(cnx, stream_id, bytes, length,
                                                  client, stream_ctx);
            }
            break;

        default:
            LOG_TRACE("Unhandled QUIC callback event: %d", fin_or_event);
            break;
    }

    return 0;
}

int quic_client_run(quic_client_t* client)
{
    if (!client) {
        return STUB_ERROR_PROTOCOL;
    }

    LOG_INFO("Starting QUIC client main loop");

    /* Initial connection attempt */
    int ret = quic_client_connect(client);
    if (ret != STUB_SUCCESS) {
        LOG_ERROR("Initial connection failed");
        return ret;
    }

    uint8_t packet_buffer[MAX_PACKET_SIZE];
    struct sockaddr_storage from_addr;
    socklen_t from_addr_len;

    while (!client->should_stop) {
        uint64_t current_time = get_current_time_ms();

        /* Process QUIC events and state changes */
        ret = quic_client_process_events(client, current_time);
        if (ret != STUB_SUCCESS && ret != STUB_ERROR_REGISTRATION) {
            LOG_ERROR("Error processing events: %d", ret);

            if (quic_client_should_reconnect(client)) {
                quic_client_start_reconnect(client);
                continue;
            } else {
                break;
            }
        }

        /* Receive packets */
        if (client->sockfd >= 0) {
            from_addr_len = sizeof(from_addr);
            ssize_t received = recvfrom(client->sockfd, packet_buffer, sizeof(packet_buffer),
                                       MSG_DONTWAIT, (struct sockaddr*)&from_addr, &from_addr_len);

            if (received > 0) {
                ret = quic_client_handle_packet(client, packet_buffer, received, &from_addr);
                if (ret != STUB_SUCCESS) {
                    LOG_DEBUG("Error handling packet: %d", ret);
                }
            } else if (received < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                LOG_ERROR("Socket receive error: %s", strerror(errno));
                client->state = CLIENT_STATE_ERROR;

                if (quic_client_should_reconnect(client)) {
                    quic_client_start_reconnect(client);
                    continue;
                } else {
                    break;
                }
            }
        }

        /* Send packets */
        if (client->cnx && client->sockfd >= 0) {
            struct sockaddr_storage peer_addr;
            struct sockaddr_storage local_addr;
            int if_index = 0;

            size_t send_length = 0;
            ret = picoquic_prepare_packet(client->cnx, picoquic_current_time(),
                                        packet_buffer, sizeof(packet_buffer), &send_length,
                                        &peer_addr, &local_addr, &if_index);

            if (ret == 0 && send_length > 0) {
                ssize_t sent = sendto(client->sockfd, packet_buffer, send_length, 0,
                                    (struct sockaddr*)&peer_addr,
                                    peer_addr.ss_family == AF_INET ? sizeof(struct sockaddr_in) : sizeof(struct sockaddr_in6));

                if (sent > 0) {
                    client->packets_sent++;
                    client->bytes_sent += sent;
                } else if (sent < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                    LOG_ERROR("Socket send error: %s", strerror(errno));
                }
            }
        }

        /* Small delay to prevent busy waiting */
        usleep(1000);  /* 1ms */
    }

    LOG_INFO("QUIC client main loop exited");
    quic_client_print_stats(client);

    return STUB_SUCCESS;
}
