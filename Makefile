# Makefile for QUIC Stub Client
# Provides convenient targets for building, testing, and deployment

# Project configuration
PROJECT_NAME = stub
VERSION = 1.0.0
BUILD_DIR = build
INSTALL_PREFIX = /usr/local

# Compiler and build configuration
CMAKE = cmake
MAKE = make
CC = gcc
STRIP = strip

# Default target
.PHONY: all
all: build

# Help target
.PHONY: help
help:
	@echo "QUIC Stub Client Build System"
	@echo "============================="
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Build the project (default)"
	@echo "  build        - Build the project"
	@echo "  clean        - Clean build artifacts"
	@echo "  rebuild      - Clean and build"
	@echo "  install      - Install to system"
	@echo "  uninstall    - Remove from system"
	@echo "  test         - Run basic tests"
	@echo "  package      - Create distribution package"
	@echo "  cross-arm    - Cross-compile for ARM"
	@echo "  cross-mips   - Cross-compile for MIPS"
	@echo "  size         - Show binary size information"
	@echo "  strip        - Strip debug symbols"
	@echo "  help         - Show this help"
	@echo ""
	@echo "Build types:"
	@echo "  make BUILD_TYPE=Debug    - Debug build"
	@echo "  make BUILD_TYPE=Release  - Release build (default)"
	@echo ""

# Build target
.PHONY: build
build: $(BUILD_DIR)/$(PROJECT_NAME)

$(BUILD_DIR)/$(PROJECT_NAME): $(BUILD_DIR)/Makefile
	@echo "Building $(PROJECT_NAME)..."
	$(MAKE) -C $(BUILD_DIR)
	@echo "Build completed: $(BUILD_DIR)/$(PROJECT_NAME)"

$(BUILD_DIR)/Makefile:
	@echo "Configuring build..."
	@mkdir -p $(BUILD_DIR)
	cd $(BUILD_DIR) && $(CMAKE) -DCMAKE_BUILD_TYPE=$(BUILD_TYPE) ..

# Build type configuration
BUILD_TYPE ?= Release

# Clean target
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR)
	@echo "Clean completed"

# Rebuild target
.PHONY: rebuild
rebuild: clean build

# Install target
.PHONY: install
install: build
	@echo "Installing $(PROJECT_NAME) to $(INSTALL_PREFIX)..."
	$(MAKE) -C $(BUILD_DIR) install
	@echo "Installation completed"

# Uninstall target
.PHONY: uninstall
uninstall:
	@echo "Uninstalling $(PROJECT_NAME)..."
	@rm -f $(INSTALL_PREFIX)/bin/$(PROJECT_NAME)
	@echo "Uninstall completed"

# Test target
.PHONY: test
test: build
	@echo "Running basic tests..."
	@chmod +x scripts/test_basic.sh
	@scripts/test_basic.sh

# Package target
.PHONY: package
package: build strip
	@echo "Creating distribution package..."
	@mkdir -p dist
	@cp $(BUILD_DIR)/$(PROJECT_NAME) dist/
	@cp README.md dist/
	@cp docs/protocol.md dist/ 2>/dev/null || true
	@tar -czf dist/$(PROJECT_NAME)-$(VERSION)-$(shell uname -m).tar.gz -C dist $(PROJECT_NAME) README.md protocol.md 2>/dev/null || \
	 tar -czf dist/$(PROJECT_NAME)-$(VERSION)-$(shell uname -m).tar.gz -C dist $(PROJECT_NAME) README.md
	@echo "Package created: dist/$(PROJECT_NAME)-$(VERSION)-$(shell uname -m).tar.gz"

# Cross-compilation targets
.PHONY: cross-arm
cross-arm:
	@echo "Cross-compiling for ARM..."
	@mkdir -p $(BUILD_DIR)-arm
	cd $(BUILD_DIR)-arm && $(CMAKE) -DCMAKE_TOOLCHAIN_FILE=../toolchains/arm-linux-gnueabihf.cmake \
		-DCMAKE_BUILD_TYPE=Release ..
	$(MAKE) -C $(BUILD_DIR)-arm
	@echo "ARM build completed: $(BUILD_DIR)-arm/$(PROJECT_NAME)"

.PHONY: cross-mips
cross-mips:
	@echo "Cross-compiling for MIPS..."
	@mkdir -p $(BUILD_DIR)-mips
	cd $(BUILD_DIR)-mips && $(CMAKE) -DCMAKE_TOOLCHAIN_FILE=../toolchains/mipsel-linux-gnu.cmake \
		-DCMAKE_BUILD_TYPE=Release ..
	$(MAKE) -C $(BUILD_DIR)-mips
	@echo "MIPS build completed: $(BUILD_DIR)-mips/$(PROJECT_NAME)"

# Size information
.PHONY: size
size: build
	@echo "Binary size information:"
	@echo "========================"
	@ls -lh $(BUILD_DIR)/$(PROJECT_NAME)
	@echo ""
	@echo "Section sizes:"
	@size $(BUILD_DIR)/$(PROJECT_NAME) 2>/dev/null || echo "size command not available"
	@echo ""
	@echo "Dependencies:"
	@ldd $(BUILD_DIR)/$(PROJECT_NAME) 2>/dev/null || otool -L $(BUILD_DIR)/$(PROJECT_NAME) 2>/dev/null || echo "Dependency check not available"

# Strip debug symbols
.PHONY: strip
strip: build
	@echo "Stripping debug symbols..."
	@cp $(BUILD_DIR)/$(PROJECT_NAME) $(BUILD_DIR)/$(PROJECT_NAME).debug
	$(STRIP) $(BUILD_DIR)/$(PROJECT_NAME)
	@echo "Stripped binary: $(BUILD_DIR)/$(PROJECT_NAME)"
	@echo "Debug symbols saved: $(BUILD_DIR)/$(PROJECT_NAME).debug"

# Development targets
.PHONY: debug
debug:
	@$(MAKE) BUILD_TYPE=Debug build

.PHONY: release
release:
	@$(MAKE) BUILD_TYPE=Release build

# Format code (if clang-format is available)
.PHONY: format
format:
	@if command -v clang-format >/dev/null 2>&1; then \
		echo "Formatting source code..."; \
		find src include -name "*.c" -o -name "*.h" | xargs clang-format -i; \
		echo "Code formatting completed"; \
	else \
		echo "clang-format not available, skipping code formatting"; \
	fi

# Static analysis (if available)
.PHONY: analyze
analyze:
	@if command -v cppcheck >/dev/null 2>&1; then \
		echo "Running static analysis..."; \
		cppcheck --enable=all --inconclusive --std=c11 src/ include/; \
	else \
		echo "cppcheck not available, skipping static analysis"; \
	fi

# Memory check (if valgrind is available)
.PHONY: memcheck
memcheck: build
	@if command -v valgrind >/dev/null 2>&1; then \
		echo "Running memory check..."; \
		valgrind --leak-check=full --show-leak-kinds=all \
			--track-origins=yes --verbose \
			$(BUILD_DIR)/$(PROJECT_NAME) --help; \
	else \
		echo "valgrind not available, skipping memory check"; \
	fi

# Documentation
.PHONY: docs
docs:
	@echo "Generating documentation..."
	@if command -v doxygen >/dev/null 2>&1; then \
		doxygen Doxyfile 2>/dev/null || echo "Doxyfile not found"; \
	else \
		echo "doxygen not available"; \
	fi

# Quick development cycle
.PHONY: dev
dev: clean debug test

# Production build
.PHONY: prod
prod: clean release strip test package

# Show configuration
.PHONY: config
config:
	@echo "Build Configuration:"
	@echo "==================="
	@echo "Project: $(PROJECT_NAME) v$(VERSION)"
	@echo "Build type: $(BUILD_TYPE)"
	@echo "Build directory: $(BUILD_DIR)"
	@echo "Install prefix: $(INSTALL_PREFIX)"
	@echo "Compiler: $(CC)"
	@echo "CMake: $(CMAKE)"
	@echo "Make: $(MAKE)"
	@echo ""

# Cleanup all build artifacts
.PHONY: distclean
distclean:
	@echo "Cleaning all build artifacts..."
	@rm -rf $(BUILD_DIR) $(BUILD_DIR)-* dist/
	@echo "Deep clean completed"
