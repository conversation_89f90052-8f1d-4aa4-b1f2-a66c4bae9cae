/*
 * Protocol Standard Client - 完全符合TLV协议规范的设备注册客户端
 * 基于提供的协议文档实现
 */

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <time.h>

#include <picoquic.h>
#include <picoquic_utils.h>
#include <picosocks.h>
#include <autoqlog.h>
#include <picoquic_packet_loop.h>
#include "picoquic_bbr.h"

#define SERVER_IP "**************"
#define SERVER_PORT 4433
#define ALPN "hq-29"

// 协议常量
#define MSG_DEVICE_REGISTER_REQ  0x01
#define MSG_DEVICE_REGISTER_ACK  0x02
#define PROTOCOL_VERSION         0x01

typedef struct {
    picoquic_cnx_t* cnx;
    int ready;
    int closed;
    int test_sent;
} protocol_ctx_t;

// 创建标准TLV格式的设备注册请求
void create_standard_device_register_req(uint8_t* buf, int* len, const char* device_id) {
    int device_id_len = strlen(device_id);
    
    // 计算Value部分长度：协议版本(1) + 设备ID长度(1) + 设备ID(N)
    int value_len = 1 + 1 + device_id_len;
    
    printf("📋 构建标准TLV设备注册请求:\n");
    printf("   设备ID: %s (长度: %d)\n", device_id, device_id_len);
    printf("   Value长度: %d\n", value_len);
    
    int offset = 0;
    
    // TLV格式
    // T (1 byte): 消息类型
    buf[offset++] = MSG_DEVICE_REGISTER_REQ;
    printf("   T: 0x%02x (MsgDeviceRegisterReq)\n", MSG_DEVICE_REGISTER_REQ);
    
    // L (2 bytes): Value长度，大端序
    *(uint16_t*)(buf + offset) = htons(value_len);
    offset += 2;
    printf("   L: %d (0x%04x)\n", value_len, value_len);
    
    // V (L bytes): 设备注册请求内容
    printf("   V部分:\n");
    
    // ProtocolVersion (1 byte)
    buf[offset++] = PROTOCOL_VERSION;
    printf("     协议版本: 0x%02x\n", PROTOCOL_VERSION);
    
    // DeviceIDLen (1 byte)
    buf[offset++] = device_id_len;
    printf("     设备ID长度: %d\n", device_id_len);
    
    // DeviceID (N bytes)
    memcpy(buf + offset, device_id, device_id_len);
    offset += device_id_len;
    printf("     设备ID: %s\n", device_id);
    
    *len = offset;
    printf("   总长度: %d 字节\n", *len);
    
    // 打印完整消息的十六进制
    printf("   完整消息: ");
    for (int i = 0; i < *len; i++) {
        printf("%02x ", buf[i]);
        if ((i + 1) % 8 == 0) printf(" ");
    }
    printf("\n");
}

// 解析服务器响应
void parse_server_response(uint8_t* buf, size_t len) {
    printf("📥 解析服务器响应 (%zu 字节): ", len);
    for (size_t i = 0; i < len && i < 20; i++) {
        printf("%02x ", buf[i]);
    }
    printf("\n");
    
    if (len < 3) {
        printf("❌ 响应太短，无法解析TLV格式\n");
        return;
    }
    
    uint8_t msg_type = buf[0];
    uint16_t value_len = ntohs(*(uint16_t*)(buf + 1));
    
    printf("   T: 0x%02x, L: %d\n", msg_type, value_len);
    
    if (msg_type == MSG_DEVICE_REGISTER_ACK) {
        printf("🎉 设备注册成功！服务器确认注册完成\n");
        if (len > 3 && value_len > 0) {
            printf("   响应内容: ");
            for (int i = 0; i < value_len && i < 10; i++) {
                printf("%02x ", buf[3 + i]);
            }
            printf("\n");
        }
    } else {
        printf("❓ 未知响应类型: 0x%02x\n", msg_type);
        if (len > 3 && value_len > 0) {
            printf("   错误内容: ");
            for (int i = 0; i < value_len && i < 10; i++) {
                printf("%02x ", buf[3 + i]);
            }
            printf("\n");
        }
    }
}

static int protocol_callback(picoquic_cnx_t* cnx, uint64_t stream_id, 
    uint8_t* bytes, size_t length, picoquic_call_back_event_t fin_or_event, 
    void* callback_ctx, void* v_stream_ctx)
{
    (void)v_stream_ctx;
    protocol_ctx_t* ctx = (protocol_ctx_t*)callback_ctx;

    switch (fin_or_event) {
    case picoquic_callback_stream_data:
        parse_server_response(bytes, length);
        ctx->closed = 1;
        break;

    case picoquic_callback_ready:
        printf("✅ [QUIC握手完成] 连接就绪，准备发送设备注册请求\n");
        ctx->ready = 1;
        break;

    case picoquic_callback_close:
    case picoquic_callback_application_close:
        printf("🔌 连接关闭\n");
        ctx->closed = 1;
        break;

    default:
        break;
    }
    return 0;
}

int test_device_registration(const char* device_id) {
    printf("\n🎯 标准协议设备注册测试\n");
    printf("=====================================\n");
    printf("设备ID: %s\n", device_id);
    printf("服务器: %s:%d\n", SERVER_IP, SERVER_PORT);
    printf("协议: TLV格式，符合文档规范\n\n");

    picoquic_quic_t* quic = NULL;
    picoquic_cnx_t* cnx = NULL;
    protocol_ctx_t ctx = {0};
    struct sockaddr_storage server_addr;
    int socket_fd = -1;
    uint64_t current_time = picoquic_current_time();

    // 设置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    struct sockaddr_in* addr_in = (struct sockaddr_in*)&server_addr;
    addr_in->sin_family = AF_INET;
    addr_in->sin_port = htons(SERVER_PORT);
    inet_pton(AF_INET, SERVER_IP, &addr_in->sin_addr);

    // 创建QUIC上下文
    quic = picoquic_create(8, NULL, NULL, NULL, ALPN, 
                          NULL, NULL, NULL, NULL, NULL, 
                          current_time, NULL, NULL, NULL, 0);
    
    if (!quic) {
        printf("❌ 创建QUIC上下文失败\n");
        return -1;
    }
    
    picoquic_set_default_congestion_algorithm(quic, picoquic_bbr_algorithm);
    picoquic_set_client_authentication(quic, 0);
    picoquic_set_verify_certificate_callback(quic, NULL, NULL);
    picoquic_set_random_initial(quic, 1);

    // 创建连接
    cnx = picoquic_create_cnx(quic, picoquic_null_connection_id, 
                             picoquic_null_connection_id,
                             (struct sockaddr*)&server_addr, current_time, 
                             0, "relay-server", ALPN, 1);
    
    if (!cnx) {
        printf("❌ 创建QUIC连接失败\n");
        picoquic_free(quic);
        return -1;
    }
    
    picoquic_set_callback(cnx, protocol_callback, &ctx);
    ctx.cnx = cnx;
    picoquic_start_client_cnx(cnx);

    // 创建套接字
    socket_fd = socket(AF_INET, SOCK_DGRAM, 0);
    if (socket_fd < 0) {
        printf("❌ 创建套接字失败\n");
        picoquic_free(quic);
        return -1;
    }
    
    struct sockaddr_in local_addr = {0};
    local_addr.sin_family = AF_INET;
    local_addr.sin_addr.s_addr = INADDR_ANY;
    local_addr.sin_port = 0;
    bind(socket_fd, (struct sockaddr*)&local_addr, sizeof(local_addr));

    printf("🚀 开始QUIC连接和设备注册...\n");

    // 主循环
    int loop_count = 0;
    while (!ctx.closed && loop_count < 300) {  // 增加循环次数
        current_time = picoquic_current_time();
        
        // 发送QUIC包
        struct sockaddr_storage addr_to, addr_from;
        int if_index = 0;
        uint8_t send_buffer[1536];
        size_t send_length = 0;

        int ret = picoquic_prepare_packet(cnx, current_time, send_buffer, 
                                        sizeof(send_buffer), &send_length, 
                                        &addr_to, &addr_from, &if_index);

        if (ret == 0 && send_length > 0) {
            sendto(socket_fd, send_buffer, send_length, 0, 
                  (struct sockaddr*)&addr_to, sizeof(addr_to));
        }

        // 发送设备注册请求
        if (ctx.ready && !ctx.test_sent) {
            uint8_t msg_buffer[256];
            int msg_len = 0;
            
            create_standard_device_register_req(msg_buffer, &msg_len, device_id);
            
            printf("\n📤 发送标准设备注册请求...\n");
            
            uint64_t stream_id = picoquic_get_next_local_stream_id(cnx, 0);
            ret = picoquic_add_to_stream(cnx, stream_id, msg_buffer, msg_len, 1);
            if (ret == 0) {
                printf("✅ 设备注册请求已发送\n");
                ctx.test_sent = 1;
            } else {
                printf("❌ 发送失败: %d\n", ret);
                break;
            }
        }

        // 接收数据包
        fd_set read_fds;
        struct timeval timeout;
        FD_ZERO(&read_fds);
        FD_SET(socket_fd, &read_fds);
        timeout.tv_sec = 0;
        timeout.tv_usec = 10000;  // 10ms

        if (select(socket_fd + 1, &read_fds, NULL, NULL, &timeout) > 0) {
            uint8_t recv_buffer[1536];
            struct sockaddr_storage addr_from;
            socklen_t from_length = sizeof(addr_from);
            
            ssize_t bytes_recv = recvfrom(socket_fd, recv_buffer, 
                                        sizeof(recv_buffer), 0,
                                        (struct sockaddr*)&addr_from, 
                                        &from_length);
            if (bytes_recv > 0) {
                picoquic_incoming_packet(quic, recv_buffer, bytes_recv,
                                       (struct sockaddr*)&addr_from, 
                                       (struct sockaddr*)&local_addr, 
                                       0, 0, current_time);
            }
        }

        loop_count++;
        usleep(10000);  // 10ms
    }

    if (socket_fd >= 0) close(socket_fd);
    if (quic) picoquic_free(quic);
    
    printf("\n📊 测试完成\n");
    printf("循环次数: %d\n", loop_count);
    printf("连接状态: %s\n", ctx.ready ? "已连接" : "未连接");
    printf("消息发送: %s\n", ctx.test_sent ? "已发送" : "未发送");
    
    return 0;
}

int main(int argc, char** argv) {
    printf("🎯 标准协议设备注册客户端\n");
    printf("========================\n");
    printf("基于TLV协议文档实现\n");
    printf("MsgDeviceRegisterReq: 0x01\n");
    printf("MsgDeviceRegisterAck: 0x02\n\n");

    const char* device_id = "picoquic_test_device";
    
    // 解析命令行参数
    if (argc > 1) {
        device_id = argv[1];
    }
    
    printf("使用设备ID: %s\n", device_id);
    printf("如需自定义设备ID，请使用: %s <device_id>\n\n", argv[0]);

    return test_device_registration(device_id);
} 
