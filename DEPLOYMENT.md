# QUIC Stub Client 部署指南

## 项目完成状态

✅ **核心功能已实现**
- QUIC客户端连接管理
- 设备注册机制（基于MAC地址）
- TCP/UDP流量转发
- 协议解析和封装
- 日志系统
- 自动重连机制

✅ **嵌入式优化**
- 裁剪版picoquic库（约300KB二进制大小）
- 内存优化设计
- 支持多架构交叉编译
- 最小化依赖

✅ **构建系统**
- CMake构建配置
- Makefile便捷操作
- 自动化测试脚本
- 打包和分发支持

## 快速部署

### 1. 编译项目

```bash
# 克隆项目
git clone <repository-url>
cd stub

# 快速构建
make

# 或者使用CMake
mkdir build && cd build
cmake ..
make
```

### 2. 基本测试

```bash
# 运行基本测试
make test

# 查看帮助信息
./build/stub --help

# 查看版本信息
./build/stub --version
```

### 3. 连接到Relay服务器

```bash
# 基本连接
./build/stub -s relay.example.com -p 4433

# 启用详细日志
./build/stub -s relay.example.com -p 4433 -l 3

# 后台运行
./build/stub -s relay.example.com -p 4433 -d
```

## 嵌入式设备部署

### ARM架构

```bash
# 交叉编译（需要ARM工具链）
make cross-arm

# 或手动配置
mkdir build-arm && cd build-arm
cmake -DCMAKE_TOOLCHAIN_FILE=../toolchains/arm-linux-gnueabihf.cmake ..
make
```

### MIPS架构

```bash
# 交叉编译（需要MIPS工具链）
make cross-mips

# 或手动配置
mkdir build-mips && cd build-mips
cmake -DCMAKE_TOOLCHAIN_FILE=../toolchains/mipsel-linux-gnu.cmake ..
make
```

### 优化配置

```bash
# 发布版本（最小体积）
make BUILD_TYPE=Release strip

# 查看二进制大小
make size
```

## 配置说明

### 运行时配置

```bash
# 服务器配置
./stub -s <relay_server> -p <port>

# 日志级别
./stub -l 0  # 仅错误
./stub -l 1  # 错误和警告
./stub -l 2  # 信息级别（默认）
./stub -l 3  # 调试信息
./stub -l 4  # 详细跟踪
```

### 编译时配置

在 `include/stub_config.h` 中调整：

```c
// 连接参数
#define MAX_RECONNECT_ATTEMPTS 10
#define RECONNECT_BASE_DELAY_MS 1000
#define CONNECTION_TIMEOUT_MS 60000

// 性能参数
#define MAX_CONCURRENT_STREAMS 100
#define MAX_CONCURRENT_UDP_SESSIONS 50
#define MAX_PACKET_SIZE 1500
```

## 监控和维护

### 运行状态检查

```bash
# 发送统计信号
kill -USR1 <pid>

# 优雅关闭
kill -TERM <pid>

# 强制关闭
kill -KILL <pid>
```

### 日志分析

```bash
# 实时查看日志
./stub -s server -l 3 | tee stub.log

# 过滤错误日志
./stub -s server -l 3 2>&1 | grep ERROR
```

### 性能监控

```bash
# 内存使用
ps aux | grep stub

# 网络连接
netstat -an | grep <port>

# 文件描述符
lsof -p <pid>
```

## 故障排除

### 常见问题

1. **编译失败**
   ```bash
   # 清理重新编译
   make clean && make
   
   # 检查依赖
   cmake --version
   gcc --version
   ```

2. **连接失败**
   ```bash
   # 测试网络连通性
   ping relay.example.com
   telnet relay.example.com 4433
   
   # 检查防火墙
   iptables -L
   ```

3. **设备注册失败**
   ```bash
   # 检查网络接口
   ip link show
   ifconfig
   
   # 启用详细日志
   ./stub -s server -l 4
   ```

### 调试方法

```bash
# 使用GDB调试
gdb ./build/stub
(gdb) run -s server -l 4

# 内存检查（如果有valgrind）
valgrind --leak-check=full ./build/stub --help

# 系统调用跟踪
strace -o trace.log ./build/stub -s server
```

## 生产环境建议

### 系统服务配置

创建systemd服务文件 `/etc/systemd/system/stub.service`：

```ini
[Unit]
Description=QUIC Stub Client
After=network.target

[Service]
Type=simple
User=stub
Group=stub
ExecStart=/usr/local/bin/stub -s relay.example.com -p 4433
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl enable stub
sudo systemctl start stub
sudo systemctl status stub
```

### 安全配置

```bash
# 创建专用用户
sudo useradd -r -s /bin/false stub

# 设置权限
sudo chown root:root /usr/local/bin/stub
sudo chmod 755 /usr/local/bin/stub

# 限制网络权限（可选）
sudo setcap 'cap_net_bind_service=+ep' /usr/local/bin/stub
```

### 日志轮转

创建 `/etc/logrotate.d/stub`：

```
/var/log/stub.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 stub stub
    postrotate
        systemctl reload stub
    endscript
}
```

## 性能调优

### 系统参数

```bash
# 增加文件描述符限制
echo "stub soft nofile 65536" >> /etc/security/limits.conf
echo "stub hard nofile 65536" >> /etc/security/limits.conf

# 网络参数优化
echo "net.core.rmem_max = 16777216" >> /etc/sysctl.conf
echo "net.core.wmem_max = 16777216" >> /etc/sysctl.conf
sysctl -p
```

### 应用参数

```bash
# 高并发配置
./stub -s server -p 4433 -l 1  # 减少日志开销

# 低延迟配置
# 在编译时调整 MAX_PACKET_SIZE 和缓冲区大小
```

## 更新和维护

### 版本更新

```bash
# 备份当前版本
cp /usr/local/bin/stub /usr/local/bin/stub.backup

# 停止服务
sudo systemctl stop stub

# 更新二进制
sudo cp build/stub /usr/local/bin/

# 重启服务
sudo systemctl start stub

# 验证运行
sudo systemctl status stub
```

### 配置备份

```bash
# 备份配置
tar -czf stub-config-$(date +%Y%m%d).tar.gz \
    /etc/systemd/system/stub.service \
    /etc/logrotate.d/stub \
    /usr/local/bin/stub
```

## 技术支持

### 收集诊断信息

```bash
#!/bin/bash
# 诊断信息收集脚本

echo "=== System Information ===" > stub-diag.txt
uname -a >> stub-diag.txt
cat /etc/os-release >> stub-diag.txt

echo "=== Stub Version ===" >> stub-diag.txt
./stub --version >> stub-diag.txt

echo "=== Network Configuration ===" >> stub-diag.txt
ip addr show >> stub-diag.txt
ip route show >> stub-diag.txt

echo "=== Service Status ===" >> stub-diag.txt
systemctl status stub >> stub-diag.txt

echo "=== Recent Logs ===" >> stub-diag.txt
journalctl -u stub --since "1 hour ago" >> stub-diag.txt

echo "Diagnostic information saved to stub-diag.txt"
```

### 联系支持

- 提交Issue到项目仓库
- 包含诊断信息和错误日志
- 描述复现步骤和环境信息
