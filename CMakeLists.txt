cmake_minimum_required(VERSION 3.10)
project(stub C)

# Set C standard
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Compiler flags for embedded systems
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -Os -ffunction-sections -fdata-sections")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -g -DDEBUG")
set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -DNDEBUG")

# Enable link-time optimization for release builds
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -flto")
    # Use appropriate linker flags for different platforms
    if(APPLE)
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-dead_strip")
    else()
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--gc-sections")
    endif()
endif()

# Include directories
include_directories(include)
include_directories(src)
include_directories(src/utils)
include_directories(src/client)
include_directories(src/device)
include_directories(src/protocol)
include_directories(src/handler)
include_directories(third_party/picoquic/picoquic)

# Use full picoquic with proper TLS implementation
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/third_party/picoquic/CMakeLists.txt")
    message(STATUS "Building with full picoquic and picotls support")

    # Configure picoquic build options for embedded systems
    set(DISABLE_DEBUG_PRINTF ON CACHE BOOL "Disable debug printf")
    set(DISABLE_QLOG ON CACHE BOOL "Disable QLOG")
    set(BUILD_TESTING OFF CACHE BOOL "Build tests")

    # Enable automatic picotls fetching
    set(PICOQUIC_FETCH_PTLS ON CACHE BOOL "Fetch PicoTLS during configuration")

    # Enable OpenSSL support for better TLS
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(OPENSSL openssl)
    endif()

    if(OPENSSL_FOUND OR APPLE)
        set(WITH_OPENSSL ON CACHE BOOL "build with OpenSSL")
        set(WITH_MBEDTLS OFF CACHE BOOL "enable MBEDTLS")
    else()
        # Fallback to minicrypto if OpenSSL not available
        set(WITH_OPENSSL OFF CACHE BOOL "build with OpenSSL")
        set(WITH_MBEDTLS OFF CACHE BOOL "enable MBEDTLS")
    endif()

    add_subdirectory(third_party/picoquic)
    set(HAVE_PICOQUIC TRUE)
    set(PICOQUIC_LIBRARIES picoquic-core picoquic-log)
else()
    message(FATAL_ERROR "picoquic source not found in third_party/picoquic")
endif()

# Source files
set(STUB_SOURCES
    src/main.c
    src/client/quic_client.c
    src/device/device_id.c
    src/device/device_register.c
    src/protocol/protocol.c
    src/handler/stream_handler.c
    src/handler/tcp_handler.c
    src/handler/udp_handler.c
    src/utils/logger.c
    src/utils/network_utils.c
)

# Create executable
add_executable(stub ${STUB_SOURCES})

# Configure based on picoquic availability
if(HAVE_PICOQUIC)
    target_compile_definitions(stub PRIVATE HAVE_PICOQUIC=1)
    target_link_libraries(stub
        ${PICOQUIC_LIBRARIES}
        ${CMAKE_THREAD_LIBS_INIT}
    )
else()
    target_compile_definitions(stub PRIVATE HAVE_PICOQUIC=0)
    target_link_libraries(stub
        ${CMAKE_THREAD_LIBS_INIT}
    )
endif()

# Platform-specific libraries
if(UNIX AND NOT APPLE)
    target_link_libraries(stub dl m)
elseif(WIN32)
    target_link_libraries(stub ws2_32 secur32 bcrypt)
endif()

# Install target
install(TARGETS stub DESTINATION bin)
