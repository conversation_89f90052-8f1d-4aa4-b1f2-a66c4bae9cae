cmake_minimum_required(VERSION 3.10)
project(stub C)

# Set C standard
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Compiler flags for embedded systems
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -Os -ffunction-sections -fdata-sections")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -g -DDEBUG")
set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -DNDEBUG")

# Enable link-time optimization for release builds
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -flto")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--gc-sections")
endif()

# Include directories
include_directories(include)
include_directories(src)
include_directories(src/utils)
include_directories(src/client)
include_directories(src/device)
include_directories(src/protocol)
include_directories(src/handler)
include_directories(third_party/picoquic/picoquic)

# Try to build minimal picoquic first
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/third_party/picoquic_minimal/CMakeLists.txt")
    message(STATUS "Building with minimal picoquic support")
    add_subdirectory(third_party/picoquic_minimal)
    set(HAVE_PICOQUIC TRUE)
    set(PICOQUIC_LIBRARIES picoquic-minimal)
elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/third_party/picoquic/CMakeLists.txt")
    # Try full picoquic if minimal not available
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(OPENSSL openssl)
    endif()

    if(OPENSSL_FOUND OR APPLE)
        message(STATUS "Building with full picoquic support")
        add_subdirectory(third_party/picoquic)
        set(HAVE_PICOQUIC TRUE)
        set(PICOQUIC_LIBRARIES picoquic-core picoquic-log)
    else()
        message(WARNING "picoquic dependencies not found, building stub version")
        set(HAVE_PICOQUIC FALSE)
    endif()
else()
    message(WARNING "picoquic source not found, building stub version")
    set(HAVE_PICOQUIC FALSE)
endif()

# Source files
set(STUB_SOURCES
    src/main.c
    src/client/quic_client.c
    src/device/device_id.c
    src/device/device_register.c
    src/protocol/protocol.c
    src/handler/stream_handler.c
    src/handler/tcp_handler.c
    src/handler/udp_handler.c
    src/utils/logger.c
    src/utils/network_utils.c
)

# Create executable
add_executable(stub ${STUB_SOURCES})

# Configure based on picoquic availability
if(HAVE_PICOQUIC)
    target_compile_definitions(stub PRIVATE HAVE_PICOQUIC=1)
    target_link_libraries(stub
        ${PICOQUIC_LIBRARIES}
        ${CMAKE_THREAD_LIBS_INIT}
    )
else()
    target_compile_definitions(stub PRIVATE HAVE_PICOQUIC=0)
    target_link_libraries(stub
        ${CMAKE_THREAD_LIBS_INIT}
    )
endif()

# Platform-specific libraries
if(UNIX AND NOT APPLE)
    target_link_libraries(stub dl m)
elseif(WIN32)
    target_link_libraries(stub ws2_32 secur32 bcrypt)
endif()

# Install target
install(TARGETS stub DESTINATION bin)
